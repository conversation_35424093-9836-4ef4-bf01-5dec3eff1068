import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/payments/payment_link.dart';
import '../../entities/payments/payment_request.dart';
import '../../repositories/payment_repository.dart';

/// Use case for creating a payment link
class CreatePaymentLinkUseCase implements UseCase<PaymentLink, PaymentRequest> {
  final PaymentRepository repository;

  CreatePaymentLinkUseCase(this.repository);

  @override
  Future<Either<Failure, PaymentLink>> call(PaymentRequest params) async {
    // Validate the payment request
    final validationErrors = params.validate();
    if (validationErrors.isNotEmpty) {
      return Left(ValidationFailure(validationErrors.join(', ')));
    }

    // Create the payment link
    return await repository.createPaymentLink(params);
  }
}

/// Parameters for creating a payment link
class CreatePaymentLinkParams {
  final PaymentRequest request;

  CreatePaymentLinkParams({required this.request});
}
