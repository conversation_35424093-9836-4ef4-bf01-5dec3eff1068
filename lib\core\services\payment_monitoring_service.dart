import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/payment_environment_config.dart';
import '../utils/logger.dart';
import '../services/analytics_service.dart';

/// Comprehensive monitoring and observability service for payment operations
class PaymentMonitoringService {
  static final AppLogger _logger = AppLogger();
  static AnalyticsService? _analyticsService;

  /// Initialize the monitoring service
  static void initialize(AnalyticsService analyticsService) {
    _analyticsService = analyticsService;
    _logger.i('Payment monitoring service initialized');
  }

  /// Track payment initiation
  static void trackPaymentInitiated({
    required String paymentId,
    required double amount,
    required String currency,
    required String customerEmail,
    String? invoiceNumber,
    String? customerId,
    Map<String, String>? metadata,
  }) {
    try {
      final eventData = {
        'payment_id': paymentId,
        'amount': amount,
        'currency': currency,
        'customer_email': _hashEmail(customerEmail),
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (invoiceNumber != null) 'invoice_number': invoiceNumber,
        if (customerId != null) 'customer_id': customerId,
        if (metadata != null) ...metadata,
      };

      _logEvent('payment_initiated', eventData);
      _analyticsService?.logEvent(
        name: 'payment_initiated',
        parameters: eventData.cast<String, Object>(),
      );

      _logger.i('Payment initiated: $paymentId, Amount: $amount $currency');
    } catch (e) {
      _logger.e('Error tracking payment initiation: $e');
    }
  }

  /// Track payment link creation
  static void trackPaymentLinkCreated({
    required String paymentLinkId,
    required String paymentLinkUrl,
    required double amount,
    required String currency,
    required String customerEmail,
    required Duration responseTime,
    String? apiEndpoint,
  }) {
    try {
      final eventData = {
        'payment_link_id': paymentLinkId,
        'amount': amount,
        'currency': currency,
        'customer_email': _hashEmail(customerEmail),
        'response_time_ms': responseTime.inMilliseconds,
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (apiEndpoint != null) 'api_endpoint': apiEndpoint,
      };

      _logEvent('payment_link_created', eventData);
      _analyticsService?.logEvent(
        name: 'payment_link_created',
        parameters: eventData.cast<String, Object>(),
      );

      _logger.i(
        'Payment link created: $paymentLinkId, Response time: ${responseTime.inMilliseconds}ms',
      );
    } catch (e) {
      _logger.e('Error tracking payment link creation: $e');
    }
  }

  /// Track payment processing started
  static void trackPaymentProcessingStarted({
    required String paymentLinkId,
    required String paymentUrl,
  }) {
    try {
      final eventData = {
        'payment_link_id': paymentLinkId,
        'payment_url_domain': Uri.parse(paymentUrl).host,
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      _logEvent('payment_processing_started', eventData);
      _analyticsService?.logEvent(
        name: 'payment_processing_started',
        parameters: eventData.cast<String, Object>(),
      );

      _logger.i('Payment processing started: $paymentLinkId');
    } catch (e) {
      _logger.e('Error tracking payment processing start: $e');
    }
  }

  /// Track payment completion
  static void trackPaymentCompleted({
    required String paymentId,
    required String status,
    required double amount,
    required String currency,
    required Duration totalTime,
    String? errorMessage,
    String? paymentMethod,
  }) {
    try {
      final eventData = {
        'payment_id': paymentId,
        'status': status,
        'amount': amount,
        'currency': currency,
        'total_time_ms': totalTime.inMilliseconds,
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (errorMessage != null) 'error_message': errorMessage,
        if (paymentMethod != null) 'payment_method': paymentMethod,
      };

      _logEvent('payment_completed', eventData);
      _analyticsService?.logEvent(
        name: 'payment_completed',
        parameters: eventData.cast<String, Object>(),
      );

      if (status == 'success') {
        _logger.i(
          'Payment completed successfully: $paymentId, Amount: $amount $currency, Time: ${totalTime.inMilliseconds}ms',
        );
      } else {
        _logger.w(
          'Payment failed: $paymentId, Status: $status, Error: $errorMessage',
        );
      }
    } catch (e) {
      _logger.e('Error tracking payment completion: $e');
    }
  }

  /// Track payment errors
  static void trackPaymentError({
    required String errorType,
    required String errorMessage,
    String? paymentId,
    String? paymentLinkId,
    String? apiEndpoint,
    int? statusCode,
    Map<String, dynamic>? additionalData,
  }) {
    try {
      final eventData = {
        'error_type': errorType,
        'error_message': errorMessage,
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (paymentId != null) 'payment_id': paymentId,
        if (paymentLinkId != null) 'payment_link_id': paymentLinkId,
        if (apiEndpoint != null) 'api_endpoint': apiEndpoint,
        if (statusCode != null) 'status_code': statusCode,
        if (additionalData != null) ...additionalData,
      };

      _logEvent('payment_error', eventData);
      _analyticsService?.logEvent(
        name: 'payment_error',
        parameters: eventData.cast<String, Object>(),
      );

      _logger.e('Payment error: $errorType - $errorMessage');
    } catch (e) {
      _logger.e('Error tracking payment error: $e');
    }
  }

  /// Track API performance
  static void trackApiPerformance({
    required String endpoint,
    required Duration responseTime,
    required int statusCode,
    required bool success,
    String? paymentId,
    Map<String, dynamic>? additionalMetrics,
  }) {
    try {
      final eventData = {
        'endpoint': endpoint,
        'response_time_ms': responseTime.inMilliseconds,
        'status_code': statusCode,
        'success': success,
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (paymentId != null) 'payment_id': paymentId,
        if (additionalMetrics != null) ...additionalMetrics,
      };

      _logEvent('payment_api_performance', eventData);
      _analyticsService?.logEvent(
        name: 'payment_api_performance',
        parameters: eventData.cast<String, Object>(),
      );

      if (responseTime.inMilliseconds > 5000) {
        _logger.w(
          'Slow API response: $endpoint, Time: ${responseTime.inMilliseconds}ms',
        );
      }
    } catch (e) {
      _logger.e('Error tracking API performance: $e');
    }
  }

  /// Track security events
  static void trackSecurityEvent({
    required String eventType,
    required String description,
    String? identifier,
    String? ipAddress,
    Map<String, dynamic>? securityData,
  }) {
    try {
      final eventData = {
        'event_type': eventType,
        'description': description,
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (identifier != null) 'identifier': identifier,
        if (ipAddress != null) 'ip_address': ipAddress,
        if (securityData != null) ...securityData,
      };

      _logEvent('payment_security_event', eventData);
      _analyticsService?.logEvent(
        name: 'payment_security_event',
        parameters: eventData.cast<String, Object>(),
      );

      _logger.w('Security event: $eventType - $description');
    } catch (e) {
      _logger.e('Error tracking security event: $e');
    }
  }

  /// Track rate limiting events
  static void trackRateLimitEvent({
    required String identifier,
    required String action,
    required int requestCount,
    required int limit,
    required Duration timeWindow,
  }) {
    try {
      final eventData = {
        'identifier': identifier,
        'action': action,
        'request_count': requestCount,
        'limit': limit,
        'time_window_ms': timeWindow.inMilliseconds,
        'environment': PaymentEnvironmentConfig.environment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      _logEvent('payment_rate_limit', eventData);
      _analyticsService?.logEvent(
        name: 'payment_rate_limit',
        parameters: eventData.cast<String, Object>(),
      );

      if (action == 'blocked') {
        _logger.w(
          'Rate limit exceeded: $identifier, Count: $requestCount/$limit',
        );
      }
    } catch (e) {
      _logger.e('Error tracking rate limit event: $e');
    }
  }

  /// Generate payment analytics summary
  static Map<String, dynamic> generateAnalyticsSummary({
    required DateTime startTime,
    required DateTime endTime,
  }) {
    try {
      // This would typically query a database or analytics service
      // For now, we'll return a basic structure
      return {
        'period': {
          'start': startTime.toIso8601String(),
          'end': endTime.toIso8601String(),
          'duration_hours': endTime.difference(startTime).inHours,
        },
        'environment': PaymentEnvironmentConfig.environment,
        'generated_at': DateTime.now().toIso8601String(),
        'metrics': {
          'total_payments': 0,
          'successful_payments': 0,
          'failed_payments': 0,
          'average_response_time_ms': 0,
          'total_amount': 0.0,
        },
        'note':
            'This is a placeholder. In production, implement actual analytics aggregation.',
      };
    } catch (e) {
      _logger.e('Error generating analytics summary: $e');
      return {'error': 'Failed to generate analytics summary'};
    }
  }

  /// Hash email for privacy compliance
  static String _hashEmail(String email) {
    // Simple hash for privacy - in production use proper hashing
    return email.hashCode.toString();
  }

  /// Log event with structured format
  static void _logEvent(String eventName, Map<String, dynamic> eventData) {
    if (kDebugMode) {
      _logger.d('Payment Event: $eventName');
      _logger.d('Data: ${json.encode(eventData)}');
    }
  }

  /// Check if monitoring is enabled for current environment
  static bool get isMonitoringEnabled {
    return PaymentEnvironmentConfig.securityConfig.logSecurityEvents;
  }
}
