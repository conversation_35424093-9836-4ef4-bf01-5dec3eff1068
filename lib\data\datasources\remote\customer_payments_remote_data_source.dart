import 'dart:convert';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/payments/payments_summary_model.dart';

abstract class CustomerPaymentsRemoteDataSource {
  Future<PaymentsSummaryModel> getCustomerPayments(String customerId);
}

class CustomerPaymentsRemoteDataSourceImpl
    implements CustomerPaymentsRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  CustomerPaymentsRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<PaymentsSummaryModel> getCustomerPayments(String customerId) async {
    try {
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/api/customerPayments/$customerId',
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.data) as Map<String, dynamic>;

        return PaymentsSummaryModel.fromJson(jsonData);
      } else {
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error fetching invoices: ${e.toString()}');
      throw ServerException();
    }
  }
}
