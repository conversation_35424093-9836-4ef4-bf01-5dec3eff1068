import 'package:aquapartner/core/network/api_client.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../models/smr_report_model.dart';

abstract class SMRReportRemoteDataSource {
  Future<List<SMRReportModel>> getSMRReports(String customerId);
  void clearCache();
  void clearCacheForCustomer(String customerId);
}

class SMRReportRemoteDataSourceImpl implements SMRReportRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  // Add a timeout for operations
  final Duration operationTimeout = const Duration(seconds: 15);

  SMRReportRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<List<SMRReportModel>> getSMRReports(String customerId) async {
    try {
      logger.i(
        "Fetching Customer Schemem data from API for customer: $customerId",
      );
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/api/smrReport/$customerId',
      );

      if (response.statusCode == 200) {
        final dynamic responseData = response.data;
        if (responseData.isEmpty) {
          return [];
        }

        // Create dashboard model from response with sync status
        final smrReportModel = SMRReportModel.listFromJson(responseData);

        return smrReportModel;
      }
      return [];
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  void clearCache() {
    // This method is kept for interface compatibility
    // Actual cache clearing should be handled by the local data source
    logger.i("Remote cache clearing requested - no action needed");
  }

  @override
  void clearCacheForCustomer(String customerId) {
    // This method is kept for interface compatibility
    // Actual cache clearing should be handled by the local data source
    logger.i("Remote cache clearing for customer requested - no action needed");
  }
}
