import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import '../utils/input_sanitizer.dart';
import '../utils/logger.dart';
import '../utils/payment_url_validator.dart';
import 'payment_credentials_manager.dart';

/// Enhanced security manager for payment operations
class PaymentSecurityManager {
  static final AppLogger _logger = AppLogger();

  /// Get secret key from secure storage
  static Future<String> get _secretKey async {
    try {
      return await PaymentCredentialsManager.getSecretKey();
    } catch (e) {
      _logger.e('Failed to get payment secret key: $e');
      rethrow;
    }
  }

  /// Validate and sanitize payment request data
  static Future<Map<String, dynamic>> sanitizePaymentRequest(
    Map<String, dynamic> request,
  ) async {
    final sanitized = <String, dynamic>{};

    // Sanitize amount
    if (request['amount'] != null) {
      final amountStr = request['amount'].toString();
      final sanitizedAmount = InputSanitizer.sanitizeAmount(amountStr);
      if (InputSanitizer.validateAmount(sanitizedAmount)) {
        sanitized['amount'] = double.tryParse(sanitizedAmount) ?? 0.0;
      } else {
        throw SecurityException('Invalid amount format');
      }
    }

    // Sanitize description
    if (request['description'] != null) {
      final description = request['description'].toString();
      if (InputSanitizer.containsSuspiciousPatterns(description)) {
        throw SecurityException('Suspicious content detected in description');
      }
      sanitized['description'] = InputSanitizer.sanitizeDescription(
        description,
      );
    }

    // Sanitize customer email
    if (request['customer_email'] != null || request['customerEmail'] != null) {
      final email =
          (request['customer_email'] ?? request['customerEmail']).toString();
      final sanitizedEmail = InputSanitizer.sanitizeEmail(email);
      if (!InputSanitizer.validateEmail(sanitizedEmail)) {
        throw SecurityException('Invalid email format');
      }
      sanitized['customer_email'] = sanitizedEmail;
      sanitized['customerEmail'] = sanitizedEmail;
    }

    // Sanitize optional fields
    _sanitizeOptionalField(
      request,
      sanitized,
      'invoice_number',
      'invoiceNumber',
    );
    _sanitizeOptionalField(request, sanitized, 'customer_id', 'customerId');
    _sanitizeOptionalField(request, sanitized, 'customer_name', 'customerName');
    _sanitizeOptionalField(request, sanitized, 'reference_id', 'referenceId');

    // Sanitize phone number
    if (request['customer_phone'] != null || request['customerPhone'] != null) {
      final phone =
          (request['customer_phone'] ?? request['customerPhone']).toString();
      final sanitizedPhone = InputSanitizer.sanitizePhoneNumber(phone);
      if (sanitizedPhone.isNotEmpty &&
          !InputSanitizer.validatePhoneNumber(sanitizedPhone)) {
        throw SecurityException('Invalid phone number format');
      }
      if (sanitizedPhone.isNotEmpty) {
        sanitized['customer_phone'] = sanitizedPhone;
        sanitized['customerPhone'] = sanitizedPhone;
      }
    }

    // Sanitize metadata
    if (request['meta_data'] != null || request['metadata'] != null) {
      final metadata = request['meta_data'] ?? request['metadata'];
      if (metadata is Map<String, String>) {
        sanitized['meta_data'] = InputSanitizer.sanitizeMetadata(metadata);
        sanitized['metadata'] = InputSanitizer.sanitizeMetadata(metadata);
      } else if (metadata is List) {
        // Handle list format metadata
        final metadataMap = <String, String>{};
        for (final item in metadata) {
          if (item is Map && item['key'] != null && item['value'] != null) {
            final key = InputSanitizer.sanitizeString(item['key'].toString());
            final value = InputSanitizer.sanitizeString(
              item['value'].toString(),
            );
            if (key.isNotEmpty && value.isNotEmpty) {
              metadataMap[key] = value;
            }
          }
        }
        sanitized['meta_data'] =
            metadataMap.entries
                .map((e) => {'key': e.key, 'value': e.value})
                .toList();
        sanitized['metadata'] = metadataMap;
      }
    }

    // Copy safe fields
    final safeFields = [
      'currency',
      'send_email',
      'send_sms',
      'partial_payments',
    ];
    for (final field in safeFields) {
      if (request[field] != null) {
        sanitized[field] = request[field];
      }
    }

    // Add security headers
    sanitized['security_timestamp'] = DateTime.now().millisecondsSinceEpoch;
    sanitized['security_nonce'] = _generateNonce();

    _logger.i('Payment request sanitized successfully');
    return sanitized;
  }

  /// Validate payment URL for security
  static bool validatePaymentUrl(String url) {
    try {
      // Use existing URL validator
      if (!PaymentUrlValidator.isValidPaymentUrl(url)) {
        return false;
      }

      final uri = Uri.parse(url);

      // Additional security checks
      if (_containsMaliciousPatterns(url)) {
        _logger.w('Malicious patterns detected in URL');
        return false;
      }

      // Check for suspicious query parameters
      for (final param in uri.queryParameters.keys) {
        if (InputSanitizer.containsSuspiciousPatterns(param) ||
            InputSanitizer.containsSuspiciousPatterns(
              uri.queryParameters[param] ?? '',
            )) {
          _logger.w('Suspicious query parameters detected');
          return false;
        }
      }

      return true;
    } catch (e) {
      _logger.e('Error validating payment URL: $e');
      return false;
    }
  }

  /// Generate secure callback URL with validation token
  static Future<String> generateSecureCallbackUrl(
    String baseUrl,
    Map<String, String> parameters,
  ) async {
    final sanitizedParams = InputSanitizer.sanitizeMetadata(parameters);

    // Add security token
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final nonce = _generateNonce();
    final token = await _generateSecurityToken(
      sanitizedParams,
      timestamp,
      nonce,
    );

    sanitizedParams['timestamp'] = timestamp;
    sanitizedParams['nonce'] = nonce;
    sanitizedParams['token'] = token;

    final uri = Uri.parse(baseUrl).replace(queryParameters: sanitizedParams);
    return uri.toString();
  }

  /// Validate callback URL security token
  static Future<bool> validateCallbackSecurity(String callbackUrl) async {
    try {
      final uri = Uri.parse(callbackUrl);
      final params = uri.queryParameters;

      final timestamp = params['timestamp'];
      final nonce = params['nonce'];
      final token = params['token'];

      if (timestamp == null || nonce == null || token == null) {
        _logger.w('Missing security parameters in callback URL');
        return false;
      }

      // Check timestamp (should not be older than 1 hour)
      final callbackTime = int.tryParse(timestamp);
      if (callbackTime == null) {
        _logger.w('Invalid timestamp in callback URL');
        return false;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - callbackTime > 3600000) {
        // 1 hour
        _logger.w('Callback URL timestamp expired');
        return false;
      }

      // Validate token
      final paramsForValidation = Map<String, String>.from(params);
      paramsForValidation.remove('token');

      final expectedToken = await _generateSecurityToken(
        paramsForValidation,
        timestamp,
        nonce,
      );
      if (token != expectedToken) {
        _logger.w('Invalid security token in callback URL');
        return false;
      }

      return true;
    } catch (e) {
      _logger.e('Error validating callback security: $e');
      return false;
    }
  }

  /// Check for rate limiting
  static bool checkRateLimit(String identifier) {
    // In a real implementation, this would check against a rate limiting store
    // For now, we'll implement a simple in-memory check
    final now = DateTime.now().millisecondsSinceEpoch;

    // Allow maximum 5 payment requests per minute per identifier
    if (_rateLimitStore.containsKey(identifier)) {
      final requests = _rateLimitStore[identifier]!;
      requests.removeWhere(
        (timestamp) => now - timestamp > 60000,
      ); // Remove old requests

      if (requests.length >= 5) {
        _logger.w('Rate limit exceeded for identifier: $identifier');
        return false;
      }

      requests.add(now);
    } else {
      _rateLimitStore[identifier] = [now];
    }

    return true;
  }

  /// Sanitize optional field
  static void _sanitizeOptionalField(
    Map<String, dynamic> request,
    Map<String, dynamic> sanitized,
    String field1,
    String field2,
  ) {
    final value = request[field1] ?? request[field2];
    if (value != null) {
      final sanitizedValue = InputSanitizer.sanitizeString(value.toString());
      if (sanitizedValue.isNotEmpty) {
        sanitized[field1] = sanitizedValue;
        sanitized[field2] = sanitizedValue;
      }
    }
  }

  /// Check for malicious patterns in URL
  static bool _containsMaliciousPatterns(String url) {
    final maliciousPatterns = [
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'data:', caseSensitive: false),
      RegExp(r'vbscript:', caseSensitive: false),
      RegExp(r'file:', caseSensitive: false),
      RegExp(r'<script', caseSensitive: false),
      RegExp(r'%3Cscript', caseSensitive: false),
      RegExp(r'onload=', caseSensitive: false),
      RegExp(r'onerror=', caseSensitive: false),
    ];

    for (final pattern in maliciousPatterns) {
      if (pattern.hasMatch(url)) {
        return true;
      }
    }

    return false;
  }

  /// Generate security nonce
  static String _generateNonce() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Generate security token
  static Future<String> _generateSecurityToken(
    Map<String, String> params,
    String timestamp,
    String nonce,
  ) async {
    final sortedKeys = params.keys.toList()..sort();
    final paramString = sortedKeys
        .map((key) => '$key=${params[key]}')
        .join('&');
    final secretKey = await _secretKey;
    final dataToSign = '$paramString|$timestamp|$nonce|$secretKey';

    final bytes = utf8.encode(dataToSign);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Simple in-memory rate limiting store (in production, use Redis or similar)
  static final Map<String, List<int>> _rateLimitStore = {};
}

/// Custom exception for security violations
class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}
