import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/entities/payments/payment_link.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/repositories/payment_repository.dart';
import 'package:aquapartner/domain/usecases/payment/create_payment_link_usecase.dart';

import 'create_payment_link_usecase_test.mocks.dart';

@GenerateMocks([PaymentRepository])
void main() {
  late CreatePaymentLinkUseCase useCase;
  late MockPaymentRepository mockRepository;

  setUp(() {
    mockRepository = MockPaymentRepository();
    useCase = CreatePaymentLinkUseCase(mockRepository);
  });

  group('CreatePaymentLinkUseCase', () {
    const tPaymentRequest = PaymentRequest(
      amount: 100.0,
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      currency: 'INR',
    );

    final tPaymentLink = PaymentLink(
      paymentLinkId: 'test_link_id',
      paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
      amount: 100.0,
      currency: 'INR',
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      status: 'active',
      createdTime: DateTime.now(),
      transactionId: 'test_transaction_id',
    );

    test('should return PaymentLink when repository call is successful', () async {
      // arrange
      when(mockRepository.createPaymentLink(any))
          .thenAnswer((_) async => Right(tPaymentLink));

      // act
      final result = await useCase(tPaymentRequest);

      // assert
      expect(result, Right(tPaymentLink));
      verify(mockRepository.createPaymentLink(tPaymentRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return ValidationFailure when payment request is invalid', () async {
      // arrange
      const invalidRequest = PaymentRequest(
        amount: -100.0, // Invalid amount
        description: '',
        customerEmail: 'invalid-email',
        currency: 'INR',
      );

      // act
      final result = await useCase(invalidRequest);

      // assert
      expect(result, isA<Left<Failure, PaymentLink>>());
      result.fold(
        (failure) => expect(failure, isA<ValidationFailure>()),
        (paymentLink) => fail('Should return failure'),
      );
      verifyNever(mockRepository.createPaymentLink(any));
    });

    test('should return ServerFailure when repository call fails', () async {
      // arrange
      when(mockRepository.createPaymentLink(any))
          .thenAnswer((_) async => Left(ServerFailure()));

      // act
      final result = await useCase(tPaymentRequest);

      // assert
      expect(result, Left(ServerFailure()));
      verify(mockRepository.createPaymentLink(tPaymentRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should return NetworkFailure when network error occurs', () async {
      // arrange
      when(mockRepository.createPaymentLink(any))
          .thenAnswer((_) async => Left(NetworkFailure()));

      // act
      final result = await useCase(tPaymentRequest);

      // assert
      expect(result, Left(NetworkFailure()));
      verify(mockRepository.createPaymentLink(tPaymentRequest));
      verifyNoMoreInteractions(mockRepository);
    });

    test('should validate payment request before calling repository', () async {
      // arrange
      const validRequest = PaymentRequest(
        amount: 100.0,
        description: 'Valid description',
        customerEmail: '<EMAIL>',
        currency: 'INR',
      );

      when(mockRepository.createPaymentLink(any))
          .thenAnswer((_) async => Right(tPaymentLink));

      // act
      final result = await useCase(validRequest);

      // assert
      expect(result, Right(tPaymentLink));
      verify(mockRepository.createPaymentLink(validRequest));
    });

    test('should handle empty description validation', () async {
      // arrange
      const requestWithEmptyDescription = PaymentRequest(
        amount: 100.0,
        description: '   ', // Empty description
        customerEmail: '<EMAIL>',
        currency: 'INR',
      );

      // act
      final result = await useCase(requestWithEmptyDescription);

      // assert
      expect(result, isA<Left<Failure, PaymentLink>>());
      result.fold(
        (failure) => expect(failure, isA<ValidationFailure>()),
        (paymentLink) => fail('Should return validation failure'),
      );
    });

    test('should handle invalid email validation', () async {
      // arrange
      const requestWithInvalidEmail = PaymentRequest(
        amount: 100.0,
        description: 'Test payment',
        customerEmail: 'invalid-email-format',
        currency: 'INR',
      );

      // act
      final result = await useCase(requestWithInvalidEmail);

      // assert
      expect(result, isA<Left<Failure, PaymentLink>>());
      result.fold(
        (failure) => expect(failure, isA<ValidationFailure>()),
        (paymentLink) => fail('Should return validation failure'),
      );
    });

    test('should handle partial payments validation', () async {
      // arrange
      const requestWithInvalidPartialPayments = PaymentRequest(
        amount: 100.0,
        description: 'Test payment',
        customerEmail: '<EMAIL>',
        currency: 'INR',
        partialPayments: true,
        minimumPartialAmount: 150.0, // Greater than total amount
      );

      // act
      final result = await useCase(requestWithInvalidPartialPayments);

      // assert
      expect(result, isA<Left<Failure, PaymentLink>>());
      result.fold(
        (failure) => expect(failure, isA<ValidationFailure>()),
        (paymentLink) => fail('Should return validation failure'),
      );
    });
  });
}
