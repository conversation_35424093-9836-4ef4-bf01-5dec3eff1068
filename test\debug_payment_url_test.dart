import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/core/utils/payment_url_validator.dart';

/// Debug test to verify the exact URL that's failing
void main() {
  test('Debug: Test the exact Zoho payment URL that was failing', () {
    // This is the exact URL from your API response
    const actualUrl = 'https://payments.zoho.in/paymentlinks/bcb708a8468d038aed8d34bf9f8db01c1bdc47ccef85c8e77c2e7f1ad67f9bfdd188810a474787a2d6d0448d99dd596c0865a135fe8728766601203e0935ab98';
    
    print('Testing URL: $actualUrl');
    
    // Test the validation
    final isValid = PaymentUrlValidator.isValidPaymentUrl(actualUrl);
    
    print('Validation result: $isValid');
    
    // Parse the URL to see its components
    final uri = Uri.parse(actualUrl);
    print('Scheme: ${uri.scheme}');
    print('Host: ${uri.host}');
    print('Path: ${uri.path}');
    
    // Test individual validation steps
    print('Is HTTPS: ${uri.scheme == 'https'}');
    print('Is allowed domain: ${uri.host == 'payments.zoho.in'}');
    print('Path starts with /paymentlinks/: ${uri.path.startsWith('/paymentlinks/')}');
    
    expect(isValid, isTrue, reason: 'The URL should be valid after our fix');
  });
  
  test('Debug: Test allowed paths list', () {
    // Test that our path is in the allowed list
    const allowedPaths = [
      '/checkout/',
      '/payment/',
      '/paymentlinks/', // This should be present after our fix
      '/api/payment/',
      '/api/payment-page',
    ];
    
    print('Allowed paths: $allowedPaths');
    
    const testPath = '/paymentlinks/';
    final isAllowed = allowedPaths.any((path) => testPath.startsWith(path));
    
    print('Is /paymentlinks/ allowed: $isAllowed');
    
    expect(isAllowed, isTrue, reason: '/paymentlinks/ should be in allowed paths');
  });
}
