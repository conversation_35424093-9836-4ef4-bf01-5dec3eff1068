import 'package:flutter/foundation.dart';
import '../security/payment_credentials_manager.dart';
import '../services/payment_monitoring_service.dart';
import '../services/analytics_service.dart';
import '../utils/logger.dart';
import 'payment_environment_config.dart';

/// Centralized payment system initialization
class PaymentInitialization {
  static final AppLogger _logger = AppLogger();
  static bool _isInitialized = false;

  /// Initialize the complete payment system with security and monitoring
  static Future<void> initialize({
    required AnalyticsService analyticsService,
  }) async {
    if (_isInitialized) {
      _logger.w('Payment system already initialized');
      return;
    }

    try {
      _logger.i('Initializing payment system...');

      // Step 1: Validate environment configuration
      _logger.i('Step 1: Validating environment configuration');
      PaymentEnvironmentConfig.validateConfiguration();
      _logger.i('✓ Environment configuration validated');

      // Step 2: Initialize secure credentials
      _logger.i('Step 2: Initializing payment credentials');
      await PaymentCredentialsManager.initialize();
      _logger.i('✓ Payment credentials initialized');

      // Step 3: Initialize monitoring service
      _logger.i('Step 3: Initializing payment monitoring');
      PaymentMonitoringService.initialize(analyticsService);
      _logger.i('✓ Payment monitoring initialized');

      // Step 4: Validate security requirements for production
      if (PaymentEnvironmentConfig.environment == 'production') {
        await _validateProductionSecurity();
      }

      // Step 5: Log successful initialization
      _logInitializationSuccess();

      _isInitialized = true;
      _logger.i('✅ Payment system initialization completed successfully');
    } catch (e) {
      _logger.e('❌ Payment system initialization failed: $e');

      // Track initialization failure
      PaymentMonitoringService.trackPaymentError(
        errorType: 'InitializationError',
        errorMessage: 'Payment system initialization failed: $e',
      );

      rethrow;
    }
  }

  /// Validate production-specific security requirements
  static Future<void> _validateProductionSecurity() async {
    _logger.i('Validating production security requirements...');

    try {
      // Check SSL pinning is enabled
      final sslConfig = PaymentEnvironmentConfig.sslPinningConfig;
      if (!sslConfig.enabled) {
        throw Exception('SSL pinning must be enabled in production');
      }

      if (sslConfig.certificates.isEmpty) {
        throw Exception('SSL certificates must be configured for production');
      }

      // Check rate limiting is enabled
      final rateLimitConfig = PaymentEnvironmentConfig.rateLimitConfig;
      if (!rateLimitConfig.enabled) {
        throw Exception('Rate limiting must be enabled in production');
      }

      // Check security settings
      final securityConfig = PaymentEnvironmentConfig.securityConfig;
      if (!securityConfig.enableTokenValidation) {
        throw Exception('Token validation must be enabled in production');
      }

      if (!securityConfig.enableRateLimit) {
        throw Exception('Security rate limiting must be enabled in production');
      }

      // Validate credentials are production-ready
      final secretKey = await PaymentCredentialsManager.getSecretKey();
      if (secretKey.contains('dev_') || secretKey.length < 32) {
        throw Exception(
          'Production requires secure, non-development credentials',
        );
      }

      _logger.i('✓ Production security validation passed');
    } catch (e) {
      _logger.e('Production security validation failed: $e');
      rethrow;
    }
  }

  /// Log successful initialization with environment details
  static void _logInitializationSuccess() {
    final environment = PaymentEnvironmentConfig.environment;
    final baseUrl = PaymentEnvironmentConfig.paymentBaseUrl;
    final allowedDomains = PaymentEnvironmentConfig.allowedDomains;
    final securityConfig = PaymentEnvironmentConfig.securityConfig;
    final rateLimitConfig = PaymentEnvironmentConfig.rateLimitConfig;

    _logger.i('Payment System Initialized Successfully');
    _logger.i('Environment: $environment');
    _logger.i('Base URL: $baseUrl');
    _logger.i('Allowed Domains: ${allowedDomains.length}');
    _logger.i('SSL Pinning: ${securityConfig.enableSSLPinning}');
    _logger.i('Rate Limiting: ${rateLimitConfig.enabled}');
    _logger.i('Token Validation: ${securityConfig.enableTokenValidation}');

    // Track successful initialization
    PaymentMonitoringService.trackSecurityEvent(
      eventType: 'payment_system_initialized',
      description: 'Payment system initialized successfully',
      securityData: {
        'environment': environment,
        'ssl_pinning_enabled': securityConfig.enableSSLPinning.toString(),
        'rate_limiting_enabled': rateLimitConfig.enabled.toString(),
        'token_validation_enabled':
            securityConfig.enableTokenValidation.toString(),
      },
    );
  }

  /// Check if payment system is properly initialized
  static bool get isInitialized => _isInitialized;

  /// Get initialization status with details
  static Map<String, dynamic> getInitializationStatus() {
    return {
      'initialized': _isInitialized,
      'environment': PaymentEnvironmentConfig.environment,
      'timestamp': DateTime.now().toIso8601String(),
      'config_valid': _isConfigurationValid(),
    };
  }

  /// Check if configuration is valid without throwing
  static bool _isConfigurationValid() {
    try {
      PaymentEnvironmentConfig.validateConfiguration();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Reset initialization state (for testing)
  @visibleForTesting
  static void reset() {
    _isInitialized = false;
    _logger.i('Payment system initialization state reset');
  }

  /// Perform health check on payment system
  static Future<Map<String, dynamic>> performHealthCheck() async {
    final healthStatus = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'environment': PaymentEnvironmentConfig.environment,
      'overall_status': 'unknown',
      'checks': <String, dynamic>{},
    };

    try {
      // Check initialization
      healthStatus['checks']['initialization'] = {
        'status': _isInitialized ? 'healthy' : 'unhealthy',
        'message':
            _isInitialized ? 'System initialized' : 'System not initialized',
      };

      // Check configuration
      try {
        PaymentEnvironmentConfig.validateConfiguration();
        healthStatus['checks']['configuration'] = {
          'status': 'healthy',
          'message': 'Configuration valid',
        };
      } catch (e) {
        healthStatus['checks']['configuration'] = {
          'status': 'unhealthy',
          'message': 'Configuration invalid: $e',
        };
      }

      // Check credentials
      try {
        await PaymentCredentialsManager.getSecretKey();
        healthStatus['checks']['credentials'] = {
          'status': 'healthy',
          'message': 'Credentials accessible',
        };
      } catch (e) {
        healthStatus['checks']['credentials'] = {
          'status': 'unhealthy',
          'message': 'Credentials not accessible: $e',
        };
      }

      // Determine overall status
      final checks = healthStatus['checks'] as Map<String, dynamic>;
      final allHealthy = checks.values.every(
        (check) => (check as Map<String, dynamic>)['status'] == 'healthy',
      );

      healthStatus['overall_status'] = allHealthy ? 'healthy' : 'unhealthy';
    } catch (e) {
      healthStatus['overall_status'] = 'error';
      healthStatus['error'] = e.toString();
    }

    return healthStatus;
  }
}
