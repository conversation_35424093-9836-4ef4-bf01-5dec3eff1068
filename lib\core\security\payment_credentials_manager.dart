import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import '../config/payment_environment_config.dart';
import '../utils/logger.dart';

/// Secure manager for payment credentials and secrets
class PaymentCredentialsManager {
  static final AppLogger _logger = AppLogger();
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock),
  );

  // Storage keys
  static const String _secretKeyStorageKey = 'payment_secret_key';
  static const String _apiKeyStorageKey = 'payment_api_key';
  static const String _encryptionKeyStorageKey = 'payment_encryption_key';
  static const String _lastRotationStorageKey = 'payment_key_rotation';

  /// Initialize payment credentials from environment and secure storage
  static Future<void> initialize() async {
    try {
      _logger.i('Initializing payment credentials manager');

      // Validate environment configuration first
      PaymentEnvironmentConfig.validateConfiguration();

      // Initialize secrets from environment variables
      await _initializeSecretsFromEnvironment();

      // Validate all required secrets are present
      await _validateRequiredSecrets();

      // Check if key rotation is needed
      await _checkKeyRotation();

      _logger.i('Payment credentials manager initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize payment credentials manager: $e');
      rethrow;
    }
  }

  /// Get payment secret key
  static Future<String> getSecretKey() async {
    try {
      final secretKey = await _secureStorage.read(key: _secretKeyStorageKey);
      if (secretKey == null || secretKey.isEmpty) {
        throw Exception('Payment secret key not found in secure storage');
      }
      return secretKey;
    } catch (e) {
      _logger.e('Failed to retrieve payment secret key: $e');
      rethrow;
    }
  }

  /// Get API key for payment service
  static Future<String> getApiKey() async {
    try {
      final apiKey = await _secureStorage.read(key: _apiKeyStorageKey);
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('Payment API key not found in secure storage');
      }
      return apiKey;
    } catch (e) {
      _logger.e('Failed to retrieve payment API key: $e');
      rethrow;
    }
  }

  /// Get encryption key for sensitive data
  static Future<String> getEncryptionKey() async {
    try {
      final encryptionKey = await _secureStorage.read(
        key: _encryptionKeyStorageKey,
      );
      if (encryptionKey == null || encryptionKey.isEmpty) {
        throw Exception('Payment encryption key not found in secure storage');
      }
      return encryptionKey;
    } catch (e) {
      _logger.e('Failed to retrieve payment encryption key: $e');
      rethrow;
    }
  }

  /// Rotate payment keys (should be called periodically)
  static Future<void> rotateKeys() async {
    try {
      _logger.i('Starting payment key rotation');

      // Generate new keys
      final newSecretKey = _generateSecureKey();
      final newEncryptionKey = _generateSecureKey();

      // Store new keys
      await _secureStorage.write(
        key: _secretKeyStorageKey,
        value: newSecretKey,
      );
      await _secureStorage.write(
        key: _encryptionKeyStorageKey,
        value: newEncryptionKey,
      );

      // Update rotation timestamp
      await _secureStorage.write(
        key: _lastRotationStorageKey,
        value: DateTime.now().millisecondsSinceEpoch.toString(),
      );

      _logger.i('Payment key rotation completed successfully');
    } catch (e) {
      _logger.e('Failed to rotate payment keys: $e');
      rethrow;
    }
  }

  /// Check if key rotation is needed (every 30 days)
  static Future<void> _checkKeyRotation() async {
    try {
      final lastRotationStr = await _secureStorage.read(
        key: _lastRotationStorageKey,
      );
      if (lastRotationStr == null) {
        // First time setup, record current time
        await _secureStorage.write(
          key: _lastRotationStorageKey,
          value: DateTime.now().millisecondsSinceEpoch.toString(),
        );
        return;
      }

      final lastRotation = DateTime.fromMillisecondsSinceEpoch(
        int.parse(lastRotationStr),
      );
      final daysSinceRotation = DateTime.now().difference(lastRotation).inDays;

      if (daysSinceRotation >= 30) {
        _logger.w(
          'Payment keys are due for rotation ($daysSinceRotation days old)',
        );
        // In production, you might want to trigger automatic rotation
        // or send an alert to administrators
      }
    } catch (e) {
      _logger.e('Failed to check key rotation: $e');
    }
  }

  /// Initialize secrets from environment variables
  static Future<void> _initializeSecretsFromEnvironment() async {
    try {
      // Get secrets from environment variables
      final secretKey = const String.fromEnvironment('PAYMENT_SECRET_KEY');
      final apiKey = const String.fromEnvironment('PAYMENT_API_KEY');
      final encryptionKey = const String.fromEnvironment(
        'PAYMENT_ENCRYPTION_KEY',
      );

      // Store in secure storage if provided
      if (secretKey.isNotEmpty) {
        await _secureStorage.write(key: _secretKeyStorageKey, value: secretKey);
        _logger.i('Payment secret key loaded from environment');
      }

      if (apiKey.isNotEmpty) {
        await _secureStorage.write(key: _apiKeyStorageKey, value: apiKey);
        _logger.i('Payment API key loaded from environment');
      }

      if (encryptionKey.isNotEmpty) {
        await _secureStorage.write(
          key: _encryptionKeyStorageKey,
          value: encryptionKey,
        );
        _logger.i('Payment encryption key loaded from environment');
      }

      // Generate missing keys for development environment
      if (PaymentEnvironmentConfig.environment == 'dev') {
        await _generateMissingKeysForDevelopment();
      }
    } catch (e) {
      _logger.e('Failed to initialize secrets from environment: $e');
      rethrow;
    }
  }

  /// Generate missing keys for development environment
  static Future<void> _generateMissingKeysForDevelopment() async {
    try {
      // Check and generate secret key if missing
      final existingSecretKey = await _secureStorage.read(
        key: _secretKeyStorageKey,
      );
      if (existingSecretKey == null || existingSecretKey.isEmpty) {
        final devSecretKey = 'dev_payment_secret_${_generateSecureKey()}';
        await _secureStorage.write(
          key: _secretKeyStorageKey,
          value: devSecretKey,
        );
        _logger.w('Generated development payment secret key');
      }

      // Check and generate API key if missing
      final existingApiKey = await _secureStorage.read(key: _apiKeyStorageKey);
      if (existingApiKey == null || existingApiKey.isEmpty) {
        final devApiKey = 'dev_api_key_${_generateSecureKey()}';
        await _secureStorage.write(key: _apiKeyStorageKey, value: devApiKey);
        _logger.w('Generated development payment API key');
      }

      // Check and generate encryption key if missing
      final existingEncryptionKey = await _secureStorage.read(
        key: _encryptionKeyStorageKey,
      );
      if (existingEncryptionKey == null || existingEncryptionKey.isEmpty) {
        final devEncryptionKey = _generateSecureKey();
        await _secureStorage.write(
          key: _encryptionKeyStorageKey,
          value: devEncryptionKey,
        );
        _logger.w('Generated development payment encryption key');
      }
    } catch (e) {
      _logger.e('Failed to generate development keys: $e');
      rethrow;
    }
  }

  /// Validate that all required secrets are present
  static Future<void> _validateRequiredSecrets() async {
    final environment = PaymentEnvironmentConfig.environment;

    try {
      // Check secret key
      final secretKey = await _secureStorage.read(key: _secretKeyStorageKey);
      if (secretKey == null || secretKey.isEmpty) {
        throw Exception(
          'Payment secret key is required for environment: $environment',
        );
      }

      // Check API key
      final apiKey = await _secureStorage.read(key: _apiKeyStorageKey);
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception(
          'Payment API key is required for environment: $environment',
        );
      }

      // Check encryption key
      final encryptionKey = await _secureStorage.read(
        key: _encryptionKeyStorageKey,
      );
      if (encryptionKey == null || encryptionKey.isEmpty) {
        throw Exception(
          'Payment encryption key is required for environment: $environment',
        );
      }

      // Additional validation for production
      if (environment == 'production') {
        if (secretKey.contains('dev_') || apiKey.contains('dev_')) {
          throw Exception(
            'Development keys detected in production environment',
          );
        }

        if (secretKey.length < 32 || encryptionKey.length < 32) {
          throw Exception(
            'Production keys must be at least 32 characters long',
          );
        }
      }

      _logger.i('All required payment secrets validated successfully');
    } catch (e) {
      _logger.e('Payment secrets validation failed: $e');
      rethrow;
    }
  }

  /// Generate a secure random key
  static String _generateSecureKey() {
    final bytes = List<int>.generate(
      32,
      (i) => DateTime.now().millisecondsSinceEpoch + i,
    );
    return sha256.convert(bytes).toString();
  }

  /// Clear all stored credentials (for logout or security reset)
  static Future<void> clearAllCredentials() async {
    try {
      await _secureStorage.delete(key: _secretKeyStorageKey);
      await _secureStorage.delete(key: _apiKeyStorageKey);
      await _secureStorage.delete(key: _encryptionKeyStorageKey);
      await _secureStorage.delete(key: _lastRotationStorageKey);

      _logger.i('All payment credentials cleared from secure storage');
    } catch (e) {
      _logger.e('Failed to clear payment credentials: $e');
      rethrow;
    }
  }
}
