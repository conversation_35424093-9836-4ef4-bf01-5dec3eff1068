import 'dart:convert';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/payment/payment_link_model.dart';
import '../../models/payment/payment_link_response_model.dart';
import '../../models/payment/payment_request_model.dart';
import '../../models/payment/payment_verification_response_model.dart';

/// Abstract interface for payment remote data source
abstract class PaymentRemoteDataSource {
  /// Create payment link using the recommended API endpoint
  Future<PaymentLinkModel> createPaymentLink(PaymentRequestModel request);

  /// Create payment session using the legacy API endpoint
  Future<PaymentLinkModel> createPaymentSession(PaymentRequestModel request);

  /// Create payment using Flutter-specific API endpoint
  Future<PaymentLinkModel> createFlutterPayment(PaymentRequestModel request);

  /// Get payment status by payment link ID
  Future<PaymentLinkModel> getPaymentStatus(String paymentLinkId);

  /// Verify payment transaction status and invoice updates
  Future<PaymentVerificationResponseModel> verifyTransaction(
    String transactionId,
  );

  /// Force refresh transaction status with invoice updates
  Future<PaymentVerificationResponseModel> forceRefreshTransaction(
    String transactionId, {
    bool updateInvoice = true,
  });
}

/// Implementation of payment remote data source
class PaymentRemoteDataSourceImpl implements PaymentRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  // Timeout for payment operations
  final Duration operationTimeout = const Duration(seconds: 30);

  PaymentRemoteDataSourceImpl({required this.apiClient, required this.logger});

  @override
  Future<PaymentLinkModel> createPaymentLink(
    PaymentRequestModel request,
  ) async {
    try {
      logger.i('Creating payment link with amount: ${request.amount}');

      final response = await apiClient
          .post('/api/zoho/payments/create-link', data: request.toJson())
          .timeout(operationTimeout);

      if (response.statusCode == 201) {
        // Handle both String and Map responses from the API
        Map<String, dynamic> responseData;
        if (response.data is String) {
          logger.d('API returned String response, parsing JSON');
          try {
            responseData = json.decode(response.data as String);
          } catch (jsonError) {
            logger.e('Failed to parse JSON response: $jsonError');
            logger.e('Raw response: ${response.data}');
            throw ServerException();
          }
        } else if (response.data is Map<String, dynamic>) {
          logger.d('API returned Map response');
          responseData = response.data as Map<String, dynamic>;
        } else {
          logger.e('Unexpected response type: ${response.data.runtimeType}');
          logger.e('Raw response: ${response.data}');
          throw ServerException();
        }

        final responseModel = PaymentLinkResponseModel.fromJson(responseData);

        if (responseModel.success && responseModel.data != null) {
          logger.i(
            'Payment link created successfully: ${responseModel.data!.paymentLinkId}',
          );
          return responseModel.data!;
        } else {
          logger.e('Payment link creation failed: ${responseModel.message}');
          throw ServerException();
        }
      } else {
        logger.e(
          'Payment link creation failed with status: ${response.statusCode}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error creating payment link: $e');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentLinkModel> createPaymentSession(
    PaymentRequestModel request,
  ) async {
    try {
      logger.i('Creating payment session with amount: ${request.amount}');

      final response = await apiClient
          .post('/api/zoho/payments/create-session', data: request.toJson())
          .timeout(operationTimeout);

      if (response.statusCode == 200) {
        // Handle both String and Map responses from the API
        Map<String, dynamic> responseData;
        if (response.data is String) {
          logger.d('API returned String response, parsing JSON');
          try {
            responseData = json.decode(response.data as String);
          } catch (jsonError) {
            logger.e('Failed to parse JSON response: $jsonError');
            logger.e('Raw response: ${response.data}');
            throw ServerException();
          }
        } else if (response.data is Map<String, dynamic>) {
          logger.d('API returned Map response');
          responseData = response.data as Map<String, dynamic>;
        } else {
          logger.e('Unexpected response type: ${response.data.runtimeType}');
          logger.e('Raw response: ${response.data}');
          throw ServerException();
        }

        final responseModel = PaymentLinkResponseModel.fromJson(responseData);

        if (responseModel.success && responseModel.data != null) {
          logger.i(
            'Payment session created successfully: ${responseModel.data!.paymentLinkId}',
          );
          return responseModel.data!;
        } else {
          logger.e('Payment session creation failed: ${responseModel.message}');
          throw ServerException();
        }
      } else {
        logger.e(
          'Payment session creation failed with status: ${response.statusCode}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error creating payment session: $e');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentLinkModel> createFlutterPayment(
    PaymentRequestModel request,
  ) async {
    try {
      logger.i('Creating Flutter payment with amount: ${request.amount}');

      final response = await apiClient
          .post('/api/flutter/payment/initiate', data: request.toFlutterJson())
          .timeout(operationTimeout);

      if (response.statusCode == 200) {
        // Handle both String and Map responses from the API
        Map<String, dynamic> responseData;
        if (response.data is String) {
          logger.d('API returned String response, parsing JSON');
          try {
            responseData = json.decode(response.data as String);
          } catch (jsonError) {
            logger.e('Failed to parse JSON response: $jsonError');
            logger.e('Raw response: ${response.data}');
            throw ServerException();
          }
        } else if (response.data is Map<String, dynamic>) {
          logger.d('API returned Map response');
          responseData = response.data as Map<String, dynamic>;
        } else {
          logger.e('Unexpected response type: ${response.data.runtimeType}');
          logger.e('Raw response: ${response.data}');
          throw ServerException();
        }

        if (responseData['success'] == true && responseData['data'] != null) {
          // Convert Flutter API response to standard format
          final data = responseData['data'];
          final standardData = {
            'payment_link_id': data['payment_session_id'] ?? '',
            'payment_link_url': data['webview_url'] ?? '',
            'amount': data['amount'] ?? 0,
            'currency': data['currency'] ?? 'INR',
            'description': data['description'] ?? '',
            'customer_email': request.customerEmail,
            'status': 'active',
            'created_time':
                data['created_time'] ??
                DateTime.now().millisecondsSinceEpoch ~/ 1000,
            'transaction_id': data['transaction_id'] ?? '',
            'invoice_number': data['invoice_number'],
          };

          final paymentLink = PaymentLinkModel.fromJson(standardData);
          logger.i(
            'Flutter payment created successfully: ${paymentLink.paymentLinkId}',
          );
          return paymentLink;
        } else {
          logger.e(
            'Flutter payment creation failed: ${responseData['message']}',
          );
          throw ServerException();
        }
      } else {
        logger.e(
          'Flutter payment creation failed with status: ${response.statusCode}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error creating Flutter payment: $e');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentLinkModel> getPaymentStatus(String paymentLinkId) async {
    try {
      logger.i('Getting payment status for: $paymentLinkId');

      final response = await apiClient
          .get('/api/payment/status/$paymentLinkId')
          .timeout(operationTimeout);

      if (response.statusCode == 200) {
        final responseModel = PaymentLinkResponseModel.fromJson(response.data);

        if (responseModel.success && responseModel.data != null) {
          logger.i('Payment status retrieved successfully');
          return responseModel.data!;
        } else {
          logger.e('Failed to get payment status: ${responseModel.message}');
          throw ServerException();
        }
      } else {
        logger.e(
          'Payment status request failed with status: ${response.statusCode}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error getting payment status: $e');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentVerificationResponseModel> verifyTransaction(
    String transactionId,
  ) async {
    try {
      logger.i('Verifying transaction: $transactionId');

      final response = await apiClient
          .get('/api/verify-transaction/$transactionId')
          .timeout(operationTimeout);

      if (response.statusCode == 200) {
        final responseModel = PaymentVerificationResponseModel.fromJson(
          response.data,
        );

        if (responseModel.success) {
          logger.i(
            'Transaction verification successful: ${responseModel.data?.status}',
          );
          if (responseModel.data?.invoiceStatusUpdated == true) {
            logger.i(
              'Invoice status was automatically updated to: ${responseModel.data?.invoicePaymentStatus}',
            );
          }
          return responseModel;
        } else {
          logger.e('Transaction verification failed: ${responseModel.message}');
          throw ServerException();
        }
      } else if (response.statusCode == 404) {
        logger.e('Transaction not found: $transactionId');
        throw ServerException();
      } else {
        logger.e(
          'Transaction verification failed with status: ${response.statusCode}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error verifying transaction: $e');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentVerificationResponseModel> forceRefreshTransaction(
    String transactionId, {
    bool updateInvoice = true,
  }) async {
    try {
      logger.i(
        'Force refreshing transaction: $transactionId (updateInvoice: $updateInvoice)',
      );

      final requestData = {
        'forceRefresh': true,
        'updateInvoice': updateInvoice,
      };

      final response = await apiClient
          .post('/api/verify-transaction/$transactionId', data: requestData)
          .timeout(operationTimeout);

      if (response.statusCode == 200) {
        final responseModel = PaymentVerificationResponseModel.fromJson(
          response.data,
        );

        if (responseModel.success) {
          logger.i(
            'Transaction force refresh successful: ${responseModel.data?.status}',
          );
          if (responseModel.data?.invoiceStatusUpdated == true) {
            logger.i(
              'Invoice status was updated to: ${responseModel.data?.invoicePaymentStatus}',
            );
          }
          return responseModel;
        } else {
          logger.e(
            'Transaction force refresh failed: ${responseModel.message}',
          );
          throw ServerException();
        }
      } else if (response.statusCode == 404) {
        logger.e('Transaction not found for force refresh: $transactionId');
        throw ServerException();
      } else {
        logger.e(
          'Transaction force refresh failed with status: ${response.statusCode}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error force refreshing transaction: $e');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }
}
