import 'package:equatable/equatable.dart';

/// Domain entity representing a payment request
class PaymentRequest extends Equatable {
  final double amount;
  final String description;
  final String customerEmail;
  final String currency;
  final String? invoiceNumber;
  final String? customerId;
  final String? customerName;
  final String? customerPhone;
  final String? referenceId;
  final Map<String, String>? metadata;
  final bool sendEmail;
  final bool sendSms;
  final DateTime? expiresAt;
  final bool partialPayments;
  final double? minimumPartialAmount;

  const PaymentRequest({
    required this.amount,
    required this.description,
    required this.customerEmail,
    this.currency = 'INR',
    this.invoiceNumber,
    this.customerId,
    this.customerName,
    this.customerPhone,
    this.referenceId,
    this.metadata,
    this.sendEmail = true,
    this.sendSms = false,
    this.expiresAt,
    this.partialPayments = false,
    this.minimumPartialAmount,
  });

  /// Create a copy with updated fields
  PaymentRequest copyWith({
    double? amount,
    String? description,
    String? customerEmail,
    String? currency,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? referenceId,
    Map<String, String>? metadata,
    bool? sendEmail,
    bool? sendSms,
    DateTime? expiresAt,
    bool? partialPayments,
    double? minimumPartialAmount,
  }) {
    return PaymentRequest(
      amount: amount ?? this.amount,
      description: description ?? this.description,
      customerEmail: customerEmail ?? this.customerEmail,
      currency: currency ?? this.currency,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      referenceId: referenceId ?? this.referenceId,
      metadata: metadata ?? this.metadata,
      sendEmail: sendEmail ?? this.sendEmail,
      sendSms: sendSms ?? this.sendSms,
      expiresAt: expiresAt ?? this.expiresAt,
      partialPayments: partialPayments ?? this.partialPayments,
      minimumPartialAmount: minimumPartialAmount ?? this.minimumPartialAmount,
    );
  }

  /// Validate the payment request
  List<String> validate() {
    final errors = <String>[];

    if (amount <= 0) {
      errors.add('Amount must be greater than 0');
    }

    if (description.trim().isEmpty) {
      errors.add('Description is required');
    }

    if (customerEmail.trim().isEmpty) {
      errors.add('Customer email is required');
    }

    // Basic email validation
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(customerEmail)) {
      errors.add('Invalid email format');
    }

    if (partialPayments && (minimumPartialAmount == null || minimumPartialAmount! <= 0)) {
      errors.add('Minimum partial amount must be specified and greater than 0 when partial payments are enabled');
    }

    if (minimumPartialAmount != null && minimumPartialAmount! > amount) {
      errors.add('Minimum partial amount cannot be greater than total amount');
    }

    return errors;
  }

  /// Check if the request is valid
  bool get isValid => validate().isEmpty;

  @override
  List<Object?> get props => [
        amount,
        description,
        customerEmail,
        currency,
        invoiceNumber,
        customerId,
        customerName,
        customerPhone,
        referenceId,
        metadata,
        sendEmail,
        sendSms,
        expiresAt,
        partialPayments,
        minimumPartialAmount,
      ];

  @override
  String toString() {
    return 'PaymentRequest{amount: $amount, description: $description, '
           'customerEmail: $customerEmail, currency: $currency, '
           'invoiceNumber: $invoiceNumber}';
  }
}
