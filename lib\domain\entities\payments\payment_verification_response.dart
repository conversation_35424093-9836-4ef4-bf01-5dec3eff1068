import 'package:equatable/equatable.dart';

/// Domain entity for payment verification response
class PaymentVerificationResponse extends Equatable {
  final bool success;
  final String message;
  final PaymentVerificationData? data;
  final String? error;

  const PaymentVerificationResponse({
    required this.success,
    required this.message,
    this.data,
    this.error,
  });

  @override
  List<Object?> get props => [success, message, data, error];

  /// Check if verification was successful
  bool get isSuccessful => success && data != null;

  /// Check if payment was completed successfully
  bool get isPaymentSuccessful => 
      isSuccessful && data!.isPaymentSuccessful;

  /// Check if invoice status was automatically updated
  bool get wasInvoiceAutoUpdated => 
      isSuccessful && data!.wasInvoiceAutoUpdated;

  /// Get payment status
  String get paymentStatus => data?.status ?? 'unknown';

  /// Get invoice payment status
  String get invoicePaymentStatus => data?.invoicePaymentStatus ?? 'unknown';
}

/// Domain entity for payment verification data
class PaymentVerificationData extends Equatable {
  final String transactionId;
  final String? paymentId;
  final String status;
  final double amount;
  final String currency;
  final String invoiceNumber;
  final String customerEmail;
  final String? paymentCompletedTime;
  final bool invoiceStatusUpdated;
  final String? invoicePaymentStatus;
  final bool? forceRefreshed;
  final bool? invoiceUpdated;
  final bool? webhookProcessed;

  const PaymentVerificationData({
    required this.transactionId,
    this.paymentId,
    required this.status,
    required this.amount,
    required this.currency,
    required this.invoiceNumber,
    required this.customerEmail,
    this.paymentCompletedTime,
    required this.invoiceStatusUpdated,
    this.invoicePaymentStatus,
    this.forceRefreshed,
    this.invoiceUpdated,
    this.webhookProcessed,
  });

  @override
  List<Object?> get props => [
    transactionId,
    paymentId,
    status,
    amount,
    currency,
    invoiceNumber,
    customerEmail,
    paymentCompletedTime,
    invoiceStatusUpdated,
    invoicePaymentStatus,
    forceRefreshed,
    invoiceUpdated,
    webhookProcessed,
  ];

  /// Check if payment was successful
  bool get isPaymentSuccessful {
    return status.toLowerCase() == 'succeeded' || 
           status.toLowerCase() == 'completed' ||
           status.toLowerCase() == 'success';
  }

  /// Check if invoice status was automatically updated
  bool get wasInvoiceAutoUpdated {
    return invoiceStatusUpdated && 
           invoicePaymentStatus?.toLowerCase() == 'paid';
  }

  /// Check if webhook was processed successfully
  bool get wasWebhookProcessed {
    return webhookProcessed ?? false;
  }

  /// Get payment completion time as DateTime
  DateTime? get paymentCompletedDateTime {
    if (paymentCompletedTime == null) return null;
    try {
      return DateTime.parse(paymentCompletedTime!);
    } catch (e) {
      return null;
    }
  }

  /// Check if this is a successful overdue to paid transition
  bool get isOverdueToPaidTransition {
    return isPaymentSuccessful && 
           wasInvoiceAutoUpdated && 
           invoicePaymentStatus?.toLowerCase() == 'paid';
  }
}
