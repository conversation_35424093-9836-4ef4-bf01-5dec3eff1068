# Flutter Payment Integration Guide for AquaPartner API

## Complete Technical Documentation for AI Agent Implementation

### Table of Contents

1. [System Architecture Overview](#system-architecture-overview)
2. [API Endpoint Specifications](#api-endpoint-specifications)
3. [Flutter Integration Architecture](#flutter-integration-architecture)
4. [Production-Ready Flutter Implementation](#production-ready-flutter-implementation)
5. [Data Models and Schema](#data-models-and-schema)
6. [Error Handling Strategies](#error-handling-strategies)
7. [Mobile-Specific Implementation](#mobile-specific-implementation)
8. [Testing and Validation Framework](#testing-and-validation-framework)
9. [Production Deployment](#production-deployment)
10. [Troubleshooting Guide](#troubleshooting-guide)

---

## System Architecture Overview

### AquaPartner Payment System Components

```mermaid
graph TB
    FA[Flutter App] --> API[AquaPartner API Server]
    API --> ZP[Zoho Payment Gateway]
    API --> DB[(MongoDB Database)]
    ZP --> WH[Webhook Handler]
    WH --> IS[Invoice Service]
    IS --> IM[Invoice Model]
    IM --> AU[Audit Service]

    subgraph "Production Environment"
        API
        DB
        WH
        IS
        IM
        AU
    end

    subgraph "External Services"
        ZP
    end

    subgraph "Mobile Application"
        FA
    end
```

### Key System Features

- **Production Server**: `https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net`
- **Automatic Invoice Status Updates**: "Overdue" → "Paid" via `updatePaymentStatus` method
- **Comprehensive Audit Logging**: Full payment and status change tracking
- **Webhook Integration**: Real-time payment status updates from Zoho
- **MongoDB Persistence**: Reliable data storage with transaction support
- **Duplicate Payment Prevention**: Built-in payment ID validation

### Payment Flow States

1. **Payment Initiation**: Flutter app creates payment link via API
2. **WebView Payment**: User completes payment on Zoho gateway
3. **Callback Processing**: Server receives payment completion callback
4. **Webhook Verification**: Zoho sends webhook for payment confirmation
5. **Invoice Status Update**: Automatic status transition using `updatePaymentStatus`
6. **Mobile Notification**: Flutter app detects completion and updates UI

---

## API Endpoint Specifications

### Base Configuration

```yaml
Production Base URL: https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net
API Version: v1
Content-Type: application/json
Authentication: None required for payment endpoints
Rate Limiting: Standard limits apply
```

### 1. Create Payment Link

**Endpoint**: `POST /api/zoho/payments/create-link`

**Request Headers**:

```http
Content-Type: application/json
```

**Request Body**:

```json
{
  "amount": 1500.5,
  "currency": "INR",
  "description": "Invoice Payment - INV-2024-001",
  "invoice_number": "INV-2024-001",
  "customer_id": "CUST-12345",
  "customer_name": "John Doe",
  "customer_email": "<EMAIL>",
  "customer_phone": "+91-9876543210",
  "redirect_url": "https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/payment/callback",
  "reference_id": "REF-2024-001",
  "meta_data": [
    {
      "key": "source",
      "value": "flutter_app"
    },
    {
      "key": "app_version",
      "value": "1.0.0"
    }
  ],
  "send_email": true,
  "send_sms": false
}
```

**Success Response** (201 Created):

```json
{
  "success": true,
  "message": "Payment link created successfully",
  "data": {
    "payment_link_id": "pl_1234567890abcdef",
    "payment_link_url": "https://payments.zoho.in/checkout/pl_1234567890abcdef",
    "amount": 1500.5,
    "currency": "INR",
    "description": "Invoice Payment - INV-2024-001",
    "customer_email": "<EMAIL>",
    "status": "active",
    "created_time": 1640995200,
    "expires_at": 1641600000,
    "transaction_id": "64f1a2b3c4d5e6f7g8h9i0j1"
  }
}
```

**Error Response** (400 Bad Request):

```json
{
  "success": false,
  "error": "Missing required fields",
  "message": "amount, description, and customer_email are required",
  "required_fields": ["amount", "description", "customer_email"]
}
```

**cURL Example**:

```bash
curl -X POST https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/create-link \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1500.50,
    "description": "Invoice Payment - INV-2024-001",
    "customer_email": "<EMAIL>",
    "invoice_number": "INV-2024-001",
    "customer_id": "CUST-12345"
  }'
```

### 2. Payment Status Verification

**Endpoint**: `GET /api/verify-transaction/{transactionId}`

**Success Response** (200 OK):

```json
{
  "success": true,
  "message": "Transaction verified successfully",
  "data": {
    "transaction_id": "64f1a2b3c4d5e6f7g8h9i0j1",
    "payment_id": "pay_1234567890",
    "status": "succeeded",
    "amount": 1500.5,
    "currency": "INR",
    "invoice_number": "INV-2024-001",
    "customer_email": "<EMAIL>",
    "payment_completed_time": "2024-01-15T10:30:00Z",
    "invoice_status_updated": true,
    "invoice_payment_status": "Paid"
  }
}
```

### 3. Payment Callback URLs

**Success Callback**:

```
https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/success?payment_id=pay_123&invoice=INV-2024-001&amount=1500.50&payment_link_id=pl_123&callback_source=zoho_payment_link
```

**Failure Callback**:

```
https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/failed?error=Payment%20declined&invoice=INV-2024-001&payment_link_id=pl_123&callback_source=zoho_payment_link&status=failure
```

---

## Flutter Integration Architecture

### Complete Payment Flow Sequence

```mermaid
sequenceDiagram
    participant FA as Flutter App
    participant PS as Payment Service
    participant API as AquaPartner API
    participant WV as WebView
    participant ZG as Zoho Gateway
    participant WH as Webhook Handler
    participant IS as Invoice Service
    participant DB as Database

    FA->>PS: initiatePayment(invoiceData)
    PS->>API: POST /api/zoho/payments/create-link
    API->>DB: Save PaymentTransaction
    API->>ZG: Create Payment Link
    ZG->>API: Return Payment Link URL
    API->>PS: Payment Link Response
    PS->>FA: Payment Link Data
    FA->>WV: Load Payment URL
    WV->>ZG: User Completes Payment
    ZG->>API: Payment Callback
    API->>DB: Update Transaction Status
    ZG->>WH: Send Webhook Event
    WH->>IS: Process Payment Success
    IS->>DB: Update Invoice Status (Overdue → Paid)
    API->>WV: Redirect to Success Page
    WV->>FA: URL Change Detection
    FA->>PS: verifyPaymentStatus()
    PS->>API: GET /api/verify-transaction
    API->>FA: Payment Confirmation
    FA->>FA: Update UI State
```

### State Management Architecture

```dart
// Payment state management using Provider pattern
enum PaymentState {
  idle,
  creating,
  processing,
  verifying,
  completed,
  failed,
  cancelled
}

class PaymentStateManager extends ChangeNotifier {
  PaymentState _state = PaymentState.idle;
  PaymentResult? _result;
  String? _errorMessage;

  // State getters and methods
}
```

---

## Production-Ready Flutter Implementation

### Dependencies Configuration

Add to `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  webview_flutter: ^4.4.2
  http: ^1.1.0
  shared_preferences: ^2.2.2
  provider: ^6.1.1
  connectivity_plus: ^5.0.2
  url_launcher: ^6.2.1
  flutter_local_notifications: ^16.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  build_runner: ^2.4.7
```

### Platform Configuration

**Android** (`android/app/src/main/AndroidManifest.xml`):

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />

<application>
    <meta-data
        android:name="flutter_deeplinking_enabled"
        android:value="true" />
</application>
```

**iOS** (`ios/Runner/Info.plist`):

```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>aquapartner.payment</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>aquapartner</string>
        </array>
    </dict>
</array>
```

### Core Payment Service Implementation

Create `lib/services/aquapartner_payment_service.dart`:

```dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/payment_models.dart';
import '../utils/app_constants.dart';

class AquaPartnerPaymentService {
  static const String _baseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  static const Duration _defaultTimeout = Duration(seconds: 30);
  static const int _maxRetries = 3;

  final http.Client _httpClient;
  final SharedPreferences _prefs;

  AquaPartnerPaymentService({
    http.Client? httpClient,
    required SharedPreferences prefs,
  }) : _httpClient = httpClient ?? http.Client(),
       _prefs = prefs;

  /// Create payment link for invoice payment
  Future<PaymentLinkResponse> createPaymentLink({
    required double amount,
    required String description,
    required String customerEmail,
    required String invoiceNumber,
    required String customerId,
    String? customerName,
    String? customerPhone,
    String? referenceId,
    List<Map<String, String>>? metadata,
    bool sendEmail = true,
    bool sendSms = false,
  }) async {
    try {
      await _checkConnectivity();

      final requestBody = {
        'amount': amount,
        'currency': 'INR',
        'description': description,
        'customer_email': customerEmail,
        'invoice_number': invoiceNumber,
        'customer_id': customerId,
        'redirect_url': '$_baseUrl/api/payment/callback',
        'send_email': sendEmail,
        'send_sms': sendSms,
        'meta_data': [
          {'key': 'source', 'value': 'flutter_app'},
          {'key': 'app_version', 'value': AppConstants.appVersion},
          {'key': 'platform', 'value': Platform.operatingSystem},
          if (metadata != null) ...metadata,
        ],
      };

      // Add optional fields
      if (customerName != null) requestBody['customer_name'] = customerName;
      if (customerPhone != null) requestBody['customer_phone'] = customerPhone;
      if (referenceId != null) requestBody['reference_id'] = referenceId;

      final response = await _makeRequest(
        'POST',
        '/api/zoho/payments/create-link',
        body: requestBody,
      );

      if (response.statusCode == 201) {
        final responseData = json.decode(response.body);
        final paymentResponse = PaymentLinkResponse.fromJson(responseData);

        // Cache payment data locally
        await _cachePaymentData(paymentResponse);

        return paymentResponse;
      } else {
        throw _handleErrorResponse(response);
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException(
        message: 'Failed to create payment link: ${e.toString()}',
        statusCode: 0,
        errorCode: 'NETWORK_ERROR',
      );
    }
  }

  /// Verify payment transaction status
  Future<PaymentVerificationResponse> verifyPaymentStatus(String transactionId) async {
    try {
      await _checkConnectivity();

      final response = await _makeRequest(
        'GET',
        '/api/verify-transaction/$transactionId',
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final verificationResponse = PaymentVerificationResponse.fromJson(responseData);

        // Update local cache with verification result
        await _updatePaymentCache(transactionId, verificationResponse);

        return verificationResponse;
      } else {
        throw _handleErrorResponse(response);
      }
    } catch (e) {
      // Try to get cached data if network fails
      final cachedData = await _getCachedPaymentStatus(transactionId);
      if (cachedData != null) {
        return cachedData;
      }

      if (e is PaymentException) rethrow;
      throw PaymentException(
        message: 'Failed to verify payment: ${e.toString()}',
        statusCode: 0,
        errorCode: 'VERIFICATION_ERROR',
      );
    }
  }

  /// Poll payment status with exponential backoff
  Future<PaymentVerificationResponse> pollPaymentStatus({
    required String transactionId,
    Duration initialDelay = const Duration(seconds: 2),
    Duration maxDelay = const Duration(seconds: 30),
    int maxAttempts = 10,
  }) async {
    Duration currentDelay = initialDelay;

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        final result = await verifyPaymentStatus(transactionId);

        // Return immediately if payment is completed or failed
        if (result.data.status == 'succeeded' || result.data.status == 'failed') {
          return result;
        }

        // Wait before next attempt
        if (attempt < maxAttempts) {
          await Future.delayed(currentDelay);
          currentDelay = Duration(
            milliseconds: (currentDelay.inMilliseconds * 1.5).round()
          );
          if (currentDelay > maxDelay) currentDelay = maxDelay;
        }
      } catch (e) {
        if (attempt == maxAttempts) rethrow;
        await Future.delayed(currentDelay);
      }
    }

    throw PaymentException(
      message: 'Payment status polling timeout',
      statusCode: 408,
      errorCode: 'POLLING_TIMEOUT',
    );
  }

  /// Make HTTP request with retry logic
  Future<http.Response> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    final url = Uri.parse('$_baseUrl$endpoint');
    final requestHeaders = {
      'Content-Type': 'application/json',
      'User-Agent': 'AquaPartner-Flutter/${AppConstants.appVersion}',
      ...?headers,
    };

    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        http.Response response;

        switch (method.toUpperCase()) {
          case 'GET':
            response = await _httpClient.get(url, headers: requestHeaders)
                .timeout(_defaultTimeout);
            break;
          case 'POST':
            response = await _httpClient.post(
              url,
              headers: requestHeaders,
              body: body != null ? json.encode(body) : null,
            ).timeout(_defaultTimeout);
            break;
          default:
            throw ArgumentError('Unsupported HTTP method: $method');
        }

        return response;
      } catch (e) {
        if (attempt == _maxRetries) rethrow;

        // Exponential backoff
        final delay = Duration(seconds: (2 * attempt));
        await Future.delayed(delay);
      }
    }

    throw Exception('Max retries exceeded');
  }

  /// Check network connectivity
  Future<void> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      throw PaymentException(
        message: 'No internet connection',
        statusCode: 0,
        errorCode: 'NO_CONNECTIVITY',
      );
    }
  }

  /// Handle error responses
  PaymentException _handleErrorResponse(http.Response response) {
    try {
      final errorData = json.decode(response.body);
      return PaymentException(
        message: errorData['message'] ?? 'Unknown error occurred',
        statusCode: response.statusCode,
        errorCode: errorData['error'] ?? 'UNKNOWN_ERROR',
      );
    } catch (e) {
      return PaymentException(
        message: 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        statusCode: response.statusCode,
        errorCode: 'HTTP_ERROR',
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}
```

---

## Data Models and Schema

### Payment Models

Create `lib/models/payment_models.dart`:

```dart
import 'dart:convert';

/// Payment Link Response from AquaPartner API
class PaymentLinkResponse {
  final bool success;
  final String message;
  final PaymentLinkData data;

  PaymentLinkResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentLinkResponse.fromJson(Map<String, dynamic> json) {
    return PaymentLinkResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: PaymentLinkData.fromJson(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.toJson(),
    };
  }
}

/// Payment Link Data matching AquaPartner API response
class PaymentLinkData {
  final String paymentLinkId;
  final String paymentLinkUrl;
  final double amount;
  final String currency;
  final String description;
  final String customerEmail;
  final String status;
  final int createdTime;
  final int? expiresAt;
  final String transactionId;

  PaymentLinkData({
    required this.paymentLinkId,
    required this.paymentLinkUrl,
    required this.amount,
    required this.currency,
    required this.description,
    required this.customerEmail,
    required this.status,
    required this.createdTime,
    this.expiresAt,
    required this.transactionId,
  });

  factory PaymentLinkData.fromJson(Map<String, dynamic> json) {
    return PaymentLinkData(
      paymentLinkId: json['payment_link_id'] ?? '',
      paymentLinkUrl: json['payment_link_url'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'INR',
      description: json['description'] ?? '',
      customerEmail: json['customer_email'] ?? '',
      status: json['status'] ?? '',
      createdTime: json['created_time'] ?? 0,
      expiresAt: json['expires_at'],
      transactionId: json['transaction_id'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'payment_link_id': paymentLinkId,
      'payment_link_url': paymentLinkUrl,
      'amount': amount,
      'currency': currency,
      'description': description,
      'customer_email': customerEmail,
      'status': status,
      'created_time': createdTime,
      'expires_at': expiresAt,
      'transaction_id': transactionId,
    };
  }
}

/// Payment Verification Response
class PaymentVerificationResponse {
  final bool success;
  final String message;
  final PaymentVerificationData data;

  PaymentVerificationResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentVerificationResponse.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: PaymentVerificationData.fromJson(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.toJson(),
    };
  }
}

/// Payment Verification Data matching AquaPartner verification response
class PaymentVerificationData {
  final String transactionId;
  final String? paymentId;
  final String status;
  final double amount;
  final String currency;
  final String invoiceNumber;
  final String customerEmail;
  final String? paymentCompletedTime;
  final bool invoiceStatusUpdated;
  final String? invoicePaymentStatus;

  PaymentVerificationData({
    required this.transactionId,
    this.paymentId,
    required this.status,
    required this.amount,
    required this.currency,
    required this.invoiceNumber,
    required this.customerEmail,
    this.paymentCompletedTime,
    required this.invoiceStatusUpdated,
    this.invoicePaymentStatus,
  });

  factory PaymentVerificationData.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationData(
      transactionId: json['transaction_id'] ?? '',
      paymentId: json['payment_id'],
      status: json['status'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'INR',
      invoiceNumber: json['invoice_number'] ?? '',
      customerEmail: json['customer_email'] ?? '',
      paymentCompletedTime: json['payment_completed_time'],
      invoiceStatusUpdated: json['invoice_status_updated'] ?? false,
      invoicePaymentStatus: json['invoice_payment_status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionId,
      'payment_id': paymentId,
      'status': status,
      'amount': amount,
      'currency': currency,
      'invoice_number': invoiceNumber,
      'customer_email': customerEmail,
      'payment_completed_time': paymentCompletedTime,
      'invoice_status_updated': invoiceStatusUpdated,
      'invoice_payment_status': invoicePaymentStatus,
    };
  }
}

/// Invoice Model matching AquaPartner Invoice schema
class Invoice {
  final String invoiceId;
  final String invoiceNumber;
  final String invoiceStatus;
  final String customerId;
  final double total;
  final double balance;
  final DateTime? invoiceDate;
  final DateTime? dueDate;

  // Payment tracking fields matching AquaPartner schema
  final String paymentStatus; // 'Unpaid', 'Partially Paid', 'Paid', 'Overdue', 'Cancelled'
  final double paidAmount;
  final DateTime? lastPaymentDate;
  final List<PaymentHistoryEntry> paymentHistory;
  final List<StatusHistoryEntry> statusHistory;

  Invoice({
    required this.invoiceId,
    required this.invoiceNumber,
    required this.invoiceStatus,
    required this.customerId,
    required this.total,
    required this.balance,
    this.invoiceDate,
    this.dueDate,
    required this.paymentStatus,
    required this.paidAmount,
    this.lastPaymentDate,
    required this.paymentHistory,
    required this.statusHistory,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      invoiceId: json['invoiceId'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      invoiceStatus: json['invoiceStatus'] ?? '',
      customerId: json['customerId'] ?? '',
      total: (json['total'] ?? 0).toDouble(),
      balance: (json['balance'] ?? 0).toDouble(),
      invoiceDate: json['invoiceDate'] != null ? DateTime.parse(json['invoiceDate']) : null,
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      paymentStatus: json['paymentStatus'] ?? 'Unpaid',
      paidAmount: (json['paidAmount'] ?? 0).toDouble(),
      lastPaymentDate: json['lastPaymentDate'] != null ? DateTime.parse(json['lastPaymentDate']) : null,
      paymentHistory: (json['paymentHistory'] as List<dynamic>?)
          ?.map((e) => PaymentHistoryEntry.fromJson(e))
          .toList() ?? [],
      statusHistory: (json['statusHistory'] as List<dynamic>?)
          ?.map((e) => StatusHistoryEntry.fromJson(e))
          .toList() ?? [],
    );
  }

  /// Check if invoice is overdue
  bool get isOverdue {
    return paymentStatus == 'Overdue' ||
           (dueDate != null && DateTime.now().isAfter(dueDate!) && paymentStatus != 'Paid');
  }

  /// Get days overdue
  int get daysOverdue {
    if (dueDate == null || !isOverdue) return 0;
    return DateTime.now().difference(dueDate!).inDays;
  }

  /// Check if payment is complete
  bool get isFullyPaid {
    return paymentStatus == 'Paid' && paidAmount >= total;
  }

  /// Get remaining balance
  double get remainingBalance {
    return total - paidAmount;
  }
}

/// Payment History Entry matching AquaPartner schema
class PaymentHistoryEntry {
  final String? paymentId;
  final double amount;
  final DateTime paymentDate;
  final String? paymentMethod;
  final String? transactionId;
  final String status;

  PaymentHistoryEntry({
    this.paymentId,
    required this.amount,
    required this.paymentDate,
    this.paymentMethod,
    this.transactionId,
    required this.status,
  });

  factory PaymentHistoryEntry.fromJson(Map<String, dynamic> json) {
    return PaymentHistoryEntry(
      paymentId: json['paymentId'],
      amount: (json['amount'] ?? 0).toDouble(),
      paymentDate: DateTime.parse(json['paymentDate']),
      paymentMethod: json['paymentMethod'],
      transactionId: json['transactionId'],
      status: json['status'] ?? 'succeeded',
    );
  }
}

/// Status History Entry matching AquaPartner schema
class StatusHistoryEntry {
  final String? previousStatus;
  final String newStatus;
  final String? changedBy;
  final String? changeReason;
  final String? paymentReference;
  final DateTime changedAt;

  StatusHistoryEntry({
    this.previousStatus,
    required this.newStatus,
    this.changedBy,
    this.changeReason,
    this.paymentReference,
    required this.changedAt,
  });

  factory StatusHistoryEntry.fromJson(Map<String, dynamic> json) {
    return StatusHistoryEntry(
      previousStatus: json['previousStatus'],
      newStatus: json['newStatus'] ?? '',
      changedBy: json['changedBy'],
      changeReason: json['changeReason'],
      paymentReference: json['paymentReference'],
      changedAt: DateTime.parse(json['changedAt']),
    );
  }
}

/// Payment Exception for error handling
class PaymentException implements Exception {
  final String message;
  final int statusCode;
  final String? errorCode;

  PaymentException({
    required this.message,
    required this.statusCode,
    this.errorCode,
  });

  @override
  String toString() => 'PaymentException: $message (Code: $statusCode)';
}

/// Payment Result from WebView callback detection
enum PaymentStatus { success, failed, cancelled, unknown }

class PaymentResult {
  final PaymentStatus status;
  final String? paymentId;
  final String? invoiceNumber;
  final double? amount;
  final String? paymentLinkId;
  final String? errorMessage;
  final String? callbackSource;

  PaymentResult({
    required this.status,
    this.paymentId,
    this.invoiceNumber,
    this.amount,
    this.paymentLinkId,
    this.errorMessage,
    this.callbackSource,
  });

  factory PaymentResult.fromUrl(String url) {
    final uri = Uri.parse(url);

    if (url.contains('/payment/success')) {
      return PaymentResult(
        status: PaymentStatus.success,
        paymentId: uri.queryParameters['payment_id'],
        invoiceNumber: uri.queryParameters['invoice'],
        amount: double.tryParse(uri.queryParameters['amount'] ?? ''),
        paymentLinkId: uri.queryParameters['payment_link_id'],
        callbackSource: uri.queryParameters['callback_source'],
      );
    } else if (url.contains('/payment/failed')) {
      return PaymentResult(
        status: PaymentStatus.failed,
        invoiceNumber: uri.queryParameters['invoice'],
        paymentLinkId: uri.queryParameters['payment_link_id'],
        errorMessage: uri.queryParameters['error'],
        callbackSource: uri.queryParameters['callback_source'],
      );
    }

    return PaymentResult(status: PaymentStatus.unknown);
  }

  factory PaymentResult.cancelled() {
    return PaymentResult(status: PaymentStatus.cancelled);
  }
}
```

### WebView Implementation

Create `lib/widgets/payment_webview.dart`:

```dart
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/payment_models.dart';
import '../utils/app_constants.dart';

class PaymentWebView extends StatefulWidget {
  final String paymentUrl;
  final Function(PaymentResult) onPaymentComplete;
  final VoidCallback? onCancel;
  final Duration? timeout;

  const PaymentWebView({
    Key? key,
    required this.paymentUrl,
    required this.onPaymentComplete,
    this.onCancel,
    this.timeout = const Duration(minutes: 15),
  }) : super(key: key);

  @override
  State<PaymentWebView> createState() => _PaymentWebViewState();
}

class _PaymentWebViewState extends State<PaymentWebView> {
  late final WebViewController _controller;
  bool _isLoading = true;
  String? _currentUrl;
  bool _paymentCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    _startTimeoutTimer();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setUserAgent('AquaPartner-Flutter/${AppConstants.appVersion}')
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (mounted) {
              setState(() {
                _isLoading = progress < 100;
              });
            }
          },
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _currentUrl = url;
              });
            }
            _handleUrlChange(url);
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _currentUrl = url;
              });
            }
            _handleUrlChange(url);
          },
          onWebResourceError: (WebResourceError error) {
            _handleWebViewError(error);
          },
          onNavigationRequest: (NavigationRequest request) {
            return _handleNavigationRequest(request);
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  void _startTimeoutTimer() {
    if (widget.timeout != null) {
      Future.delayed(widget.timeout!, () {
        if (mounted && !_paymentCompleted) {
          _handleTimeout();
        }
      });
    }
  }

  NavigationDecision _handleNavigationRequest(NavigationRequest request) {
    final url = request.url;

    // Allow navigation to payment gateway and callback URLs
    if (_isAllowedUrl(url)) {
      return NavigationDecision.navigate;
    }

    // Handle external URLs by launching in external browser
    if (url.startsWith('http://') || url.startsWith('https://')) {
      _launchExternalUrl(url);
      return NavigationDecision.prevent;
    }

    return NavigationDecision.navigate;
  }

  bool _isAllowedUrl(String url) {
    final allowedDomains = [
      'payments.zoho.in',
      'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
      'js.zohostatic.com', // Zoho static resources
    ];

    try {
      final uri = Uri.parse(url);
      return allowedDomains.any((domain) => uri.host.contains(domain));
    } catch (e) {
      return false;
    }
  }

  void _handleUrlChange(String url) {
    // Prevent duplicate processing
    if (_paymentCompleted) return;

    // Check for payment completion URLs
    if (_isPaymentCompletionUrl(url)) {
      _paymentCompleted = true;
      final result = PaymentResult.fromUrl(url);

      // Delay to allow page to load before processing
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          widget.onPaymentComplete(result);
        }
      });
    }
  }

  bool _isPaymentCompletionUrl(String url) {
    const baseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
    return url.startsWith('$baseUrl/payment/success') ||
           url.startsWith('$baseUrl/payment/failed');
  }

  void _handleWebViewError(WebResourceError error) {
    if (_paymentCompleted) return;

    debugPrint('WebView Error: ${error.description}');

    // Handle specific error cases
    switch (error.errorCode) {
      case -2: // Network error
        _showErrorDialog(
          'Network Error',
          'Please check your internet connection and try again.',
        );
        break;
      case -6: // Connection refused
        _showErrorDialog(
          'Connection Error',
          'Unable to connect to payment server. Please try again.',
        );
        break;
      default:
        _showErrorDialog(
          'Payment Error',
          'An error occurred while processing your payment. Please try again.',
        );
    }
  }

  void _handleTimeout() {
    if (_paymentCompleted) return;

    _showErrorDialog(
      'Payment Timeout',
      'Payment is taking longer than expected. Please try again or contact support.',
      onOk: () {
        Navigator.of(context).pop();
        widget.onPaymentComplete(PaymentResult(
          status: PaymentStatus.cancelled,
          errorMessage: 'Payment timeout',
        ));
      },
    );
  }

  Future<void> _launchExternalUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  void _showErrorDialog(String title, String message, {VoidCallback? onOk}) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onOk != null) {
                  onOk();
                } else if (widget.onCancel != null) {
                  widget.onCancel!();
                }
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            if (!_paymentCompleted) {
              widget.onPaymentComplete(PaymentResult.cancelled());
            }
            if (widget.onCancel != null) {
              widget.onCancel!();
            }
          },
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading payment page...'),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Clean up WebView resources
    _controller.clearCache();
    _controller.clearLocalStorage();
    super.dispose();
  }
}
```

---

## Error Handling Strategies

### Comprehensive Error Recovery System

Create `lib/services/payment_error_handler.dart`:

```dart
import 'dart:async';
import 'dart:math';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/payment_models.dart';

class PaymentErrorHandler {
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 2);

  final SharedPreferences _prefs;

  PaymentErrorHandler(this._prefs);

  /// Handle payment service errors with retry logic
  Future<T> handleWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = _maxRetries,
    Duration baseDelay = _baseDelay,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    int attempts = 0;
    dynamic lastError;

    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        attempts++;

        // Check if we should retry this error
        if (shouldRetry != null && !shouldRetry(error)) {
          rethrow;
        }

        // Don't retry on final attempt
        if (attempts >= maxRetries) {
          rethrow;
        }

        // Calculate exponential backoff delay
        final delay = Duration(
          milliseconds: (baseDelay.inMilliseconds * pow(2, attempts - 1)).round(),
        );

        await Future.delayed(delay);
      }
    }

    throw lastError;
  }

  /// Determine if error should be retried
  bool shouldRetryError(dynamic error) {
    if (error is PaymentException) {
      switch (error.errorCode) {
        case 'NETWORK_ERROR':
        case 'NO_CONNECTIVITY':
        case 'HTTP_ERROR':
          return true;
        case 'INVALID_AMOUNT':
        case 'MISSING_FIELDS':
          return false; // Don't retry validation errors
        default:
          return error.statusCode >= 500; // Retry server errors
      }
    }

    // Retry network-related exceptions
    return error is SocketException ||
           error is TimeoutException ||
           error is HttpException;
  }

  /// Handle offline scenarios
  Future<T?> handleOfflineOperation<T>(
    String cacheKey,
    Future<T> Function() onlineOperation,
    T Function(Map<String, dynamic>)? fromCacheJson,
  ) async {
    try {
      // Check connectivity
      final connectivityResult = await Connectivity().checkConnectivity();

      if (connectivityResult == ConnectivityResult.none) {
        // Try to get cached data
        return await _getCachedData<T>(cacheKey, fromCacheJson);
      }

      // Perform online operation
      final result = await onlineOperation();

      // Cache successful result
      if (result != null) {
        await _cacheData(cacheKey, result);
      }

      return result;
    } catch (error) {
      // Fallback to cached data on error
      final cachedResult = await _getCachedData<T>(cacheKey, fromCacheJson);
      if (cachedResult != null) {
        return cachedResult;
      }

      rethrow;
    }
  }

  /// Cache data for offline access
  Future<void> _cacheData<T>(String key, T data) async {
    try {
      final cacheEntry = {
        'data': data,
        'cached_at': DateTime.now().millisecondsSinceEpoch,
        'type': T.toString(),
      };

      await _prefs.setString(key, jsonEncode(cacheEntry));
    } catch (e) {
      // Ignore cache errors
      debugPrint('Failed to cache data: $e');
    }
  }

  /// Get cached data
  Future<T?> _getCachedData<T>(
    String key,
    T Function(Map<String, dynamic>)? fromJson,
  ) async {
    try {
      final cachedString = _prefs.getString(key);
      if (cachedString == null) return null;

      final cacheEntry = jsonDecode(cachedString);
      final cachedAt = DateTime.fromMillisecondsSinceEpoch(cacheEntry['cached_at']);

      // Check if cache is still valid (24 hours)
      if (DateTime.now().difference(cachedAt).inHours > 24) {
        await _prefs.remove(key);
        return null;
      }

      if (fromJson != null) {
        return fromJson(cacheEntry['data']);
      }

      return cacheEntry['data'] as T?;
    } catch (e) {
      // Remove corrupted cache
      await _prefs.remove(key);
      return null;
    }
  }

  /// Handle payment verification failures
  Future<PaymentVerificationResponse?> handleVerificationFailure(
    String transactionId,
    PaymentException error,
  ) async {
    // Log error for monitoring
    await _logPaymentError(transactionId, error);

    // Try to get cached verification result
    final cachedResult = await _getCachedData<PaymentVerificationResponse>(
      'verification_$transactionId',
      (json) => PaymentVerificationResponse.fromJson(json),
    );

    if (cachedResult != null) {
      return cachedResult;
    }

    // For critical errors, return null to trigger manual verification
    if (_isCriticalError(error)) {
      return null;
    }

    rethrow;
  }

  /// Check if error is critical and requires manual intervention
  bool _isCriticalError(PaymentException error) {
    return error.statusCode == 404 || // Transaction not found
           error.errorCode == 'INVALID_TRANSACTION' ||
           error.errorCode == 'TRANSACTION_EXPIRED';
  }

  /// Log payment errors for monitoring
  Future<void> _logPaymentError(String transactionId, PaymentException error) async {
    final errorLog = {
      'transaction_id': transactionId,
      'error_code': error.errorCode,
      'error_message': error.message,
      'status_code': error.statusCode,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Store error log locally for later sync
    final errorLogs = _prefs.getStringList('payment_error_logs') ?? [];
    errorLogs.add(jsonEncode(errorLog));

    // Keep only last 100 error logs
    if (errorLogs.length > 100) {
      errorLogs.removeRange(0, errorLogs.length - 100);
    }

    await _prefs.setStringList('payment_error_logs', errorLogs);
  }

  /// Get error logs for debugging
  Future<List<Map<String, dynamic>>> getErrorLogs() async {
    final errorLogs = _prefs.getStringList('payment_error_logs') ?? [];
    return errorLogs.map((log) => jsonDecode(log) as Map<String, dynamic>).toList();
  }

  /// Clear error logs
  Future<void> clearErrorLogs() async {
    await _prefs.remove('payment_error_logs');
  }
}
```

---

## Mobile-Specific Implementation Details

### Background Payment Processing

Create `lib/services/background_payment_service.dart`:

```dart
import 'dart:async';
import 'dart:isolate';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/payment_models.dart';
import 'aquapartner_payment_service.dart';

class BackgroundPaymentService {
  static const String _channelId = 'payment_verification';
  static const String _channelName = 'Payment Verification';
  static const String _channelDescription = 'Notifications for payment status updates';

  final FlutterLocalNotificationsPlugin _notifications;
  final AquaPartnerPaymentService _paymentService;
  final SharedPreferences _prefs;

  Timer? _verificationTimer;

  BackgroundPaymentService({
    required FlutterLocalNotificationsPlugin notifications,
    required AquaPartnerPaymentService paymentService,
    required SharedPreferences prefs,
  }) : _notifications = notifications,
       _paymentService = paymentService,
       _prefs = prefs;

  /// Initialize background payment processing
  Future<void> initialize() async {
    await _initializeNotifications();
    await _startBackgroundVerification();
  }

  /// Initialize local notifications
  Future<void> _initializeNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    const androidChannel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: _channelDescription,
      importance: Importance.high,
    );

    await _notifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  /// Start background payment verification
  Future<void> _startBackgroundVerification() async {
    _verificationTimer?.cancel();

    _verificationTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _verifyPendingPayments(),
    );
  }

  /// Verify pending payments in background
  Future<void> _verifyPendingPayments() async {
    try {
      final pendingPayments = await _getPendingPayments();

      for (final transactionId in pendingPayments) {
        try {
          final result = await _paymentService.verifyPaymentStatus(transactionId);

          if (result.data.status == 'succeeded') {
            await _handlePaymentSuccess(transactionId, result);
          } else if (result.data.status == 'failed') {
            await _handlePaymentFailure(transactionId, result);
          }
        } catch (e) {
          // Continue with other payments if one fails
          debugPrint('Failed to verify payment $transactionId: $e');
        }
      }
    } catch (e) {
      debugPrint('Background verification error: $e');
    }
  }

  /// Get list of pending payment transaction IDs
  Future<List<String>> _getPendingPayments() async {
    final pendingList = _prefs.getStringList('pending_payments') ?? [];
    return pendingList;
  }

  /// Add payment to pending verification list
  Future<void> addPendingPayment(String transactionId) async {
    final pendingList = await _getPendingPayments();
    if (!pendingList.contains(transactionId)) {
      pendingList.add(transactionId);
      await _prefs.setStringList('pending_payments', pendingList);
    }
  }

  /// Remove payment from pending verification list
  Future<void> removePendingPayment(String transactionId) async {
    final pendingList = await _getPendingPayments();
    pendingList.remove(transactionId);
    await _prefs.setStringList('pending_payments', pendingList);
  }

  /// Handle successful payment in background
  Future<void> _handlePaymentSuccess(
    String transactionId,
    PaymentVerificationResponse result,
  ) async {
    await removePendingPayment(transactionId);

    // Show success notification
    await _showNotification(
      id: transactionId.hashCode,
      title: 'Payment Successful',
      body: 'Payment for invoice ${result.data.invoiceNumber} completed successfully',
      payload: 'payment_success:$transactionId',
    );

    // Update local cache
    await _updateLocalPaymentStatus(transactionId, result);
  }

  /// Handle failed payment in background
  Future<void> _handlePaymentFailure(
    String transactionId,
    PaymentVerificationResponse result,
  ) async {
    await removePendingPayment(transactionId);

    // Show failure notification
    await _showNotification(
      id: transactionId.hashCode,
      title: 'Payment Failed',
      body: 'Payment for invoice ${result.data.invoiceNumber} was not successful',
      payload: 'payment_failed:$transactionId',
    );
  }

  /// Show local notification
  Future<void> _showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: _channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      // Handle notification tap based on payload
      if (payload.startsWith('payment_success:')) {
        final transactionId = payload.split(':')[1];
        _navigateToPaymentResult(transactionId, true);
      } else if (payload.startsWith('payment_failed:')) {
        final transactionId = payload.split(':')[1];
        _navigateToPaymentResult(transactionId, false);
      }
    }
  }

  /// Navigate to payment result screen
  void _navigateToPaymentResult(String transactionId, bool success) {
    // Implement navigation logic based on your app's routing
    // This could use a global navigator key or event bus
  }

  /// Update local payment status cache
  Future<void> _updateLocalPaymentStatus(
    String transactionId,
    PaymentVerificationResponse result,
  ) async {
    final cacheKey = 'payment_status_$transactionId';
    final statusData = {
      'transaction_id': transactionId,
      'status': result.data.status,
      'invoice_number': result.data.invoiceNumber,
      'amount': result.data.amount,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    };

    await _prefs.setString(cacheKey, jsonEncode(statusData));
  }

  /// Dispose background service
  void dispose() {
    _verificationTimer?.cancel();
  }
}
```

### State Management with Provider

Create `lib/providers/payment_provider.dart`:

```dart
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/payment_models.dart';
import '../services/aquapartner_payment_service.dart';
import '../services/background_payment_service.dart';
import '../services/payment_error_handler.dart';

enum PaymentState {
  idle,
  creating,
  processing,
  verifying,
  completed,
  failed,
  cancelled,
}

class PaymentProvider extends ChangeNotifier {
  final AquaPartnerPaymentService _paymentService;
  final BackgroundPaymentService _backgroundService;
  final PaymentErrorHandler _errorHandler;

  PaymentState _state = PaymentState.idle;
  PaymentLinkResponse? _currentPaymentLink;
  PaymentVerificationResponse? _verificationResult;
  PaymentException? _lastError;
  String? _currentTransactionId;

  // Getters
  PaymentState get state => _state;
  PaymentLinkResponse? get currentPaymentLink => _currentPaymentLink;
  PaymentVerificationResponse? get verificationResult => _verificationResult;
  PaymentException? get lastError => _lastError;
  String? get currentTransactionId => _currentTransactionId;

  bool get isLoading => _state == PaymentState.creating ||
                       _state == PaymentState.processing ||
                       _state == PaymentState.verifying;

  bool get hasError => _lastError != null;
  bool get isCompleted => _state == PaymentState.completed;
  bool get isFailed => _state == PaymentState.failed;

  PaymentProvider({
    required AquaPartnerPaymentService paymentService,
    required BackgroundPaymentService backgroundService,
    required PaymentErrorHandler errorHandler,
  }) : _paymentService = paymentService,
       _backgroundService = backgroundService,
       _errorHandler = errorHandler;

  /// Create payment link
  Future<void> createPaymentLink({
    required double amount,
    required String description,
    required String customerEmail,
    required String invoiceNumber,
    required String customerId,
    String? customerName,
    String? customerPhone,
    String? referenceId,
    List<Map<String, String>>? metadata,
  }) async {
    try {
      _setState(PaymentState.creating);
      _clearError();

      final response = await _errorHandler.handleWithRetry(
        () => _paymentService.createPaymentLink(
          amount: amount,
          description: description,
          customerEmail: customerEmail,
          invoiceNumber: invoiceNumber,
          customerId: customerId,
          customerName: customerName,
          customerPhone: customerPhone,
          referenceId: referenceId,
          metadata: metadata,
        ),
        shouldRetry: _errorHandler.shouldRetryError,
      );

      _currentPaymentLink = response;
      _currentTransactionId = response.data.transactionId;

      // Add to background verification
      await _backgroundService.addPendingPayment(response.data.transactionId);

      _setState(PaymentState.processing);
    } catch (e) {
      _handleError(e);
    }
  }

  /// Process payment result from WebView
  Future<void> processPaymentResult(PaymentResult result) async {
    try {
      _setState(PaymentState.verifying);

      if (result.status == PaymentStatus.success) {
        // Verify payment status with server
        if (_currentTransactionId != null) {
          await verifyPayment(_currentTransactionId!);
        } else {
          _setState(PaymentState.completed);
        }
      } else if (result.status == PaymentStatus.failed) {
        _setState(PaymentState.failed);
        _lastError = PaymentException(
          message: result.errorMessage ?? 'Payment failed',
          statusCode: 400,
          errorCode: 'PAYMENT_FAILED',
        );
      } else if (result.status == PaymentStatus.cancelled) {
        _setState(PaymentState.cancelled);
      }
    } catch (e) {
      _handleError(e);
    }
  }

  /// Verify payment status
  Future<void> verifyPayment(String transactionId) async {
    try {
      _setState(PaymentState.verifying);

      final result = await _errorHandler.handleWithRetry(
        () => _paymentService.verifyPaymentStatus(transactionId),
        shouldRetry: _errorHandler.shouldRetryError,
      );

      _verificationResult = result;

      if (result.data.status == 'succeeded') {
        await _backgroundService.removePendingPayment(transactionId);
        _setState(PaymentState.completed);
      } else if (result.data.status == 'failed') {
        await _backgroundService.removePendingPayment(transactionId);
        _setState(PaymentState.failed);
      } else {
        // Payment still pending, continue background verification
        _setState(PaymentState.processing);
      }
    } catch (e) {
      // Try to handle verification failure gracefully
      final cachedResult = await _errorHandler.handleVerificationFailure(
        transactionId,
        e is PaymentException ? e : PaymentException(
          message: e.toString(),
          statusCode: 0,
          errorCode: 'VERIFICATION_ERROR',
        ),
      );

      if (cachedResult != null) {
        _verificationResult = cachedResult;
        _setState(PaymentState.completed);
      } else {
        _handleError(e);
      }
    }
  }

  /// Poll payment status with exponential backoff
  Future<void> pollPaymentStatus({
    required String transactionId,
    Duration initialDelay = const Duration(seconds: 2),
    Duration maxDelay = const Duration(seconds: 30),
    int maxAttempts = 10,
  }) async {
    try {
      _setState(PaymentState.verifying);

      final result = await _paymentService.pollPaymentStatus(
        transactionId: transactionId,
        initialDelay: initialDelay,
        maxDelay: maxDelay,
        maxAttempts: maxAttempts,
      );

      _verificationResult = result;

      if (result.data.status == 'succeeded') {
        await _backgroundService.removePendingPayment(transactionId);
        _setState(PaymentState.completed);
      } else {
        await _backgroundService.removePendingPayment(transactionId);
        _setState(PaymentState.failed);
      }
    } catch (e) {
      _handleError(e);
    }
  }

  /// Reset payment state
  void reset() {
    _state = PaymentState.idle;
    _currentPaymentLink = null;
    _verificationResult = null;
    _lastError = null;
    _currentTransactionId = null;
    notifyListeners();
  }

  /// Clear error state
  void clearError() {
    _clearError();
    notifyListeners();
  }

  void _setState(PaymentState newState) {
    _state = newState;
    notifyListeners();
  }

  void _clearError() {
    _lastError = null;
  }

  void _handleError(dynamic error) {
    if (error is PaymentException) {
      _lastError = error;
    } else {
      _lastError = PaymentException(
        message: error.toString(),
        statusCode: 0,
        errorCode: 'UNKNOWN_ERROR',
      );
    }
    _setState(PaymentState.failed);
  }
}
```

---

## Testing and Validation Framework

### Unit Tests

Create `test/services/payment_service_test.dart`:

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:aquapartner_app/services/aquapartner_payment_service.dart';
import 'package:aquapartner_app/models/payment_models.dart';

@GenerateMocks([http.Client, SharedPreferences])
import 'payment_service_test.mocks.dart';

void main() {
  group('AquaPartnerPaymentService', () {
    late AquaPartnerPaymentService paymentService;
    late MockClient mockHttpClient;
    late MockSharedPreferences mockPrefs;

    setUp(() {
      mockHttpClient = MockClient();
      mockPrefs = MockSharedPreferences();
      paymentService = AquaPartnerPaymentService(
        httpClient: mockHttpClient,
        prefs: mockPrefs,
      );
    });

    group('createPaymentLink', () {
      test('should create payment link successfully', () async {
        // Arrange
        const responseBody = '''
        {
          "success": true,
          "message": "Payment link created successfully",
          "data": {
            "payment_link_id": "pl_test123",
            "payment_link_url": "https://payments.zoho.in/checkout/pl_test123",
            "amount": 1500.50,
            "currency": "INR",
            "description": "Test Payment",
            "customer_email": "<EMAIL>",
            "status": "active",
            "created_time": 1640995200,
            "transaction_id": "txn_test123"
          }
        }
        ''';

        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(responseBody, 201));

        when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);

        // Act
        final result = await paymentService.createPaymentLink(
          amount: 1500.50,
          description: 'Test Payment',
          customerEmail: '<EMAIL>',
          invoiceNumber: 'INV-TEST-001',
          customerId: 'CUST-001',
        );

        // Assert
        expect(result.success, true);
        expect(result.data.paymentLinkId, 'pl_test123');
        expect(result.data.amount, 1500.50);
        expect(result.data.transactionId, 'txn_test123');

        verify(mockHttpClient.post(
          Uri.parse('https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/create-link'),
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).called(1);
      });

      test('should handle API error response', () async {
        // Arrange
        const errorResponse = '''
        {
          "success": false,
          "error": "Missing required fields",
          "message": "amount, description, and customer_email are required"
        }
        ''';

        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(errorResponse, 400));

        // Act & Assert
        expect(
          () => paymentService.createPaymentLink(
            amount: 0, // Invalid amount
            description: '',
            customerEmail: '',
            invoiceNumber: 'INV-TEST-001',
            customerId: 'CUST-001',
          ),
          throwsA(isA<PaymentException>()),
        );
      });

      test('should handle network timeout', () async {
        // Arrange
        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenThrow(TimeoutException('Request timeout', Duration(seconds: 30)));

        // Act & Assert
        expect(
          () => paymentService.createPaymentLink(
            amount: 1500.50,
            description: 'Test Payment',
            customerEmail: '<EMAIL>',
            invoiceNumber: 'INV-TEST-001',
            customerId: 'CUST-001',
          ),
          throwsA(isA<PaymentException>()),
        );
      });
    });

    group('verifyPaymentStatus', () {
      test('should verify payment status successfully', () async {
        // Arrange
        const responseBody = '''
        {
          "success": true,
          "message": "Transaction verified successfully",
          "data": {
            "transaction_id": "txn_test123",
            "payment_id": "pay_test123",
            "status": "succeeded",
            "amount": 1500.50,
            "currency": "INR",
            "invoice_number": "INV-TEST-001",
            "customer_email": "<EMAIL>",
            "payment_completed_time": "2024-01-15T10:30:00Z",
            "invoice_status_updated": true,
            "invoice_payment_status": "Paid"
          }
        }
        ''';

        when(mockHttpClient.get(
          any,
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response(responseBody, 200));

        when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);

        // Act
        final result = await paymentService.verifyPaymentStatus('txn_test123');

        // Assert
        expect(result.success, true);
        expect(result.data.status, 'succeeded');
        expect(result.data.invoiceStatusUpdated, true);
        expect(result.data.invoicePaymentStatus, 'Paid');
      });

      test('should return cached data when network fails', () async {
        // Arrange
        when(mockHttpClient.get(
          any,
          headers: anyNamed('headers'),
        )).thenThrow(SocketException('Network error'));

        const cachedData = '''
        {
          "success": true,
          "data": {
            "transaction_id": "txn_test123",
            "status": "succeeded",
            "amount": 1500.50,
            "currency": "INR",
            "invoice_number": "INV-TEST-001",
            "customer_email": "<EMAIL>",
            "invoice_status_updated": true
          }
        }
        ''';

        when(mockPrefs.getString('payment_txn_test123'))
            .thenReturn('{"verification_response": $cachedData}');

        // Act
        final result = await paymentService.verifyPaymentStatus('txn_test123');

        // Assert
        expect(result.success, true);
        expect(result.data.status, 'succeeded');
      });
    });

    group('pollPaymentStatus', () {
      test('should return immediately for completed payment', () async {
        // Arrange
        const responseBody = '''
        {
          "success": true,
          "data": {
            "transaction_id": "txn_test123",
            "status": "succeeded",
            "amount": 1500.50,
            "invoice_status_updated": true
          }
        }
        ''';

        when(mockHttpClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(responseBody, 200));

        when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await paymentService.pollPaymentStatus(
          transactionId: 'txn_test123',
          maxAttempts: 5,
        );
        stopwatch.stop();

        // Assert
        expect(result.data.status, 'succeeded');
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should return quickly
      });

      test('should timeout after max attempts', () async {
        // Arrange
        const responseBody = '''
        {
          "success": true,
          "data": {
            "transaction_id": "txn_test123",
            "status": "pending",
            "amount": 1500.50
          }
        }
        ''';

        when(mockHttpClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(responseBody, 200));

        when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);

        // Act & Assert
        expect(
          () => paymentService.pollPaymentStatus(
            transactionId: 'txn_test123',
            maxAttempts: 2,
            initialDelay: Duration(milliseconds: 100),
          ),
          throwsA(isA<PaymentException>()),
        );
      });
    });
  });
}
```

### Integration Tests

Create `integration_test/payment_flow_test.dart`:

```dart
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:aquapartner_app/main.dart' as app;
import 'package:aquapartner_app/models/payment_models.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Payment Flow Integration Tests', () {
    testWidgets('complete payment flow with success', (WidgetTester tester) async {
      // Launch app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to payment screen
      await tester.tap(find.text('Pay Invoice'));
      await tester.pumpAndSettle();

      // Fill payment form
      await tester.enterText(find.byKey(Key('amount_field')), '1500.50');
      await tester.enterText(find.byKey(Key('description_field')), 'Test Payment');
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('invoice_field')), 'INV-TEST-001');

      // Initiate payment
      await tester.tap(find.text('Create Payment Link'));
      await tester.pumpAndSettle();

      // Wait for payment link creation
      await tester.pump(Duration(seconds: 3));

      // Verify WebView is loaded
      expect(find.byType(WebViewWidget), findsOneWidget);

      // Simulate payment completion by injecting success URL
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/platform_views',
        StandardMethodCodec().encodeMethodCall(
          MethodCall('navigationDelegate', {
            'url': 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/success?payment_id=pay_test&invoice=INV-TEST-001&amount=1500.50'
          }),
        ),
        (data) {},
      );

      await tester.pumpAndSettle();

      // Verify success dialog
      expect(find.text('Payment Successful'), findsOneWidget);
      expect(find.text('INV-TEST-001'), findsOneWidget);
    });

    testWidgets('handle payment failure gracefully', (WidgetTester tester) async {
      // Launch app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to payment screen
      await tester.tap(find.text('Pay Invoice'));
      await tester.pumpAndSettle();

      // Fill payment form with test data
      await tester.enterText(find.byKey(Key('amount_field')), '100.00');
      await tester.enterText(find.byKey(Key('description_field')), 'Test Failed Payment');
      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('invoice_field')), 'INV-FAIL-001');

      // Initiate payment
      await tester.tap(find.text('Create Payment Link'));
      await tester.pumpAndSettle();

      // Simulate payment failure
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/platform_views',
        StandardMethodCodec().encodeMethodCall(
          MethodCall('navigationDelegate', {
            'url': 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/failed?error=Payment%20declined&invoice=INV-FAIL-001'
          }),
        ),
        (data) {},
      );

      await tester.pumpAndSettle();

      // Verify failure dialog
      expect(find.text('Payment Failed'), findsOneWidget);
      expect(find.text('Payment declined'), findsOneWidget);
    });

    testWidgets('handle network connectivity issues', (WidgetTester tester) async {
      // Launch app
      app.main();
      await tester.pumpAndSettle();

      // Simulate network disconnection
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'dev.fluttercommunity.plus/connectivity',
        StandardMethodCodec().encodeMethodCall(
          MethodCall('check', null),
        ),
        (data) {
          return StandardMethodCodec().encodeSuccessEnvelope('none');
        },
      );

      // Navigate to payment screen
      await tester.tap(find.text('Pay Invoice'));
      await tester.pumpAndSettle();

      // Try to create payment
      await tester.tap(find.text('Create Payment Link'));
      await tester.pumpAndSettle();

      // Verify network error handling
      expect(find.text('No internet connection'), findsOneWidget);
    });
  });
}
```

### Mock Server for Development

Create `test/mock_server.dart`:

```dart
import 'dart:convert';
import 'dart:io';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_router/shelf_router.dart';

class MockAquaPartnerServer {
  static const int port = 8080;
  static const String baseUrl = 'http://localhost:$port';

  late HttpServer _server;

  Future<void> start() async {
    final router = Router();

    // Mock payment link creation
    router.post('/api/zoho/payments/create-link', _createPaymentLink);

    // Mock payment verification
    router.get('/api/verify-transaction/<transactionId>', _verifyTransaction);

    // Mock success page
    router.get('/payment/success', _paymentSuccess);

    // Mock failure page
    router.get('/payment/failed', _paymentFailed);

    final handler = Pipeline()
        .addMiddleware(logRequests())
        .addMiddleware(_corsMiddleware)
        .addHandler(router);

    _server = await serve(handler, 'localhost', port);
    print('Mock server running on $baseUrl');
  }

  Future<void> stop() async {
    await _server.close();
  }

  Response _createPaymentLink(Request request) async {
    final body = await request.readAsString();
    final data = jsonDecode(body);

    // Simulate validation
    if (data['amount'] == null || data['amount'] <= 0) {
      return Response(400,
        body: jsonEncode({
          'success': false,
          'error': 'Invalid amount',
          'message': 'Amount must be greater than 0'
        }),
        headers: {'Content-Type': 'application/json'}
      );
    }

    // Simulate successful response
    final transactionId = 'mock_txn_${DateTime.now().millisecondsSinceEpoch}';
    final paymentLinkId = 'mock_pl_${DateTime.now().millisecondsSinceEpoch}';

    return Response.ok(
      jsonEncode({
        'success': true,
        'message': 'Payment link created successfully',
        'data': {
          'payment_link_id': paymentLinkId,
          'payment_link_url': '$baseUrl/mock-payment/$paymentLinkId',
          'amount': data['amount'],
          'currency': 'INR',
          'description': data['description'],
          'customer_email': data['customer_email'],
          'status': 'active',
          'created_time': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          'transaction_id': transactionId,
        }
      }),
      headers: {'Content-Type': 'application/json'}
    );
  }

  Response _verifyTransaction(Request request) {
    final transactionId = request.params['transactionId'];

    // Simulate different scenarios based on transaction ID
    if (transactionId?.contains('fail') == true) {
      return Response.ok(
        jsonEncode({
          'success': true,
          'data': {
            'transaction_id': transactionId,
            'status': 'failed',
            'amount': 100.0,
            'currency': 'INR',
            'invoice_number': 'INV-FAIL-001',
            'customer_email': '<EMAIL>',
            'invoice_status_updated': false,
          }
        }),
        headers: {'Content-Type': 'application/json'}
      );
    }

    return Response.ok(
      jsonEncode({
        'success': true,
        'data': {
          'transaction_id': transactionId,
          'payment_id': 'mock_pay_${DateTime.now().millisecondsSinceEpoch}',
          'status': 'succeeded',
          'amount': 1500.50,
          'currency': 'INR',
          'invoice_number': 'INV-TEST-001',
          'customer_email': '<EMAIL>',
          'payment_completed_time': DateTime.now().toIso8601String(),
          'invoice_status_updated': true,
          'invoice_payment_status': 'Paid',
        }
      }),
      headers: {'Content-Type': 'application/json'}
    );
  }

  Response _paymentSuccess(Request request) {
    return Response.ok('''
      <!DOCTYPE html>
      <html>
      <head><title>Payment Successful</title></head>
      <body>
        <h1>Payment Successful</h1>
        <p>Your payment has been processed successfully.</p>
      </body>
      </html>
    ''', headers: {'Content-Type': 'text/html'});
  }

  Response _paymentFailed(Request request) {
    return Response.ok('''
      <!DOCTYPE html>
      <html>
      <head><title>Payment Failed</title></head>
      <body>
        <h1>Payment Failed</h1>
        <p>Your payment could not be processed.</p>
      </body>
      </html>
    ''', headers: {'Content-Type': 'text/html'});
  }

  Middleware get _corsMiddleware => (Handler handler) {
    return (Request request) async {
      final response = await handler(request);
      return response.change(headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      });
    };
  };
}
```

---

## Production Deployment

### Environment Configuration

Create `lib/utils/app_constants.dart`:

```dart
class AppConstants {
  static const String appVersion = '1.0.0';
  static const String appName = 'AquaPartner';

  // Production API Configuration
  static const String productionBaseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  static const String stagingBaseUrl = 'https://staging-aquapartner.azurewebsites.net';
  static const String developmentBaseUrl = 'http://localhost:8080';

  // Get base URL based on build mode
  static String get baseUrl {
    const bool isProduction = bool.fromEnvironment('dart.vm.product');
    const bool isStaging = bool.fromEnvironment('STAGING');

    if (isProduction) {
      return productionBaseUrl;
    } else if (isStaging) {
      return stagingBaseUrl;
    } else {
      return developmentBaseUrl;
    }
  }

  // Payment Configuration
  static const Duration paymentTimeout = Duration(minutes: 15);
  static const Duration verificationTimeout = Duration(minutes: 5);
  static const int maxRetryAttempts = 3;
  static const Duration retryBaseDelay = Duration(seconds: 2);

  // Cache Configuration
  static const Duration cacheExpiry = Duration(hours: 24);
  static const int maxErrorLogs = 100;
  static const int maxCachedPayments = 50;

  // Notification Configuration
  static const String notificationChannelId = 'payment_notifications';
  static const String notificationChannelName = 'Payment Updates';
  static const String notificationChannelDescription = 'Notifications for payment status updates';
}
```

### Build Configuration

Create `android/app/build.gradle` configuration:

```gradle
android {
    compileSdkVersion 34

    defaultConfig {
        applicationId "com.aquapartner.app"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // Enable multidex for large apps
        multiDexEnabled true
    }

    buildTypes {
        debug {
            applicationIdSuffix ".debug"
            debuggable true
            buildConfigField "String", "API_BASE_URL", '"http://localhost:8080"'
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
        }

        staging {
            applicationIdSuffix ".staging"
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "API_BASE_URL", '"https://staging-aquapartner.azurewebsites.net"'
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
        }

        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "API_BASE_URL", '"https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net"'
            buildConfigField "boolean", "ENABLE_LOGGING", "false"

            signingConfig signingConfigs.release
        }
    }
}
```

### iOS Configuration

Update `ios/Runner/Info.plist` for production:

```xml
<key>CFBundleDisplayName</key>
<string>AquaPartner</string>

<key>NSAppTransportSecurity</key>
<dict>
    <key>NSExceptionDomains</key>
    <dict>
        <key>aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net</key>
        <dict>
            <key>NSExceptionRequiresForwardSecrecy</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
            <key>NSIncludesSubdomains</key>
            <true/>
        </dict>
        <key>payments.zoho.in</key>
        <dict>
            <key>NSExceptionRequiresForwardSecrecy</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
        </dict>
    </dict>
</dict>

<key>UIBackgroundModes</key>
<array>
    <string>background-processing</string>
    <string>background-fetch</string>
</array>
```

### Performance Optimization

Create `lib/utils/performance_monitor.dart`:

```dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PerformanceMonitor {
  static final Map<String, Stopwatch> _timers = {};
  static final List<PerformanceMetric> _metrics = [];

  /// Start timing an operation
  static void startTimer(String operationName) {
    _timers[operationName] = Stopwatch()..start();
  }

  /// Stop timing and record metric
  static void stopTimer(String operationName, {Map<String, dynamic>? metadata}) {
    final timer = _timers[operationName];
    if (timer != null) {
      timer.stop();

      final metric = PerformanceMetric(
        operationName: operationName,
        duration: timer.elapsed,
        timestamp: DateTime.now(),
        metadata: metadata,
      );

      _metrics.add(metric);
      _timers.remove(operationName);

      // Log slow operations in debug mode
      if (kDebugMode && timer.elapsedMilliseconds > 1000) {
        debugPrint('SLOW OPERATION: $operationName took ${timer.elapsedMilliseconds}ms');
      }

      // Keep only last 100 metrics
      if (_metrics.length > 100) {
        _metrics.removeRange(0, _metrics.length - 100);
      }
    }
  }

  /// Get performance metrics
  static List<PerformanceMetric> getMetrics() => List.unmodifiable(_metrics);

  /// Get average duration for operation
  static Duration? getAverageDuration(String operationName) {
    final operationMetrics = _metrics.where((m) => m.operationName == operationName);
    if (operationMetrics.isEmpty) return null;

    final totalMs = operationMetrics.fold<int>(
      0,
      (sum, metric) => sum + metric.duration.inMilliseconds,
    );

    return Duration(milliseconds: totalMs ~/ operationMetrics.length);
  }

  /// Clear all metrics
  static void clearMetrics() {
    _metrics.clear();
    _timers.clear();
  }
}

class PerformanceMetric {
  final String operationName;
  final Duration duration;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  PerformanceMetric({
    required this.operationName,
    required this.duration,
    required this.timestamp,
    this.metadata,
  });
}
```

### Security Configuration

Create `lib/utils/security_config.dart`:

```dart
import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

class SecurityConfig {
  static const List<String> allowedHosts = [
    'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
    'payments.zoho.in',
    'js.zohostatic.com',
  ];

  /// Validate URL for security
  static bool isUrlSafe(String url) {
    try {
      final uri = Uri.parse(url);

      // Must be HTTPS in production
      if (!kDebugMode && uri.scheme != 'https') {
        return false;
      }

      // Check against allowed hosts
      return allowedHosts.any((host) => uri.host.contains(host));
    } catch (e) {
      return false;
    }
  }

  /// Generate request signature for API calls
  static String generateSignature(String data, String secret) {
    final key = utf8.encode(secret);
    final bytes = utf8.encode(data);
    final hmacSha256 = Hmac(sha256, key);
    final digest = hmacSha256.convert(bytes);
    return digest.toString();
  }

  /// Validate response integrity
  static bool validateResponseIntegrity(
    String responseBody,
    String? signature,
    String secret,
  ) {
    if (signature == null) return false;

    final expectedSignature = generateSignature(responseBody, secret);
    return signature == expectedSignature;
  }

  /// Sanitize user input
  static String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'[<>"\']'), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'data:', caseSensitive: false), '')
        .trim();
  }

  /// Generate secure random string
  static String generateSecureToken(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }
}
```

### Deployment Checklist

#### Pre-Deployment Verification

- [ ] **API Integration Testing**

  - [ ] Payment link creation works with production API
  - [ ] Payment verification returns correct status
  - [ ] Invoice status updates automatically (Overdue → Paid)
  - [ ] Webhook integration functions properly
  - [ ] Error handling covers all scenarios

- [ ] **Security Validation**

  - [ ] HTTPS enforced for all API calls
  - [ ] URL validation prevents malicious redirects
  - [ ] Input sanitization implemented
  - [ ] Certificate pinning configured (optional)

- [ ] **Performance Testing**

  - [ ] Payment flow completes within acceptable time
  - [ ] Memory usage optimized for mobile devices
  - [ ] Background processing doesn't drain battery
  - [ ] Offline mode works correctly

- [ ] **Platform-Specific Testing**
  - [ ] Android WebView handles payment links correctly
  - [ ] iOS WKWebView processes callbacks properly
  - [ ] Deep linking works on both platforms
  - [ ] Background notifications function correctly

#### Production Configuration

- [ ] **Build Configuration**

  - [ ] Production API URLs configured
  - [ ] Debug logging disabled
  - [ ] Code obfuscation enabled
  - [ ] App signing configured

- [ ] **Monitoring Setup**
  - [ ] Crash reporting enabled (Firebase Crashlytics)
  - [ ] Performance monitoring configured
  - [ ] Payment analytics tracking
  - [ ] Error logging and alerting

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Payment Link Creation Fails

**Symptoms:**

- API returns 400 Bad Request
- Missing required fields error
- Network timeout errors

**Solutions:**

```dart
// Debug payment link creation
void debugPaymentLinkCreation() {
  PerformanceMonitor.startTimer('payment_link_creation');

  try {
    final result = await paymentService.createPaymentLink(
      amount: amount,
      description: description,
      customerEmail: customerEmail,
      invoiceNumber: invoiceNumber,
      customerId: customerId,
    );

    PerformanceMonitor.stopTimer('payment_link_creation', metadata: {
      'success': true,
      'transaction_id': result.data.transactionId,
    });
  } catch (e) {
    PerformanceMonitor.stopTimer('payment_link_creation', metadata: {
      'success': false,
      'error': e.toString(),
    });

    // Log detailed error information
    debugPrint('Payment Link Creation Failed:');
    debugPrint('Error: $e');
    debugPrint('Amount: $amount');
    debugPrint('Email: $customerEmail');
    debugPrint('Invoice: $invoiceNumber');
  }
}
```

**Common Fixes:**

- Validate all required fields before API call
- Check network connectivity
- Verify API endpoint URL
- Ensure amount is greater than 0
- Validate email format

#### 2. WebView Not Loading Payment Page

**Symptoms:**

- Blank WebView screen
- Loading indicator never disappears
- JavaScript errors in WebView

**Solutions:**

```dart
// Enhanced WebView debugging
void debugWebView() {
  _controller = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..enableDebugging(kDebugMode) // Enable debugging in development
    ..setNavigationDelegate(
      NavigationDelegate(
        onPageStarted: (url) {
          debugPrint('WebView started loading: $url');
        },
        onPageFinished: (url) {
          debugPrint('WebView finished loading: $url');
        },
        onWebResourceError: (error) {
          debugPrint('WebView error: ${error.description}');
          debugPrint('Error code: ${error.errorCode}');
          debugPrint('Error type: ${error.errorType}');
        },
      ),
    );
}
```

**Common Fixes:**

- Enable JavaScript in WebView
- Check internet connectivity
- Verify payment URL is valid
- Clear WebView cache
- Check for CORS issues

#### 3. Payment Status Not Updating

**Symptoms:**

- Payment completes but status remains pending
- Invoice status doesn't change from "Overdue" to "Paid"
- Verification API returns stale data

**Solutions:**

```dart
// Debug payment status verification
Future<void> debugPaymentVerification(String transactionId) async {
  debugPrint('Starting payment verification for: $transactionId');

  try {
    // Check cached data first
    final cachedResult = await _getCachedPaymentStatus(transactionId);
    if (cachedResult != null) {
      debugPrint('Found cached result: ${cachedResult.data.status}');
    }

    // Verify with server
    final result = await paymentService.verifyPaymentStatus(transactionId);
    debugPrint('Server verification result: ${result.data.status}');
    debugPrint('Invoice status updated: ${result.data.invoiceStatusUpdated}');
    debugPrint('Invoice payment status: ${result.data.invoicePaymentStatus}');

    // Check if webhook was processed
    if (result.data.invoiceStatusUpdated) {
      debugPrint('✅ Invoice status successfully updated via webhook');
    } else {
      debugPrint('⚠️ Invoice status not updated - check webhook processing');
    }

  } catch (e) {
    debugPrint('❌ Payment verification failed: $e');
  }
}
```

**Common Fixes:**

- Implement polling with exponential backoff
- Check webhook processing on server
- Verify transaction ID is correct
- Clear local cache and retry
- Check server logs for webhook delivery

#### 4. Background Processing Issues

**Symptoms:**

- Notifications not appearing
- Payment status not updated when app is backgrounded
- Battery drain from background processing

**Solutions:**

```dart
// Debug background service
void debugBackgroundService() {
  // Check if background processing is enabled
  final pendingPayments = await _getPendingPayments();
  debugPrint('Pending payments: ${pendingPayments.length}');

  for (final transactionId in pendingPayments) {
    debugPrint('Checking payment: $transactionId');

    try {
      final result = await paymentService.verifyPaymentStatus(transactionId);
      debugPrint('Status: ${result.data.status}');

      if (result.data.status == 'succeeded' || result.data.status == 'failed') {
        debugPrint('Payment completed, removing from pending list');
        await removePendingPayment(transactionId);
      }
    } catch (e) {
      debugPrint('Background verification failed: $e');
    }
  }
}
```

**Common Fixes:**

- Check notification permissions
- Verify background processing permissions
- Optimize polling frequency
- Implement proper error handling
- Use WorkManager for Android background tasks

#### 5. Network and Connectivity Issues

**Symptoms:**

- Intermittent API failures
- Timeout errors
- Offline mode not working

**Solutions:**

```dart
// Network diagnostics
Future<void> diagnoseNetworkIssues() async {
  // Check connectivity
  final connectivityResult = await Connectivity().checkConnectivity();
  debugPrint('Connectivity: $connectivityResult');

  // Test API endpoint
  try {
    final response = await http.get(
      Uri.parse('${AppConstants.baseUrl}/health'),
      headers: {'User-Agent': 'AquaPartner-Flutter/${AppConstants.appVersion}'},
    ).timeout(Duration(seconds: 10));

    debugPrint('API Health Check: ${response.statusCode}');
  } catch (e) {
    debugPrint('API Health Check Failed: $e');
  }

  // Check cached data availability
  final cachedPayments = await _getCachedPayments();
  debugPrint('Cached payments available: ${cachedPayments.length}');
}
```

**Common Fixes:**

- Implement retry logic with exponential backoff
- Add offline mode with local caching
- Check firewall and proxy settings
- Verify SSL certificate validity
- Use connectivity monitoring

### Debug Tools and Utilities

#### Payment Debug Screen

```dart
class PaymentDebugScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Payment Debug')),
      body: ListView(
        children: [
          ListTile(
            title: Text('Performance Metrics'),
            onTap: () => _showPerformanceMetrics(context),
          ),
          ListTile(
            title: Text('Error Logs'),
            onTap: () => _showErrorLogs(context),
          ),
          ListTile(
            title: Text('Cached Payments'),
            onTap: () => _showCachedPayments(context),
          ),
          ListTile(
            title: Text('Network Diagnostics'),
            onTap: () => _runNetworkDiagnostics(context),
          ),
          ListTile(
            title: Text('Clear All Cache'),
            onTap: () => _clearAllCache(context),
          ),
        ],
      ),
    );
  }

  void _showPerformanceMetrics(BuildContext context) {
    final metrics = PerformanceMonitor.getMetrics();
    // Show metrics in dialog or new screen
  }

  void _showErrorLogs(BuildContext context) async {
    final errorHandler = context.read<PaymentErrorHandler>();
    final logs = await errorHandler.getErrorLogs();
    // Display error logs
  }

  // Additional debug methods...
}
```

### Production Monitoring

#### Key Metrics to Monitor

1. **Payment Success Rate**

   - Track successful vs failed payments
   - Monitor by payment amount ranges
   - Identify patterns in failures

2. **API Response Times**

   - Payment link creation time
   - Payment verification time
   - Server response times

3. **Error Rates**

   - Network errors
   - API errors
   - WebView errors
   - Background processing errors

4. **User Experience Metrics**
   - Time to complete payment
   - Abandonment rates
   - Retry attempts

#### Monitoring Implementation

```dart
class PaymentAnalytics {
  static void trackPaymentStarted(String invoiceNumber, double amount) {
    // Send to analytics service
    FirebaseAnalytics.instance.logEvent(
      name: 'payment_started',
      parameters: {
        'invoice_number': invoiceNumber,
        'amount': amount,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  static void trackPaymentCompleted(
    String invoiceNumber,
    double amount,
    bool success,
    Duration duration,
  ) {
    FirebaseAnalytics.instance.logEvent(
      name: 'payment_completed',
      parameters: {
        'invoice_number': invoiceNumber,
        'amount': amount,
        'success': success,
        'duration_ms': duration.inMilliseconds,
      },
    );
  }

  static void trackError(String errorType, String errorMessage) {
    FirebaseCrashlytics.instance.recordError(
      errorMessage,
      null,
      information: [errorType],
    );
  }
}
```

---

## Summary

This comprehensive Flutter Payment Integration Guide provides everything needed to implement a production-ready payment system with the AquaPartner API. The implementation includes:

✅ **Complete API Integration** - Full compatibility with existing AquaPartner payment workflow
✅ **Automatic Invoice Status Updates** - Seamless "Overdue" → "Paid" transitions
✅ **Production-Ready Code** - Error handling, retry logic, and offline support
✅ **Mobile Optimization** - Background processing and notifications
✅ **Comprehensive Testing** - Unit tests, integration tests, and mock server
✅ **Security Best Practices** - URL validation, input sanitization, and HTTPS enforcement
✅ **Performance Monitoring** - Analytics, error tracking, and performance metrics
✅ **Troubleshooting Tools** - Debug utilities and diagnostic capabilities

The implementation maintains full compatibility with the existing AquaPartner system while providing a robust, scalable Flutter integration that can handle production workloads reliably.
