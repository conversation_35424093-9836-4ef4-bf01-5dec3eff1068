# Transaction Verification Endpoint

## Overview

The `/api/verify-transaction/[sessionId]` endpoint provides payment transaction verification for both session IDs and transaction IDs. This unified endpoint integrates with the existing AquaPartner payment workflow and returns Flutter-compatible responses when accessed with transaction IDs.

## Endpoint Details

- **URL**: `/api/verify-transaction/{sessionId|transactionId}`
- **Methods**: `GET`, `POST`
- **Authentication**: None required
- **Rate Limiting**: Standard API limits apply
- **Supports**: Both payment session IDs and MongoDB transaction IDs

## GET Request - Transaction Verification

### Request Format

```http
GET /api/verify-transaction/64f1a2b3c4d5e6f7g8h9i0j1
Content-Type: application/json
```

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Transaction verified successfully",
  "data": {
    "transaction_id": "64f1a2b3c4d5e6f7g8h9i0j1",
    "payment_id": "pay_1234567890",
    "status": "succeeded",
    "amount": 1500.5,
    "currency": "INR",
    "invoice_number": "INV-2024-001",
    "customer_email": "<EMAIL>",
    "payment_completed_time": "2024-01-15T10:30:00.000Z",
    "invoice_status_updated": true,
    "invoice_payment_status": "Paid"
  }
}
```

### Error Responses

#### Transaction Not Found (404)

```json
{
  "success": false,
  "error": "Transaction not found",
  "message": "No transaction found with ID: 64f1a2b3c4d5e6f7g8h9i0j1"
}
```

#### Invalid Transaction ID (400)

```json
{
  "success": false,
  "error": "Invalid transaction ID format",
  "message": "Transaction ID must be a valid MongoDB ObjectId"
}
```

#### Server Error (500)

```json
{
  "success": false,
  "error": "Transaction verification failed",
  "message": "An unexpected error occurred while verifying the transaction"
}
```

## POST Request - Force Verification with Updates

### Request Format

```http
POST /api/transaction/verify/64f1a2b3c4d5e6f7g8h9i0j1
Content-Type: application/json

{
  "forceRefresh": true,
  "updateInvoice": true
}
```

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Transaction verification completed with updates",
  "data": {
    "transaction_id": "64f1a2b3c4d5e6f7g8h9i0j1",
    "payment_id": "pay_1234567890",
    "status": "succeeded",
    "amount": 1500.5,
    "currency": "INR",
    "invoice_number": "INV-2024-001",
    "customer_email": "<EMAIL>",
    "payment_completed_time": "2024-01-15T10:30:00.000Z",
    "invoice_status_updated": true,
    "invoice_payment_status": "Paid",
    "force_refreshed": true,
    "invoice_updated": true
  }
}
```

## Response Fields

| Field                    | Type    | Description                                                                         |
| ------------------------ | ------- | ----------------------------------------------------------------------------------- |
| `transaction_id`         | string  | MongoDB ObjectId of the transaction                                                 |
| `payment_id`             | string  | Zoho payment ID (if available)                                                      |
| `status`                 | string  | Payment status: `created`, `pending`, `succeeded`, `failed`, `cancelled`, `expired` |
| `amount`                 | number  | Payment amount                                                                      |
| `currency`               | string  | Payment currency (default: "INR")                                                   |
| `invoice_number`         | string  | Associated invoice number                                                           |
| `customer_email`         | string  | Customer email address                                                              |
| `payment_completed_time` | string  | ISO timestamp of payment completion                                                 |
| `invoice_status_updated` | boolean | Whether the invoice status was updated                                              |
| `invoice_payment_status` | string  | Current invoice payment status: `Paid`, `Overdue`, `Partially Paid`, etc.           |

## Integration with AquaPartner System

### Transaction Lookup

- Uses MongoDB ObjectId to find transactions in the `PaymentTransaction` collection
- Integrates with existing `zohoPaymentService.getTransactionById()` method

### Invoice Status Verification

- Uses existing `invoiceService.findInvoiceByNumber()` to locate invoices
- Checks invoice payment history for transaction references
- Returns current invoice payment status using `invoice.getPaymentSummary()`

### Automatic Status Updates

- Verifies if the invoice status was automatically updated via the `updatePaymentStatus` method
- Confirms the "Overdue" → "Paid" transition workflow
- Tracks payment history entries linked to the transaction

## Usage Examples

### Flutter Integration

```dart
// Verify payment status after WebView completion
final response = await http.get(
  Uri.parse('$baseUrl/api/verify-transaction/$transactionId'),
  headers: {'Content-Type': 'application/json'},
);

if (response.statusCode == 200) {
  final data = json.decode(response.body);
  if (data['success'] && data['data']['status'] == 'succeeded') {
    // Payment successful, check invoice status
    final invoiceUpdated = data['data']['invoice_status_updated'];
    final invoiceStatus = data['data']['invoice_payment_status'];

    if (invoiceUpdated && invoiceStatus == 'Paid') {
      // Invoice successfully updated from Overdue to Paid
      showPaymentSuccess();
    }
  }
}
```

### Force Refresh Example

```dart
// Force refresh transaction status from Zoho
final response = await http.post(
  Uri.parse('$baseUrl/api/verify-transaction/$transactionId'),
  headers: {'Content-Type': 'application/json'},
  body: json.encode({
    'forceRefresh': true,
    'updateInvoice': true,
  }),
);
```

## Error Handling

The endpoint implements comprehensive error handling:

1. **Validation Errors**: Invalid transaction ID format
2. **Not Found Errors**: Transaction doesn't exist
3. **Database Errors**: Connection or query failures
4. **Service Errors**: Invoice service or Zoho API failures

All errors follow the consistent AquaPartner API error response format.

## Security Considerations

- No authentication required (read-only verification)
- Transaction ID validation prevents injection attacks
- Rate limiting follows standard API patterns
- HTTPS enforced in production environment

## Monitoring and Logging

The endpoint includes comprehensive logging:

- Transaction verification attempts
- Invoice status check results
- Error conditions and stack traces
- Performance metrics for monitoring

## Testing

### Manual Testing

```bash
# Test successful verification
curl -X GET "https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/transaction/verify/64f1a2b3c4d5e6f7g8h9i0j1"

# Test invalid transaction ID
curl -X GET "https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/transaction/verify/invalid-id"

# Test force refresh
curl -X POST "https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/transaction/verify/64f1a2b3c4d5e6f7g8h9i0j1" \
  -H "Content-Type: application/json" \
  -d '{"forceRefresh": true, "updateInvoice": true}'
```

### Integration Testing

The endpoint is designed to work seamlessly with the Flutter Payment Integration Guide test scenarios and supports the complete payment verification workflow.
