import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/services/payment_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/payments/payment_link.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_result.dart';
import 'package:aquapartner/presentation/cubit/payment/payment_cubit.dart';
import 'package:aquapartner/presentation/cubit/payment/payment_state.dart';

import 'payment_cubit_test.mocks.dart';

@GenerateMocks([PaymentService, AppLogger, AnalyticsService])
void main() {
  late PaymentCubit paymentCubit;
  late MockPaymentService mockPaymentService;
  late MockAppLogger mockLogger;
  late MockAnalyticsService mockAnalyticsService;

  setUp(() {
    mockPaymentService = MockPaymentService();
    mockLogger = MockAppLogger();
    mockAnalyticsService = MockAnalyticsService();

    paymentCubit = PaymentCubit(
      paymentService: mockPaymentService,
      logger: mockLogger,
      analyticsService: mockAnalyticsService,
    );
  });

  tearDown(() {
    paymentCubit.close();
  });

  group('PaymentCubit', () {
    const tPaymentRequest = PaymentRequest(
      amount: 100.0,
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      currency: 'INR',
    );

    final tPaymentLink = PaymentLink(
      paymentLinkId: 'test_link_id',
      paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
      amount: 100.0,
      currency: 'INR',
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      status: 'active',
      createdTime: DateTime.now(),
      transactionId: 'test_transaction_id',
    );

    test('initial state should be PaymentInitial', () {
      expect(paymentCubit.state, const PaymentInitial());
    });

    group('createPaymentLink', () {
      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLinkCreating, PaymentLinkCreated] when successful',
        build: () {
          when(
            mockPaymentService.createPaymentLink(any),
          ).thenAnswer((_) async => Right(tPaymentLink));
          return paymentCubit;
        },
        act: (cubit) => cubit.createPaymentLink(tPaymentRequest),
        expect:
            () => [
              const PaymentLinkCreating(
                amount: 100.0,
                description: 'Test payment',
                customerEmail: '<EMAIL>',
              ),
              isA<PaymentLinkCreated>(),
            ],
        verify: (_) {
          verify(mockPaymentService.createPaymentLink(tPaymentRequest));
          verify(
            mockAnalyticsService.logEvent(
              name: 'payment_link_creation_started',
              parameters: any,
            ),
          );
          verify(
            mockAnalyticsService.logEvent(
              name: 'payment_link_created',
              parameters: any,
            ),
          );
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLinkCreating, PaymentError] when service fails',
        build: () {
          when(
            mockPaymentService.createPaymentLink(any),
          ).thenAnswer((_) async => Left(ServerFailure()));
          when(
            mockPaymentService.getErrorMessage(any),
          ).thenReturn('Server error occurred');
          return paymentCubit;
        },
        act: (cubit) => cubit.createPaymentLink(tPaymentRequest),
        expect:
            () => [
              const PaymentLinkCreating(
                amount: 100.0,
                description: 'Test payment',
                customerEmail: '<EMAIL>',
              ),
              isA<PaymentError>(),
            ],
        verify: (_) {
          verify(mockPaymentService.createPaymentLink(tPaymentRequest));
          verify(
            mockAnalyticsService.logEvent(
              name: 'payment_link_creation_failed',
              parameters: any,
            ),
          );
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLinkCreating, PaymentError] when exception occurs',
        build: () {
          when(
            mockPaymentService.createPaymentLink(any),
          ).thenThrow(Exception('Unexpected error'));
          return paymentCubit;
        },
        act: (cubit) => cubit.createPaymentLink(tPaymentRequest),
        expect:
            () => [
              const PaymentLinkCreating(
                amount: 100.0,
                description: 'Test payment',
                customerEmail: '<EMAIL>',
              ),
              isA<PaymentError>(),
            ],
        verify: (_) {
          verify(
            mockAnalyticsService.logEvent(
              name: 'payment_link_creation_error',
              parameters: any,
            ),
          );
        },
      );
    });

    group('startPaymentProcessing', () {
      blocTest<PaymentCubit, PaymentState>(
        'should emit PaymentProcessing when called with PaymentLinkCreated state',
        build: () => paymentCubit,
        seed:
            () => PaymentLinkCreated(
              paymentLink: tPaymentLink,
              createdAt: DateTime.now(),
            ),
        act:
            (cubit) =>
                cubit.startPaymentProcessing(tPaymentLink.paymentLinkUrl),
        expect:
            () => [
              PaymentProcessing(
                paymentLink: tPaymentLink,
                currentUrl: tPaymentLink.paymentLinkUrl,
                isLoading: true,
              ),
            ],
        verify: (_) {
          verify(
            mockAnalyticsService.logEvent(
              name: 'payment_processing_started',
              parameters: any,
            ),
          );
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should not emit when not in PaymentLinkCreated state',
        build: () => paymentCubit,
        act:
            (cubit) =>
                cubit.startPaymentProcessing(tPaymentLink.paymentLinkUrl),
        expect: () => [],
      );
    });

    group('updateWebViewLoading', () {
      blocTest<PaymentCubit, PaymentState>(
        'should update loading state when in PaymentProcessing state',
        build: () => paymentCubit,
        seed:
            () => PaymentProcessing(
              paymentLink: tPaymentLink,
              currentUrl: tPaymentLink.paymentLinkUrl,
              isLoading: true,
            ),
        act: (cubit) => cubit.updateWebViewLoading(false),
        expect:
            () => [
              PaymentProcessing(
                paymentLink: tPaymentLink,
                currentUrl: tPaymentLink.paymentLinkUrl,
                isLoading: false,
              ),
            ],
      );
    });

    group('updateCurrentUrl', () {
      const tNewUrl = 'https://example.com/payment/success';

      blocTest<PaymentCubit, PaymentState>(
        'should update URL when in PaymentProcessing state',
        build: () {
          when(
            mockPaymentService.isPaymentCompletionUrl(any),
          ).thenReturn(false);
          return paymentCubit;
        },
        seed:
            () => PaymentProcessing(
              paymentLink: tPaymentLink,
              currentUrl: tPaymentLink.paymentLinkUrl,
              isLoading: false,
            ),
        act: (cubit) => cubit.updateCurrentUrl(tNewUrl),
        expect:
            () => [
              PaymentProcessing(
                paymentLink: tPaymentLink,
                currentUrl: tNewUrl,
                isLoading: false,
              ),
            ],
      );

      blocTest<PaymentCubit, PaymentState>(
        'should handle payment completion when completion URL is detected',
        build: () {
          when(mockPaymentService.isPaymentCompletionUrl(any)).thenReturn(true);
          when(mockPaymentService.parsePaymentResult(any)).thenAnswer(
            (_) async => Right(PaymentResult.success(paymentId: '123')),
          );
          return paymentCubit;
        },
        seed:
            () => PaymentProcessing(
              paymentLink: tPaymentLink,
              currentUrl: tPaymentLink.paymentLinkUrl,
              isLoading: false,
            ),
        act: (cubit) => cubit.updateCurrentUrl(tNewUrl),
        expect:
            () => [
              PaymentProcessing(
                paymentLink: tPaymentLink,
                currentUrl: tNewUrl,
                isLoading: false,
              ),
              isA<PaymentCompleted>(),
            ],
      );
    });

    group('cancelPayment', () {
      blocTest<PaymentCubit, PaymentState>(
        'should emit PaymentCancelled with default reason',
        build: () => paymentCubit,
        seed:
            () => PaymentProcessing(
              paymentLink: tPaymentLink,
              currentUrl: tPaymentLink.paymentLinkUrl,
              isLoading: false,
            ),
        act: (cubit) => cubit.cancelPayment(),
        expect: () => [isA<PaymentCancelled>()],
        verify: (_) {
          verify(
            mockAnalyticsService.logEvent(
              name: 'payment_cancelled',
              parameters: any,
            ),
          );
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit PaymentCancelled with custom reason',
        build: () => paymentCubit,
        act: (cubit) => cubit.cancelPayment(reason: 'Network error'),
        expect: () => [isA<PaymentCancelled>()],
      );
    });

    group('validatePaymentUrl', () {
      const tUrl = 'https://payments.zoho.in/checkout/test';

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentUrlValidating, PaymentUrlValidated] when valid',
        build: () {
          when(
            mockPaymentService.validatePaymentUrl(any),
          ).thenAnswer((_) async => const Right(true));
          return paymentCubit;
        },
        act: (cubit) => cubit.validatePaymentUrl(tUrl),
        expect:
            () => [
              const PaymentUrlValidating(url: tUrl),
              const PaymentUrlValidated(url: tUrl, isValid: true),
            ],
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentUrlValidating, PaymentUrlValidated] when invalid',
        build: () {
          when(
            mockPaymentService.validatePaymentUrl(any),
          ).thenAnswer((_) async => Left(ValidationFailure('Invalid URL')));
          when(
            mockPaymentService.getErrorMessage(any),
          ).thenReturn('Invalid URL');
          return paymentCubit;
        },
        act: (cubit) => cubit.validatePaymentUrl(tUrl),
        expect:
            () => [
              const PaymentUrlValidating(url: tUrl),
              const PaymentUrlValidated(
                url: tUrl,
                isValid: false,
                errorMessage: 'Invalid URL',
              ),
            ],
      );
    });

    group('resetPayment', () {
      blocTest<PaymentCubit, PaymentState>(
        'should emit PaymentInitial',
        build: () => paymentCubit,
        seed:
            () => PaymentError(message: 'Test error', errorAt: DateTime.now()),
        act: (cubit) => cubit.resetPayment(),
        expect: () => [const PaymentInitial()],
      );
    });
  });
}
