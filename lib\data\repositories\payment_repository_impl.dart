import 'package:dartz/dartz.dart';
import '../../core/config/payment_environment_config.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/security/payment_security_manager.dart';
import '../../core/services/payment_rate_limiter.dart';
import '../../core/services/payment_monitoring_service.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/payments/payment_link.dart';
import '../../domain/entities/payments/payment_request.dart';
import '../../domain/entities/payments/payment_result.dart';
import '../../domain/entities/payments/payment_verification_response.dart';
import '../../domain/repositories/payment_repository.dart';
import '../datasources/remote/payment_remote_datasource.dart';
import '../models/payment/payment_request_model.dart';

/// Implementation of the payment repository
class PaymentRepositoryImpl implements PaymentRepository {
  final PaymentRemoteDataSource remoteDataSource;
  final AppLogger logger;

  PaymentRepositoryImpl({required this.remoteDataSource, required this.logger});

  @override
  Future<Either<Failure, PaymentLink>> createPaymentLink(
    PaymentRequest request,
  ) async {
    final startTime = DateTime.now();

    try {
      // Check rate limiting
      final rateLimitAllowed = await PaymentRateLimiter.checkRateLimit(
        request.customerEmail,
      );
      if (!rateLimitAllowed) {
        PaymentMonitoringService.trackRateLimitEvent(
          identifier: request.customerEmail,
          action: 'blocked',
          requestCount: 0, // Would get actual count in production
          limit: PaymentEnvironmentConfig.rateLimitConfig.maxRequestsPerMinute,
          timeWindow: const Duration(minutes: 1),
        );
        return Left(
          ValidationFailure('Rate limit exceeded. Please try again later.'),
        );
      }

      // Convert domain entity to data model
      final requestModel = PaymentRequestModel.fromEntity(
        request,
        redirectUrl:
            '${PaymentEnvironmentConfig.paymentBaseUrl}/api/payment/callback',
      );

      // Track payment initiation
      PaymentMonitoringService.trackPaymentInitiated(
        paymentId: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        amount: request.amount,
        currency: request.currency,
        customerEmail: request.customerEmail,
        invoiceNumber: request.invoiceNumber,
        customerId: request.customerId,
        metadata: request.metadata,
      );

      // Call remote data source
      final paymentLinkModel = await remoteDataSource.createPaymentLink(
        requestModel,
      );

      // Convert to domain entity
      final paymentLink = paymentLinkModel.toEntity();

      // Calculate response time
      final responseTime = DateTime.now().difference(startTime);

      // Track successful payment link creation
      PaymentMonitoringService.trackPaymentLinkCreated(
        paymentLinkId: paymentLink.paymentLinkId,
        paymentLinkUrl: paymentLink.paymentLinkUrl,
        amount: paymentLink.amount,
        currency: paymentLink.currency,
        customerEmail: request.customerEmail,
        responseTime: responseTime,
        apiEndpoint: '/api/zoho/payments/create-link',
      );

      logger.i(
        'Payment link created successfully: ${paymentLink.paymentLinkId}',
      );

      return Right(paymentLink);
    } on ServerException {
      logger.e('Server error while creating payment link');

      PaymentMonitoringService.trackPaymentError(
        errorType: 'ServerException',
        errorMessage: 'Failed to create payment link',
        apiEndpoint: '/api/zoho/payments/create-link',
      );

      return Left(ServerFailure());
    } on NetworkException {
      logger.e('Network error while creating payment link');

      PaymentMonitoringService.trackPaymentError(
        errorType: 'NetworkException',
        errorMessage: 'Network error while creating payment link',
        apiEndpoint: '/api/zoho/payments/create-link',
      );

      return Left(NetworkFailure());
    } catch (e) {
      logger.e('Unexpected error while creating payment link: $e');

      PaymentMonitoringService.trackPaymentError(
        errorType: 'UnexpectedException',
        errorMessage: e.toString(),
        apiEndpoint: '/api/zoho/payments/create-link',
      );

      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PaymentLink>> createPaymentSession(
    PaymentRequest request,
  ) async {
    try {
      // Convert domain entity to data model
      final requestModel = PaymentRequestModel.fromEntity(
        request,
        redirectUrl:
            '${PaymentEnvironmentConfig.paymentBaseUrl}/api/payment/callback',
      );

      // Call remote data source
      final paymentLinkModel = await remoteDataSource.createPaymentSession(
        requestModel,
      );

      // Convert to domain entity and return
      return Right(paymentLinkModel.toEntity());
    } on ServerException {
      logger.e('Server error while creating payment session');
      return Left(ServerFailure());
    } on NetworkException {
      logger.e('Network error while creating payment session');
      return Left(NetworkFailure());
    } catch (e) {
      logger.e('Unexpected error while creating payment session: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  bool validatePaymentUrl(String url) {
    try {
      // Use security manager for comprehensive validation
      final isValid = PaymentSecurityManager.validatePaymentUrl(url);

      if (isValid) {
        logger.i('Payment URL validation passed: $url');
      } else {
        logger.w('Payment URL validation failed: $url');
      }

      return isValid;
    } catch (e) {
      logger.e('Error validating payment URL: $e');
      return false;
    }
  }

  @override
  PaymentResult parsePaymentResult(String callbackUrl) {
    try {
      final uri = Uri.parse(callbackUrl);

      // Check if it's a success or failure URL
      if (uri.path.contains('/payment/success')) {
        return PaymentResult.success(
          paymentId: uri.queryParameters['payment_id'] ?? '',
          transactionId: uri.queryParameters['transaction_id'],
          invoiceNumber: uri.queryParameters['invoice'],
          amount: double.tryParse(uri.queryParameters['amount'] ?? ''),
          paymentLinkId: uri.queryParameters['payment_link_id'],
          callbackSource: uri.queryParameters['callback_source'],
          additionalData: Map<String, String>.from(uri.queryParameters),
        );
      } else if (uri.path.contains('/payment/failed')) {
        return PaymentResult.failed(
          errorMessage: uri.queryParameters['error'],
          transactionId: uri.queryParameters['transaction_id'],
          invoiceNumber: uri.queryParameters['invoice'],
          paymentLinkId: uri.queryParameters['payment_link_id'],
          callbackSource: uri.queryParameters['callback_source'],
          additionalData: Map<String, String>.from(uri.queryParameters),
        );
      } else {
        logger.w('Unknown payment result URL: $callbackUrl');
        return PaymentResult.unknown(
          errorMessage: 'Unknown payment status',
          additionalData: Map<String, String>.from(uri.queryParameters),
        );
      }
    } catch (e) {
      logger.e('Error parsing payment result: $e');
      return PaymentResult.unknown(
        errorMessage: 'Failed to parse payment result: ${e.toString()}',
      );
    }
  }

  @override
  Future<Either<Failure, PaymentResult>> getPaymentStatus(
    String paymentLinkId,
  ) async {
    try {
      final paymentLinkModel = await remoteDataSource.getPaymentStatus(
        paymentLinkId,
      );

      // Convert payment link status to payment result
      PaymentStatus status;
      switch (paymentLinkModel.status.toLowerCase()) {
        case 'paid':
        case 'completed':
        case 'success':
          status = PaymentStatus.success;
          break;
        case 'failed':
        case 'declined':
        case 'error':
          status = PaymentStatus.failed;
          break;
        case 'cancelled':
        case 'canceled':
          status = PaymentStatus.cancelled;
          break;
        default:
          status = PaymentStatus.unknown;
      }

      final result = PaymentResult(
        status: status,
        paymentId: paymentLinkModel.transactionId,
        invoiceNumber: paymentLinkModel.invoiceNumber,
        amount: paymentLinkModel.amount,
        paymentLinkId: paymentLinkModel.paymentLinkId,
        timestamp: DateTime.now(),
      );

      return Right(result);
    } on ServerException {
      logger.e('Server error while getting payment status');
      return Left(ServerFailure());
    } on NetworkException {
      logger.e('Network error while getting payment status');
      return Left(NetworkFailure());
    } catch (e) {
      logger.e('Unexpected error while getting payment status: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> cancelPaymentLink(String paymentLinkId) async {
    try {
      // Note: This would require an API endpoint for cancellation
      // For now, we'll return a not implemented error
      logger.w('Payment link cancellation not implemented');
      return Left(
        UnexpectedFailure('Payment link cancellation not implemented'),
      );
    } catch (e) {
      logger.e('Error cancelling payment link: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PaymentVerificationResponse>> verifyTransaction(
    String transactionId,
  ) async {
    try {
      logger.i('Verifying transaction: $transactionId');

      // Call remote data source to verify transaction
      final responseModel = await remoteDataSource.verifyTransaction(
        transactionId,
      );

      // Convert to domain entity
      final verificationResponse = responseModel.toEntity();

      // Log verification results
      if (verificationResponse.isPaymentSuccessful) {
        logger.i(
          'Payment verification successful for transaction: $transactionId',
        );

        if (verificationResponse.wasInvoiceAutoUpdated) {
          logger.i(
            'Invoice status automatically updated to: ${verificationResponse.invoicePaymentStatus}',
          );

          // Track successful invoice status update
          PaymentMonitoringService.trackSecurityEvent(
            eventType: 'invoice_status_updated',
            description:
                'Invoice status automatically updated from Overdue to ${verificationResponse.invoicePaymentStatus}',
            identifier: transactionId,
            securityData: {
              'invoice_number': verificationResponse.data?.invoiceNumber ?? '',
              'old_status': 'Overdue',
              'new_status': verificationResponse.invoicePaymentStatus,
              'update_method': 'webhook_automatic',
            },
          );
        }
      }

      return Right(verificationResponse);
    } on ServerException {
      logger.e('Server error while verifying transaction: $transactionId');
      return Left(ServerFailure());
    } on NetworkException {
      logger.e('Network error while verifying transaction: $transactionId');
      return Left(NetworkFailure());
    } catch (e) {
      logger.e('Unexpected error while verifying transaction: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, PaymentVerificationResponse>> forceRefreshTransaction(
    String transactionId, {
    bool updateInvoice = true,
  }) async {
    try {
      logger.i(
        'Force refreshing transaction: $transactionId (updateInvoice: $updateInvoice)',
      );

      // Call remote data source to force refresh transaction
      final responseModel = await remoteDataSource.forceRefreshTransaction(
        transactionId,
        updateInvoice: updateInvoice,
      );

      // Convert to domain entity
      final verificationResponse = responseModel.toEntity();

      // Log force refresh results
      if (verificationResponse.isPaymentSuccessful) {
        logger.i('Force refresh successful for transaction: $transactionId');

        if (verificationResponse.wasInvoiceAutoUpdated && updateInvoice) {
          logger.i(
            'Invoice status force updated to: ${verificationResponse.invoicePaymentStatus}',
          );

          // Track forced invoice status update
          PaymentMonitoringService.trackSecurityEvent(
            eventType: 'invoice_status_force_updated',
            description:
                'Invoice status force updated from Overdue to ${verificationResponse.invoicePaymentStatus}',
            identifier: transactionId,
            securityData: {
              'invoice_number': verificationResponse.data?.invoiceNumber ?? '',
              'old_status': 'Overdue',
              'new_status': verificationResponse.invoicePaymentStatus,
              'update_method': 'force_refresh',
            },
          );
        }
      }

      return Right(verificationResponse);
    } on ServerException {
      logger.e(
        'Server error while force refreshing transaction: $transactionId',
      );
      return Left(ServerFailure());
    } on NetworkException {
      logger.e(
        'Network error while force refreshing transaction: $transactionId',
      );
      return Left(NetworkFailure());
    } catch (e) {
      logger.e('Unexpected error while force refreshing transaction: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
