import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:dartz/dartz.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/services/payment_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/payments/payment_link.dart';
import 'package:aquapartner/presentation/cubit/payment/payment_cubit.dart';
import 'package:aquapartner/presentation/screens/payment/payment_initiation_screen.dart';

import 'payment_flow_integration_test.mocks.dart';

@GenerateMocks([PaymentService, AnalyticsService, AppLogger])
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Payment Flow Integration Tests', () {
    late MockPaymentService mockPaymentService;
    late MockAnalyticsService mockAnalyticsService;
    late MockAppLogger mockLogger;

    setUp(() {
      mockPaymentService = MockPaymentService();
      mockAnalyticsService = MockAnalyticsService();
      mockLogger = MockAppLogger();
    });

    testWidgets('Complete payment flow - success scenario', (
      WidgetTester tester,
    ) async {
      // Arrange
      final paymentLink = PaymentLink(
        paymentLinkId: 'test_link_id',
        paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
        amount: 100.0,
        currency: 'INR',
        description: 'Test payment',
        customerEmail: '<EMAIL>',
        status: 'active',
        createdTime: DateTime.now(),
        transactionId: 'test_transaction_id',
      );

      when(
        mockPaymentService.createPaymentLink(any),
      ).thenAnswer((_) async => Right(paymentLink));

      when(
        mockPaymentService.validatePaymentUrl(any),
      ).thenAnswer((_) async => const Right(true));

      when(mockPaymentService.isPaymentCompletionUrl(any)).thenReturn(false);

      // Build the widget tree
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create:
                (context) => PaymentCubit(
                  paymentService: mockPaymentService,
                  logger: mockLogger,
                  analyticsService: mockAnalyticsService,
                ),
            child: const PaymentInitiationScreen(
              amount: 100.0,
              description: 'Test payment',
              customerEmail: '<EMAIL>',
            ),
          ),
        ),
      );

      // Act & Assert
      // 1. Verify initial screen is displayed
      expect(find.text('Payment Details'), findsOneWidget);
      expect(find.text('₹100.00'), findsOneWidget);
      expect(find.text('Test payment'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);

      // 2. Tap the Pay Now button
      final payButton = find.text('Pay Now');
      expect(payButton, findsOneWidget);
      await tester.tap(payButton);
      await tester.pump();

      // 3. Verify loading state
      expect(find.text('Creating Payment Link...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // 4. Wait for payment link creation
      await tester.pumpAndSettle();

      // 5. Verify payment service was called
      verify(mockPaymentService.createPaymentLink(any)).called(1);

      // 6. Verify analytics events
      verify(
        mockAnalyticsService.logEvent(
          name: 'payment_initiation_requested',
          parameters: any,
        ),
      ).called(1);
    });

    testWidgets('Payment flow - validation error scenario', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockPaymentService.createPaymentLink(any),
      ).thenAnswer((_) async => Left(ValidationFailure('Invalid email')));

      when(
        mockPaymentService.getErrorMessage(any),
      ).thenReturn('Invalid email format');

      // Build the widget tree
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create:
                (context) => PaymentCubit(
                  paymentService: mockPaymentService,
                  logger: mockLogger,
                  analyticsService: mockAnalyticsService,
                ),
            child: const PaymentInitiationScreen(
              amount: 100.0,
              description: 'Test payment',
              customerEmail: 'invalid-email',
            ),
          ),
        ),
      );

      // Act
      final payButton = find.text('Pay Now');
      await tester.tap(payButton);
      await tester.pump();

      // Wait for error state
      await tester.pumpAndSettle();

      // Assert
      // Error should be handled gracefully
      verify(mockPaymentService.createPaymentLink(any)).called(1);
      verify(
        mockAnalyticsService.logEvent(
          name: 'payment_initiation_requested',
          parameters: any,
        ),
      ).called(1);
    });

    testWidgets('Payment flow - network error scenario', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(
        mockPaymentService.createPaymentLink(any),
      ).thenAnswer((_) async => Left(NetworkFailure()));

      when(mockPaymentService.getErrorMessage(any)).thenReturn(
        'Network error. Please check your internet connection and try again.',
      );

      // Build the widget tree
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create:
                (context) => PaymentCubit(
                  paymentService: mockPaymentService,
                  logger: mockLogger,
                  analyticsService: mockAnalyticsService,
                ),
            child: const PaymentInitiationScreen(
              amount: 100.0,
              description: 'Test payment',
              customerEmail: '<EMAIL>',
            ),
          ),
        ),
      );

      // Act
      final payButton = find.text('Pay Now');
      await tester.tap(payButton);
      await tester.pump();

      // Wait for error state
      await tester.pumpAndSettle();

      // Assert
      verify(mockPaymentService.createPaymentLink(any)).called(1);
      verify(mockPaymentService.getErrorMessage(any)).called(1);
    });

    testWidgets('Payment initiation screen UI elements', (
      WidgetTester tester,
    ) async {
      // Build the widget tree
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create:
                (context) => PaymentCubit(
                  paymentService: mockPaymentService,
                  logger: mockLogger,
                  analyticsService: mockAnalyticsService,
                ),
            child: const PaymentInitiationScreen(
              amount: 150.50,
              description: 'Product purchase',
              customerEmail: '<EMAIL>',
              invoiceNumber: 'INV-001',
              customerName: 'John Doe',
            ),
          ),
        ),
      );

      // Assert UI elements are present
      expect(find.text('Payment'), findsOneWidget); // App bar title
      expect(find.text('Payment Details'), findsOneWidget);
      expect(find.text('₹150.50'), findsOneWidget);
      expect(find.text('Product purchase'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('INV-001'), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Pay Now'), findsOneWidget);
      expect(find.byIcon(Icons.security), findsOneWidget);
      expect(find.text('Your payment is secured'), findsOneWidget);
    });

    testWidgets('Payment amount formatting', (WidgetTester tester) async {
      // Test different amount formats
      final testCases = [
        {'amount': 100.0, 'expected': '₹100.00'},
        {'amount': 1234.56, 'expected': '₹1,234.56'},
        {'amount': 0.99, 'expected': '₹0.99'},
        {'amount': 10000.0, 'expected': '₹10,000.00'},
      ];

      for (final testCase in testCases) {
        await tester.pumpWidget(
          MaterialApp(
            home: BlocProvider(
              create:
                  (context) => PaymentCubit(
                    paymentService: mockPaymentService,
                    logger: mockLogger,
                    analyticsService: mockAnalyticsService,
                  ),
              child: PaymentInitiationScreen(
                amount: testCase['amount'] as double,
                description: 'Test payment',
                customerEmail: '<EMAIL>',
              ),
            ),
          ),
        );

        expect(find.text(testCase['expected'] as String), findsOneWidget);
        await tester.pumpWidget(Container()); // Clear the widget tree
      }
    });

    testWidgets('Payment button states', (WidgetTester tester) async {
      // Arrange
      when(mockPaymentService.createPaymentLink(any)).thenAnswer((_) async {
        // Simulate delay
        await Future.delayed(const Duration(milliseconds: 500));
        return Left(NetworkFailure());
      });

      // Build the widget tree
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create:
                (context) => PaymentCubit(
                  paymentService: mockPaymentService,
                  logger: mockLogger,
                  analyticsService: mockAnalyticsService,
                ),
            child: const PaymentInitiationScreen(
              amount: 100.0,
              description: 'Test payment',
              customerEmail: '<EMAIL>',
            ),
          ),
        ),
      );

      // Initial state - button should be enabled
      final payButton = find.text('Pay Now');
      expect(payButton, findsOneWidget);

      // Tap the button
      await tester.tap(payButton);
      await tester.pump();

      // Loading state - button should show loading
      expect(find.text('Creating Payment Link...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for completion
      await tester.pumpAndSettle();

      // Button should be enabled again after error
      expect(find.text('Pay Now'), findsOneWidget);
    });
  });
}
