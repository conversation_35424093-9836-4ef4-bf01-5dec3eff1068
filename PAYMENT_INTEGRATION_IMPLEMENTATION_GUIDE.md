# AquaPartner Payment Integration Implementation Guide

## Overview

This document provides a comprehensive guide for the payment service integration implemented in the AquaPartner Flutter application. The implementation follows the existing codebase patterns and integrates seamlessly with the current architecture.

## Implementation Summary

### Architecture Overview

The payment integration follows the Clean Architecture pattern established in the AquaPartner codebase:

```
lib/
├── domain/
│   ├── entities/payments/          # Payment domain entities
│   ├── repositories/               # Payment repository interfaces
│   └── usecases/payment/          # Payment use cases
├── data/
│   ├── models/payment/            # Payment data models
│   ├── datasources/remote/        # Payment API data sources
│   └── repositories/              # Payment repository implementations
├── presentation/
│   ├── cubit/payment/             # Payment state management
│   ├── screens/payment/           # Payment UI screens
│   └── widgets/payment/           # Payment UI components
├── core/
│   ├── services/                  # Payment service layer
│   ├── security/                  # Payment security utilities
│   └── utils/                     # Payment utility classes
└── injection/                     # Dependency injection setup
```

### Key Components

#### 1. Domain Layer

- **PaymentLink**: Entity representing a payment link
- **PaymentRequest**: Entity for payment request data
- **PaymentResult**: Entity for payment completion results
- **PaymentRepository**: Repository interface for payment operations
- **Use Cases**: CreatePaymentLinkUseCase, ValidatePaymentUrlUseCase, ParsePaymentResultUseCase

#### 2. Data Layer

- **PaymentLinkModel**: Data model for API responses
- **PaymentRequestModel**: Data model for API requests
- **PaymentRemoteDataSource**: API client for payment operations
- **PaymentRepositoryImpl**: Repository implementation with error handling

#### 3. Presentation Layer

- **PaymentCubit**: State management for payment operations
- **PaymentState**: Various states for payment flow
- **PaymentInitiationScreen**: Screen for initiating payments
- **PaymentResultScreen**: Screen for displaying payment results
- **PaymentWebView**: WebView component for payment processing

#### 4. Core Services

- **PaymentService**: High-level service for payment operations
- **PaymentSecurityManager**: Security utilities for payment data
- **PaymentUrlValidator**: URL validation utilities
- **InputSanitizer**: Input sanitization utilities

## Files Created

### Domain Layer

1. `lib/domain/entities/payments/payment_link.dart` - Payment link entity
2. `lib/domain/entities/payments/payment_request.dart` - Payment request entity
3. `lib/domain/entities/payments/payment_result.dart` - Payment result entity
4. `lib/domain/repositories/payment_repository.dart` - Repository interface
5. `lib/domain/usecases/payment/create_payment_link_usecase.dart` - Create payment link use case
6. `lib/domain/usecases/payment/validate_payment_url_usecase.dart` - URL validation use case
7. `lib/domain/usecases/payment/parse_payment_result_usecase.dart` - Result parsing use case

### Data Layer

8. `lib/data/models/payment/payment_link_model.dart` - Payment link data model
9. `lib/data/models/payment/payment_request_model.dart` - Payment request data model
10. `lib/data/models/payment/payment_link_response_model.dart` - API response model
11. `lib/data/datasources/remote/payment_remote_datasource.dart` - Remote data source
12. `lib/data/repositories/payment_repository_impl.dart` - Repository implementation

### Presentation Layer

13. `lib/presentation/cubit/payment/payment_state.dart` - Payment states
14. `lib/presentation/cubit/payment/payment_cubit.dart` - Payment cubit
15. `lib/presentation/screens/payment/payment_initiation_screen.dart` - Initiation screen
16. `lib/presentation/screens/payment/payment_result_screen.dart` - Result screen
17. `lib/presentation/widgets/payment/payment_webview.dart` - WebView widget

### Core Services

18. `lib/core/services/payment_service.dart` - Payment service
19. `lib/core/security/payment_security_manager.dart` - Security manager
20. `lib/core/utils/payment_url_validator.dart` - URL validator
21. `lib/core/utils/input_sanitizer.dart` - Input sanitizer

### Dependency Injection

22. `lib/injection/payment_di.dart` - Payment DI setup

### Tests

23. `test/domain/usecases/payment/create_payment_link_usecase_test.dart` - Use case tests
24. `test/core/services/payment_service_test.dart` - Service tests
25. `test/presentation/cubit/payment/payment_cubit_test.dart` - Cubit tests
26. `test/integration/payment_flow_integration_test.dart` - Integration tests

## Dependencies Added

The following dependencies were added using Flutter's package manager:

```bash
flutter pub add crypto
flutter pub add webview_flutter
```

### Existing Dependencies Used

- `dartz` - For functional programming (Either type)
- `equatable` - For value equality
- `flutter_bloc` - For state management
- `get_it` - For dependency injection
- `aqua_ui` - For UI components (existing)

## Integration Points

### 1. Dependency Injection

Payment services are registered in the existing GetIt container:

```dart
// In lib/injection_container.dart
import 'injection/payment_di.dart';

// Added in init() method:
initPaymentDependencies(sl);
```

### 2. App Router

Payment routes are integrated into the existing app router:

```dart
// In lib/core/routes/app_router.dart
static const String paymentInitiation = '/payment-initiation';
static const String paymentResult = '/payment-result';

// Navigation methods added:
static Future<dynamic> navigateToPaymentInitiation({...});
static Future<dynamic> navigateToPaymentResult({...});
```

### 3. API Client

Uses the existing `ApiClient` for network requests with consistent error handling.

### 4. Analytics

Integrates with the existing `AnalyticsService` for tracking payment events.

### 5. Logging

Uses the existing `AppLogger` for consistent logging throughout the payment flow.

## Usage Examples

### 1. Basic Payment Initiation

```dart
// Navigate to payment screen
AppRouter.navigateToPaymentInitiation(
  amount: 100.0,
  description: 'Product purchase',
  customerEmail: '<EMAIL>',
  invoiceNumber: 'INV-001',
);
```

### 2. Using Payment Service Directly

```dart
// Inject payment service
final paymentService = sl<PaymentService>();

// Create payment request
final request = paymentService.createPaymentRequest(
  amount: 100.0,
  description: 'Product purchase',
  customerEmail: '<EMAIL>',
);

// Create payment link
final result = await paymentService.createPaymentLink(request);
result.fold(
  (failure) => print('Error: ${paymentService.getErrorMessage(failure)}'),
  (paymentLink) => print('Payment URL: ${paymentLink.paymentLinkUrl}'),
);
```

### 3. Using Payment Cubit

```dart
// In a widget
BlocProvider(
  create: (context) => sl<PaymentCubit>(),
  child: BlocListener<PaymentCubit, PaymentState>(
    listener: (context, state) {
      if (state is PaymentCompleted) {
        if (state.isSuccess) {
          // Handle successful payment
        } else {
          // Handle failed payment
        }
      }
    },
    child: YourPaymentWidget(),
  ),
)
```

## Security Features

### 1. Input Sanitization

All user inputs are sanitized using `InputSanitizer`:

- SQL injection prevention
- XSS attack prevention
- HTML tag removal
- Control character filtering

### 2. URL Validation

Payment URLs are validated using `PaymentUrlValidator`:

- Domain whitelist validation
- HTTPS enforcement
- Malicious pattern detection
- Query parameter sanitization

### 3. Security Manager

`PaymentSecurityManager` provides:

- Request data sanitization
- Security token generation
- Rate limiting
- Callback URL validation

## Error Handling

The implementation includes comprehensive error handling:

### 1. Domain Failures

- `ValidationFailure` - Input validation errors
- `NetworkFailure` - Network connectivity issues
- `ServerFailure` - Server-side errors
- `AuthFailure` - Authentication errors
- `UnexpectedFailure` - Unexpected errors

### 2. User-Friendly Messages

The `PaymentService.getErrorMessage()` method converts technical failures into user-friendly messages.

### 3. Retry Mechanisms

Payment screens include retry functionality for failed operations.

## Testing

### 1. Unit Tests

- Use case tests with mocked repositories
- Service tests with mocked dependencies
- Cubit tests with bloc_test package

### 2. Widget Tests

- Screen rendering tests
- User interaction tests
- State change verification

### 3. Integration Tests

- Complete payment flow testing
- Error scenario testing
- UI element verification

### Running Tests

```bash
# Run all tests
flutter test

# Run specific test files
flutter test test/domain/usecases/payment/
flutter test test/core/services/payment_service_test.dart
flutter test test/presentation/cubit/payment/

# Run integration tests
flutter test integration_test/
```

## Configuration

### 1. API Endpoints

Configure payment API endpoints in `AppConstants`:

```dart
// In lib/core/constants/app_constants.dart
static const String paymentBaseUrl = 'your-payment-api-base-url';
```

### 2. Security Settings

Update allowed domains in `PaymentUrlValidator`:

```dart
static const List<String> _allowedDomains = [
  'payments.zoho.in',
  'your-payment-gateway.com',
  'localhost', // For development
];
```

## Best Practices

### 1. Always Validate Inputs

Use the provided sanitization utilities for all user inputs.

### 2. Handle All Error Cases

Implement proper error handling for network, server, and validation errors.

### 3. Use Analytics

Track payment events for monitoring and debugging.

### 4. Test Thoroughly

Write comprehensive tests for all payment scenarios.

### 5. Follow Security Guidelines

- Never log sensitive payment data
- Validate all URLs before loading
- Use HTTPS for all payment operations
- Implement proper timeout handling

## Troubleshooting

### Common Issues

1. **WebView not loading**: Check URL validation and network connectivity
2. **Payment link creation fails**: Verify API credentials and request format
3. **Callback not working**: Ensure callback URL is properly configured
4. **State not updating**: Check BlocProvider setup and state emissions

### Debug Mode

Enable debug logging by setting log level in `AppLogger`:

```dart
// Enable verbose logging for payment operations
logger.setLevel(LogLevel.debug);
```

## Future Enhancements

1. **Offline Support**: Implement local storage for payment requests
2. **Multiple Payment Gateways**: Add support for additional payment providers
3. **Recurring Payments**: Implement subscription payment flows
4. **Payment History**: Add payment transaction history
5. **Advanced Analytics**: Implement detailed payment analytics

## Quick Start Example

Here's a complete example of integrating payment functionality into an existing screen:

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:aquapartner/core/routes/app_router.dart';
import 'package:aquapartner/injection_container.dart' as di;

class ProductPurchaseScreen extends StatelessWidget {
  final double productPrice;
  final String productName;
  final String customerEmail;

  const ProductPurchaseScreen({
    Key? key,
    required this.productPrice,
    required this.productName,
    required this.customerEmail,
  }) : super(key: key);

  void _initiatePayment(BuildContext context) {
    AppRouter.navigateToPaymentInitiation(
      amount: productPrice,
      description: 'Purchase: $productName',
      customerEmail: customerEmail,
      metadata: {
        'product_name': productName,
        'source': 'product_purchase_screen',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Purchase $productName')),
      body: Column(
        children: [
          // Product details...
          ElevatedButton(
            onPressed: () => _initiatePayment(context),
            child: Text('Pay ₹${productPrice.toStringAsFixed(2)}'),
          ),
        ],
      ),
    );
  }
}
```

## Support

For issues or questions regarding the payment integration:

1. Check the test files for usage examples
2. Review the existing codebase patterns
3. Ensure all dependencies are properly installed
4. Verify API configuration and credentials
5. Check network connectivity and firewall settings

This implementation provides a robust, secure, and scalable payment solution that integrates seamlessly with the existing AquaPartner application architecture.
