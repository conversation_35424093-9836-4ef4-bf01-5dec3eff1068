import 'package:flutter/foundation.dart';
import '../utils/logger.dart';

/// Environment-specific payment configuration
class PaymentEnvironmentConfig {
  static final AppLogger _logger = AppLogger();
  
  /// Current environment from build configuration
  static String get environment => 
      const String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');
  
  /// Payment API base URL for current environment
  static String get paymentBaseUrl => _getConfig().baseUrl;
  
  /// Allowed payment domains for current environment
  static List<String> get allowedDomains => _getConfig().allowedDomains;
  
  /// Payment timeout for current environment
  static Duration get paymentTimeout => _getConfig().timeout;
  
  /// Rate limit settings for current environment
  static RateLimitConfig get rateLimitConfig => _getConfig().rateLimitConfig;
  
  /// Security settings for current environment
  static SecurityConfig get securityConfig => _getConfig().securityConfig;
  
  /// SSL pinning configuration
  static SSLPinningConfig get sslPinningConfig => _getConfig().sslPinningConfig;
  
  /// Get configuration for current environment
  static PaymentConfig _getConfig() {
    final config = _configs[environment];
    if (config == null) {
      _logger.e('No payment configuration found for environment: $environment');
      throw Exception('Invalid environment configuration: $environment');
    }
    
    _logger.i('Using payment configuration for environment: $environment');
    return config;
  }
  
  /// Environment-specific configurations
  static const Map<String, PaymentConfig> _configs = {
    'dev': PaymentConfig(
      baseUrl: 'https://aquapartner-dev.centralindia-01.azurewebsites.net',
      allowedDomains: [
        'aquapartner-dev.centralindia-01.azurewebsites.net',
        'payments-dev.zoho.in',
        'localhost',
      ],
      timeout: Duration(seconds: 45),
      rateLimitConfig: RateLimitConfig(
        maxRequestsPerMinute: 10,
        maxRequestsPerHour: 100,
        enabled: false, // Disabled for development
      ),
      securityConfig: SecurityConfig(
        enableSSLPinning: false,
        enableTokenValidation: true,
        enableRateLimit: false,
        logSecurityEvents: true,
      ),
      sslPinningConfig: SSLPinningConfig(
        enabled: false,
        certificates: [],
      ),
    ),
    'staging': PaymentConfig(
      baseUrl: 'https://aquapartner-staging.centralindia-01.azurewebsites.net',
      allowedDomains: [
        'aquapartner-staging.centralindia-01.azurewebsites.net',
        'payments-staging.zoho.in',
      ],
      timeout: Duration(seconds: 30),
      rateLimitConfig: RateLimitConfig(
        maxRequestsPerMinute: 8,
        maxRequestsPerHour: 200,
        enabled: true,
      ),
      securityConfig: SecurityConfig(
        enableSSLPinning: true,
        enableTokenValidation: true,
        enableRateLimit: true,
        logSecurityEvents: true,
      ),
      sslPinningConfig: SSLPinningConfig(
        enabled: true,
        certificates: [
          'sha256/STAGING_CERT_HASH_HERE',
        ],
      ),
    ),
    'production': PaymentConfig(
      baseUrl: 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
      allowedDomains: [
        'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
        'payments.zoho.in',
        'payments.zoho.com',
      ],
      timeout: Duration(seconds: 30),
      rateLimitConfig: RateLimitConfig(
        maxRequestsPerMinute: 5,
        maxRequestsPerHour: 100,
        enabled: true,
      ),
      securityConfig: SecurityConfig(
        enableSSLPinning: true,
        enableTokenValidation: true,
        enableRateLimit: true,
        logSecurityEvents: true,
      ),
      sslPinningConfig: SSLPinningConfig(
        enabled: true,
        certificates: [
          'sha256/PRODUCTION_CERT_HASH_HERE',
          'sha256/BACKUP_CERT_HASH_HERE',
        ],
      ),
    ),
  };
  
  /// Validate environment configuration at startup
  static void validateConfiguration() {
    try {
      final config = _getConfig();
      
      // Validate required fields
      if (config.baseUrl.isEmpty) {
        throw Exception('Payment base URL not configured for environment: $environment');
      }
      
      if (config.allowedDomains.isEmpty) {
        throw Exception('No allowed domains configured for environment: $environment');
      }
      
      // Validate production-specific requirements
      if (environment == 'production') {
        if (!config.securityConfig.enableSSLPinning) {
          throw Exception('SSL pinning must be enabled in production');
        }
        
        if (!config.securityConfig.enableRateLimit) {
          throw Exception('Rate limiting must be enabled in production');
        }
        
        if (config.sslPinningConfig.certificates.isEmpty) {
          throw Exception('SSL certificates must be configured for production');
        }
      }
      
      _logger.i('Payment configuration validation successful for environment: $environment');
    } catch (e) {
      _logger.e('Payment configuration validation failed: $e');
      rethrow;
    }
  }
}

/// Payment configuration for a specific environment
class PaymentConfig {
  final String baseUrl;
  final List<String> allowedDomains;
  final Duration timeout;
  final RateLimitConfig rateLimitConfig;
  final SecurityConfig securityConfig;
  final SSLPinningConfig sslPinningConfig;
  
  const PaymentConfig({
    required this.baseUrl,
    required this.allowedDomains,
    required this.timeout,
    required this.rateLimitConfig,
    required this.securityConfig,
    required this.sslPinningConfig,
  });
}

/// Rate limiting configuration
class RateLimitConfig {
  final int maxRequestsPerMinute;
  final int maxRequestsPerHour;
  final bool enabled;
  
  const RateLimitConfig({
    required this.maxRequestsPerMinute,
    required this.maxRequestsPerHour,
    required this.enabled,
  });
}

/// Security configuration
class SecurityConfig {
  final bool enableSSLPinning;
  final bool enableTokenValidation;
  final bool enableRateLimit;
  final bool logSecurityEvents;
  
  const SecurityConfig({
    required this.enableSSLPinning,
    required this.enableTokenValidation,
    required this.enableRateLimit,
    required this.logSecurityEvents,
  });
}

/// SSL certificate pinning configuration
class SSLPinningConfig {
  final bool enabled;
  final List<String> certificates;
  
  const SSLPinningConfig({
    required this.enabled,
    required this.certificates,
  });
}
