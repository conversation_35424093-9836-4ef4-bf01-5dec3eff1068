import '../../domain/entities/payments/payment_verification_response.dart';
import '../../domain/usecases/payments/verify_transaction_usecase.dart';
import '../error/failures.dart';
import '../utils/logger.dart';
import 'payment_monitoring_service.dart';

/// Service to handle payment completion flow including transaction verification
/// and invoice status updates
class PaymentCompletionService {
  final VerifyTransaction _verifyTransaction;
  final ForceRefreshTransaction _forceRefreshTransaction;
  final AppLogger _logger;

  PaymentCompletionService({
    required VerifyTransaction verifyTransaction,
    required ForceRefreshTransaction forceRefreshTransaction,
    required AppLogger logger,
  }) : _verifyTransaction = verifyTransaction,
       _forceRefreshTransaction = forceRefreshTransaction,
       _logger = logger;

  /// Complete payment verification flow
  /// This method handles the entire payment completion process:
  /// 1. Verify transaction status
  /// 2. Check if invoice was automatically updated
  /// 3. Force refresh if needed
  /// 4. Return comprehensive result
  Future<PaymentCompletionResult> completePaymentFlow({
    required String transactionId,
    required String invoiceNumber,
    bool forceRefreshIfNeeded = true,
    Duration? retryDelay,
  }) async {
    _logger.i(
      'Starting payment completion flow for transaction: $transactionId',
    );

    try {
      // Step 1: Initial verification
      final verificationResult = await _verifyTransaction(transactionId);

      return await verificationResult.fold(
        (failure) async {
          _logger.e(
            'Initial transaction verification failed: ${failure.toString()}',
          );

          // If initial verification fails and force refresh is enabled, try force refresh
          if (forceRefreshIfNeeded) {
            _logger.i('Attempting force refresh due to verification failure');
            return await _attemptForceRefresh(transactionId, invoiceNumber);
          }

          return PaymentCompletionResult.failure(
            error:
                'Transaction verification failed: ${_getFailureMessage(failure)}',
            transactionId: transactionId,
            invoiceNumber: invoiceNumber,
          );
        },
        (verificationResponse) async {
          _logger.i(
            'Transaction verification successful: ${verificationResponse.paymentStatus}',
          );

          // Step 2: Check if payment was successful
          if (!verificationResponse.isPaymentSuccessful) {
            _logger.w(
              'Payment was not successful: ${verificationResponse.paymentStatus}',
            );
            return PaymentCompletionResult.paymentFailed(
              status: verificationResponse.paymentStatus,
              transactionId: transactionId,
              invoiceNumber: invoiceNumber,
              verificationResponse: verificationResponse,
            );
          }

          // Step 3: Check if invoice was automatically updated
          if (verificationResponse.wasInvoiceAutoUpdated) {
            _logger.i(
              'Invoice status was automatically updated to: ${verificationResponse.invoicePaymentStatus}',
            );

            // Track successful automatic update
            PaymentMonitoringService.trackSecurityEvent(
              eventType: 'payment_completion_success',
              description:
                  'Payment completed and invoice automatically updated',
              identifier: transactionId,
              securityData: {
                'invoice_number': invoiceNumber,
                'payment_status': verificationResponse.paymentStatus,
                'invoice_status': verificationResponse.invoicePaymentStatus,
                'auto_updated': 'true',
              },
            );

            return PaymentCompletionResult.success(
              transactionId: transactionId,
              invoiceNumber: invoiceNumber,
              verificationResponse: verificationResponse,
              wasAutoUpdated: true,
            );
          }

          // Step 4: Invoice not auto-updated, force refresh if enabled
          if (forceRefreshIfNeeded) {
            _logger.w('Invoice not auto-updated, attempting force refresh');

            // Add delay if specified to allow webhook processing
            if (retryDelay != null) {
              _logger.i(
                'Waiting ${retryDelay.inSeconds} seconds before force refresh',
              );
              await Future.delayed(retryDelay);
            }

            return await _attemptForceRefresh(transactionId, invoiceNumber);
          }

          // Payment successful but invoice not updated and no force refresh
          _logger.w('Payment successful but invoice not automatically updated');
          return PaymentCompletionResult.partialSuccess(
            transactionId: transactionId,
            invoiceNumber: invoiceNumber,
            verificationResponse: verificationResponse,
            reason: 'Invoice status not automatically updated',
          );
        },
      );
    } catch (e) {
      _logger.e('Unexpected error in payment completion flow: $e');
      return PaymentCompletionResult.failure(
        error: 'Unexpected error: $e',
        transactionId: transactionId,
        invoiceNumber: invoiceNumber,
      );
    }
  }

  /// Attempt force refresh of transaction
  Future<PaymentCompletionResult> _attemptForceRefresh(
    String transactionId,
    String invoiceNumber,
  ) async {
    _logger.i('Attempting force refresh for transaction: $transactionId');

    final forceRefreshResult = await _forceRefreshTransaction(
      transactionId,
      updateInvoice: true,
    );

    return forceRefreshResult.fold(
      (failure) {
        _logger.e('Force refresh failed: ${failure.toString()}');
        return PaymentCompletionResult.failure(
          error: 'Force refresh failed: ${_getFailureMessage(failure)}',
          transactionId: transactionId,
          invoiceNumber: invoiceNumber,
        );
      },
      (verificationResponse) {
        if (verificationResponse.isPaymentSuccessful &&
            verificationResponse.wasInvoiceAutoUpdated) {
          _logger.i(
            'Force refresh successful, invoice updated to: ${verificationResponse.invoicePaymentStatus}',
          );

          // Track successful force refresh
          PaymentMonitoringService.trackSecurityEvent(
            eventType: 'payment_completion_force_success',
            description:
                'Payment completed and invoice updated via force refresh',
            identifier: transactionId,
            securityData: {
              'invoice_number': invoiceNumber,
              'payment_status': verificationResponse.paymentStatus,
              'invoice_status': verificationResponse.invoicePaymentStatus,
              'force_refreshed': 'true',
            },
          );

          return PaymentCompletionResult.success(
            transactionId: transactionId,
            invoiceNumber: invoiceNumber,
            verificationResponse: verificationResponse,
            wasAutoUpdated: false,
            wasForceRefreshed: true,
          );
        } else {
          _logger.w('Force refresh completed but invoice still not updated');
          return PaymentCompletionResult.partialSuccess(
            transactionId: transactionId,
            invoiceNumber: invoiceNumber,
            verificationResponse: verificationResponse,
            reason: 'Force refresh completed but invoice not updated',
          );
        }
      },
    );
  }

  /// Convert failure to user-friendly message
  String _getFailureMessage(Failure failure) {
    if (failure is NetworkFailure) {
      return 'Network connection error';
    } else if (failure is ServerFailure) {
      return 'Server error';
    } else if (failure is ValidationFailure) {
      return failure.message;
    } else if (failure is UnexpectedFailure) {
      return failure.message;
    } else {
      return 'Unknown error occurred';
    }
  }
}

/// Result of payment completion flow
class PaymentCompletionResult {
  final bool isSuccess;
  final bool isPartialSuccess;
  final String transactionId;
  final String invoiceNumber;
  final String? error;
  final PaymentVerificationResponse? verificationResponse;
  final bool wasAutoUpdated;
  final bool wasForceRefreshed;
  final String? reason;

  PaymentCompletionResult._({
    required this.isSuccess,
    required this.isPartialSuccess,
    required this.transactionId,
    required this.invoiceNumber,
    this.error,
    this.verificationResponse,
    this.wasAutoUpdated = false,
    this.wasForceRefreshed = false,
    this.reason,
  });

  /// Create success result
  factory PaymentCompletionResult.success({
    required String transactionId,
    required String invoiceNumber,
    required PaymentVerificationResponse verificationResponse,
    bool wasAutoUpdated = false,
    bool wasForceRefreshed = false,
  }) {
    return PaymentCompletionResult._(
      isSuccess: true,
      isPartialSuccess: false,
      transactionId: transactionId,
      invoiceNumber: invoiceNumber,
      verificationResponse: verificationResponse,
      wasAutoUpdated: wasAutoUpdated,
      wasForceRefreshed: wasForceRefreshed,
    );
  }

  /// Create partial success result (payment successful but invoice not updated)
  factory PaymentCompletionResult.partialSuccess({
    required String transactionId,
    required String invoiceNumber,
    required PaymentVerificationResponse verificationResponse,
    String? reason,
  }) {
    return PaymentCompletionResult._(
      isSuccess: false,
      isPartialSuccess: true,
      transactionId: transactionId,
      invoiceNumber: invoiceNumber,
      verificationResponse: verificationResponse,
      reason: reason,
    );
  }

  /// Create payment failed result
  factory PaymentCompletionResult.paymentFailed({
    required String status,
    required String transactionId,
    required String invoiceNumber,
    PaymentVerificationResponse? verificationResponse,
  }) {
    return PaymentCompletionResult._(
      isSuccess: false,
      isPartialSuccess: false,
      transactionId: transactionId,
      invoiceNumber: invoiceNumber,
      error: 'Payment failed with status: $status',
      verificationResponse: verificationResponse,
    );
  }

  /// Create failure result
  factory PaymentCompletionResult.failure({
    required String error,
    required String transactionId,
    required String invoiceNumber,
  }) {
    return PaymentCompletionResult._(
      isSuccess: false,
      isPartialSuccess: false,
      transactionId: transactionId,
      invoiceNumber: invoiceNumber,
      error: error,
    );
  }

  /// Check if invoice status was successfully updated
  bool get wasInvoiceUpdated =>
      verificationResponse?.wasInvoiceAutoUpdated == true;

  /// Get user-friendly status message
  String get statusMessage {
    if (isSuccess) {
      if (wasAutoUpdated) {
        return 'Payment successful! Invoice status automatically updated.';
      } else if (wasForceRefreshed) {
        return 'Payment successful! Invoice status updated.';
      } else {
        return 'Payment successful!';
      }
    } else if (isPartialSuccess) {
      return 'Payment successful, but invoice status update is pending. Please refresh to see updated status.';
    } else {
      return error ?? 'Payment processing failed.';
    }
  }
}
