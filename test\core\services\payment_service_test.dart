import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/services/payment_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/payments/payment_link.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_result.dart';
import 'package:aquapartner/domain/usecases/payment/create_payment_link_usecase.dart';
import 'package:aquapartner/domain/usecases/payment/parse_payment_result_usecase.dart';
import 'package:aquapartner/domain/usecases/payment/validate_payment_url_usecase.dart';

import 'payment_service_test.mocks.dart';

@GenerateMocks([
  CreatePaymentLinkUseCase,
  ValidatePaymentUrlUseCase,
  ParsePaymentResultUseCase,
  AppLogger,
])
void main() {
  late PaymentService paymentService;
  late MockCreatePaymentLinkUseCase mockCreatePaymentLinkUseCase;
  late MockValidatePaymentUrlUseCase mockValidatePaymentUrlUseCase;
  late MockParsePaymentResultUseCase mockParsePaymentResultUseCase;
  late MockAppLogger mockLogger;

  setUp(() {
    mockCreatePaymentLinkUseCase = MockCreatePaymentLinkUseCase();
    mockValidatePaymentUrlUseCase = MockValidatePaymentUrlUseCase();
    mockParsePaymentResultUseCase = MockParsePaymentResultUseCase();
    mockLogger = MockAppLogger();

    paymentService = PaymentService(
      createPaymentLinkUseCase: mockCreatePaymentLinkUseCase,
      validatePaymentUrlUseCase: mockValidatePaymentUrlUseCase,
      parsePaymentResultUseCase: mockParsePaymentResultUseCase,
      logger: mockLogger,
    );
  });

  group('PaymentService', () {
    const tPaymentRequest = PaymentRequest(
      amount: 100.0,
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      currency: 'INR',
    );

    final tPaymentLink = PaymentLink(
      paymentLinkId: 'test_link_id',
      paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
      amount: 100.0,
      currency: 'INR',
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      status: 'active',
      createdTime: DateTime.now(),
      transactionId: 'test_transaction_id',
    );

    group('createPaymentLink', () {
      test('should return PaymentLink when use case succeeds', () async {
        // arrange
        when(mockCreatePaymentLinkUseCase(any))
            .thenAnswer((_) async => Right(tPaymentLink));

        // act
        final result = await paymentService.createPaymentLink(tPaymentRequest);

        // assert
        expect(result, Right(tPaymentLink));
        verify(mockCreatePaymentLinkUseCase(tPaymentRequest));
        verify(mockLogger.i('Creating payment link for amount: 100.0'));
        verify(mockLogger.i('Payment link created successfully: test_link_id'));
      });

      test('should return ValidationFailure for invalid request', () async {
        // arrange
        const invalidRequest = PaymentRequest(
          amount: -100.0,
          description: '',
          customerEmail: 'invalid-email',
          currency: 'INR',
        );

        // act
        final result = await paymentService.createPaymentLink(invalidRequest);

        // assert
        expect(result, isA<Left<Failure, PaymentLink>>());
        result.fold(
          (failure) => expect(failure, isA<ValidationFailure>()),
          (paymentLink) => fail('Should return failure'),
        );
        verifyNever(mockCreatePaymentLinkUseCase(any));
      });

      test('should handle use case failure', () async {
        // arrange
        when(mockCreatePaymentLinkUseCase(any))
            .thenAnswer((_) async => Left(ServerFailure()));

        // act
        final result = await paymentService.createPaymentLink(tPaymentRequest);

        // assert
        expect(result, Left(ServerFailure()));
        verify(mockCreatePaymentLinkUseCase(tPaymentRequest));
        verify(mockLogger.e('Failed to create payment link: ServerFailure'));
      });

      test('should handle unexpected exceptions', () async {
        // arrange
        when(mockCreatePaymentLinkUseCase(any))
            .thenThrow(Exception('Unexpected error'));

        // act
        final result = await paymentService.createPaymentLink(tPaymentRequest);

        // assert
        expect(result, isA<Left<Failure, PaymentLink>>());
        result.fold(
          (failure) => expect(failure, isA<UnexpectedFailure>()),
          (paymentLink) => fail('Should return failure'),
        );
      });
    });

    group('validatePaymentUrl', () {
      const tUrl = 'https://payments.zoho.in/checkout/test';

      test('should return true for valid URL', () async {
        // arrange
        when(mockValidatePaymentUrlUseCase(any))
            .thenAnswer((_) async => const Right(true));

        // act
        final result = await paymentService.validatePaymentUrl(tUrl);

        // assert
        expect(result, const Right(true));
        verify(mockValidatePaymentUrlUseCase(tUrl));
        verify(mockLogger.i('Validating payment URL: $tUrl'));
        verify(mockLogger.i('Payment URL validation result: true'));
      });

      test('should return false for invalid URL', () async {
        // arrange
        when(mockValidatePaymentUrlUseCase(any))
            .thenAnswer((_) async => const Right(false));

        // act
        final result = await paymentService.validatePaymentUrl(tUrl);

        // assert
        expect(result, const Right(false));
        verify(mockValidatePaymentUrlUseCase(tUrl));
        verify(mockLogger.i('Payment URL validation result: false'));
      });

      test('should return ValidationFailure for empty URL', () async {
        // act
        final result = await paymentService.validatePaymentUrl('');

        // assert
        expect(result, isA<Left<Failure, bool>>());
        result.fold(
          (failure) => expect(failure, isA<ValidationFailure>()),
          (isValid) => fail('Should return failure'),
        );
        verifyNever(mockValidatePaymentUrlUseCase(any));
      });

      test('should handle use case failure', () async {
        // arrange
        when(mockValidatePaymentUrlUseCase(any))
            .thenAnswer((_) async => Left(ValidationFailure('Invalid URL')));

        // act
        final result = await paymentService.validatePaymentUrl(tUrl);

        // assert
        expect(result, Left(ValidationFailure('Invalid URL')));
        verify(mockValidatePaymentUrlUseCase(tUrl));
      });
    });

    group('parsePaymentResult', () {
      const tCallbackUrl = 'https://example.com/payment/success?payment_id=123';
      final tPaymentResult = PaymentResult.success(
        paymentId: '123',
        amount: 100.0,
      );

      test('should return PaymentResult for valid callback URL', () async {
        // arrange
        when(mockParsePaymentResultUseCase(any))
            .thenAnswer((_) async => Right(tPaymentResult));

        // act
        final result = await paymentService.parsePaymentResult(tCallbackUrl);

        // assert
        expect(result, Right(tPaymentResult));
        verify(mockParsePaymentResultUseCase(tCallbackUrl));
        verify(mockLogger.i('Parsing payment result from URL: $tCallbackUrl'));
        verify(mockLogger.i('Payment result parsed: ${tPaymentResult.status}'));
      });

      test('should return ValidationFailure for empty callback URL', () async {
        // act
        final result = await paymentService.parsePaymentResult('');

        // assert
        expect(result, isA<Left<Failure, PaymentResult>>());
        result.fold(
          (failure) => expect(failure, isA<ValidationFailure>()),
          (paymentResult) => fail('Should return failure'),
        );
        verifyNever(mockParsePaymentResultUseCase(any));
      });

      test('should handle use case failure', () async {
        // arrange
        when(mockParsePaymentResultUseCase(any))
            .thenAnswer((_) async => Left(ValidationFailure('Invalid callback URL')));

        // act
        final result = await paymentService.parsePaymentResult(tCallbackUrl);

        // assert
        expect(result, Left(ValidationFailure('Invalid callback URL')));
        verify(mockParsePaymentResultUseCase(tCallbackUrl));
      });
    });

    group('utility methods', () {
      test('isPaymentCompletionUrl should identify completion URLs', () {
        expect(paymentService.isPaymentCompletionUrl('https://example.com/payment/success'), true);
        expect(paymentService.isPaymentCompletionUrl('https://example.com/payment/failed'), true);
        expect(paymentService.isPaymentCompletionUrl('https://example.com/other'), false);
      });

      test('getErrorMessage should return appropriate messages', () {
        expect(paymentService.getErrorMessage(ValidationFailure('Test error')), 'Test error');
        expect(paymentService.getErrorMessage(NetworkFailure()), contains('Network error'));
        expect(paymentService.getErrorMessage(ServerFailure()), contains('Server error'));
        expect(paymentService.getErrorMessage(AuthFailure()), contains('Authentication error'));
      });

      test('createPaymentRequest should create valid request', () {
        final request = paymentService.createPaymentRequest(
          amount: 100.0,
          description: 'Test payment',
          customerEmail: '<EMAIL>',
        );

        expect(request.amount, 100.0);
        expect(request.description, 'Test payment');
        expect(request.customerEmail, '<EMAIL>');
        expect(request.currency, 'INR');
        expect(request.sendEmail, true);
        expect(request.sendSms, false);
      });
    });
  });
}
