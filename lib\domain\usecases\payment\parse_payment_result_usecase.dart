import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../entities/payments/payment_result.dart';
import '../../repositories/payment_repository.dart';

/// Use case for parsing payment results from callback URLs
class ParsePaymentResultUseCase implements UseCase<PaymentResult, String> {
  final PaymentRepository repository;

  ParsePaymentResultUseCase(this.repository);

  @override
  Future<Either<Failure, PaymentResult>> call(String callbackUrl) async {
    try {
      final result = repository.parsePaymentResult(callbackUrl);
      return Right(result);
    } catch (e) {
      return Left(ValidationFailure('Failed to parse payment result: ${e.toString()}'));
    }
  }
}

/// Parameters for parsing payment result
class ParsePaymentResultParams {
  final String callbackUrl;

  ParsePaymentResultParams({required this.callbackUrl});
}
