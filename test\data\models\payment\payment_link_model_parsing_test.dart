import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/payment/payment_link_model.dart';

void main() {
  group('PaymentLinkModel String Parsing Tests', () {
    test('should parse numeric strings correctly', () {
      // Arrange - API response with string values
      final jsonWithStrings = {
        'payment_link_id': 'pl_test_123',
        'payment_link_url': 'https://payment.example.com/pay/pl_test_123',
        'amount': '5.00', // String instead of number
        'currency': 'INR',
        'description': 'Test payment',
        'customer_email': '<EMAIL>',
        'status': 'active',
        'created_time': '1640995200', // String instead of number
        'expires_at': '1641081600', // String instead of number
        'transaction_id': 'txn_test_123',
        'invoice_number': 'INV-001',
        'customer_id': 'cust_123',
        'customer_name': 'Test Customer',
        'customer_phone': '+1234567890',
        'reference_id': 'ref_123',
        'send_email': 'true', // String instead of boolean
        'send_sms': 'false', // String instead of boolean
        'partial_payments': '0', // String instead of boolean
      };

      // Act
      final model = PaymentLinkModel.fromJson(jsonWithStrings);

      // Assert
      expect(model.paymentLinkId, equals('pl_test_123'));
      expect(model.paymentLinkUrl, equals('https://payment.example.com/pay/pl_test_123'));
      expect(model.amount, equals(5.0)); // Should be parsed from string "5.00"
      expect(model.currency, equals('INR'));
      expect(model.description, equals('Test payment'));
      expect(model.customerEmail, equals('<EMAIL>'));
      expect(model.status, equals('active'));
      expect(model.createdTime, equals(1640995200)); // Should be parsed from string
      expect(model.expiresAt, equals(1641081600)); // Should be parsed from string
      expect(model.transactionId, equals('txn_test_123'));
      expect(model.invoiceNumber, equals('INV-001'));
      expect(model.customerId, equals('cust_123'));
      expect(model.customerName, equals('Test Customer'));
      expect(model.customerPhone, equals('+1234567890'));
      expect(model.referenceId, equals('ref_123'));
      expect(model.sendEmail, isTrue); // Should be parsed from string "true"
      expect(model.sendSms, isFalse); // Should be parsed from string "false"
      expect(model.partialPayments, isFalse); // Should be parsed from string "0"
    });

    test('should handle mixed numeric types correctly', () {
      // Arrange - API response with mixed types
      final jsonWithMixedTypes = {
        'payment_link_id': 'pl_test_456',
        'payment_link_url': 'https://payment.example.com/pay/pl_test_456',
        'amount': 10.50, // Double
        'currency': 'USD',
        'description': 'Mixed types test',
        'customer_email': '<EMAIL>',
        'status': 'pending',
        'created_time': 1640995300, // Int
        'expires_at': null, // Null
        'transaction_id': 'txn_test_456',
        'send_email': true, // Boolean
        'send_sms': 1, // Int (should be true)
        'partial_payments': false, // Boolean
      };

      // Act
      final model = PaymentLinkModel.fromJson(jsonWithMixedTypes);

      // Assert
      expect(model.amount, equals(10.50)); // Should handle double
      expect(model.createdTime, equals(1640995300)); // Should handle int
      expect(model.expiresAt, isNull); // Should handle null
      expect(model.sendEmail, isTrue); // Should handle boolean
      expect(model.sendSms, isTrue); // Should handle int 1 as true
      expect(model.partialPayments, isFalse); // Should handle boolean
    });

    test('should handle invalid string values gracefully', () {
      // Arrange - API response with invalid string values
      final jsonWithInvalidStrings = {
        'payment_link_id': 'pl_test_789',
        'payment_link_url': 'https://payment.example.com/pay/pl_test_789',
        'amount': 'invalid_amount', // Invalid string
        'currency': 'EUR',
        'description': 'Invalid values test',
        'customer_email': '<EMAIL>',
        'status': 'active',
        'created_time': 'invalid_time', // Invalid string
        'expires_at': 'invalid_expires', // Invalid string
        'transaction_id': 'txn_test_789',
        'send_email': 'maybe', // Invalid boolean string
        'send_sms': 'invalid_bool', // Invalid boolean string
        'partial_payments': 'unknown', // Invalid boolean string
      };

      // Act
      final model = PaymentLinkModel.fromJson(jsonWithInvalidStrings);

      // Assert - Should use default values for invalid strings
      expect(model.amount, equals(0.0)); // Should default to 0.0
      expect(model.createdTime, equals(0)); // Should default to 0
      expect(model.expiresAt, equals(0)); // Should default to 0
      expect(model.sendEmail, isTrue); // Should use default value (true)
      expect(model.sendSms, isFalse); // Should use default value (false)
      expect(model.partialPayments, isFalse); // Should use default value (false)
    });

    test('should handle null and missing values correctly', () {
      // Arrange - API response with minimal data
      final jsonWithNulls = {
        'payment_link_id': 'pl_test_minimal',
        'payment_link_url': 'https://payment.example.com/pay/pl_test_minimal',
        'transaction_id': 'txn_test_minimal',
        // Missing amount, created_time, etc.
      };

      // Act
      final model = PaymentLinkModel.fromJson(jsonWithNulls);

      // Assert - Should use default values
      expect(model.paymentLinkId, equals('pl_test_minimal'));
      expect(model.paymentLinkUrl, equals('https://payment.example.com/pay/pl_test_minimal'));
      expect(model.amount, equals(0.0)); // Default
      expect(model.currency, equals('INR')); // Default
      expect(model.description, equals('')); // Default
      expect(model.customerEmail, equals('')); // Default
      expect(model.status, equals('')); // Default
      expect(model.createdTime, equals(0)); // Default
      expect(model.expiresAt, isNull); // Should be null when not provided
      expect(model.transactionId, equals('txn_test_minimal'));
      expect(model.sendEmail, isTrue); // Default
      expect(model.sendSms, isFalse); // Default
      expect(model.partialPayments, isFalse); // Default
    });

    test('should handle edge case boolean values', () {
      // Arrange - API response with various boolean representations
      final jsonWithBooleanEdgeCases = {
        'payment_link_id': 'pl_test_bool',
        'payment_link_url': 'https://payment.example.com/pay/pl_test_bool',
        'amount': '15.75',
        'transaction_id': 'txn_test_bool',
        'send_email': '1', // String "1" should be true
        'send_sms': '0', // String "0" should be false
        'partial_payments': 'TRUE', // Uppercase string should be true
      };

      // Act
      final model = PaymentLinkModel.fromJson(jsonWithBooleanEdgeCases);

      // Assert
      expect(model.sendEmail, isTrue); // String "1" should be true
      expect(model.sendSms, isFalse); // String "0" should be false
      expect(model.partialPayments, isTrue); // String "TRUE" should be true
    });
  });
}
