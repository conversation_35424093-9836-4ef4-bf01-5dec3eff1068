import 'package:aquapartner/presentation/cubit/customer/customer_cubit.dart';
import 'package:aquapartner/presentation/cubit/dues/dues_cubit.dart';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:aquapartner/presentation/cubit/invoices/invoices_cubit.dart';
import 'package:aquapartner/presentation/cubit/my_farmers/farmer_visits_cubit.dart';
import 'package:aquapartner/presentation/cubit/sales_order/sales_order_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../injection_container.dart' as di;
import '../cubit/account_statement/account_statement_cubit.dart';
import '../cubit/auth/auth_cubit.dart';
import '../cubit/auth/auth_state.dart';
import '../cubit/billing_and_payments/billing_and_payments_cubit.dart';
import '../cubit/connectivity/connectivity_cubit.dart';
import '../cubit/credit_notes/credit_notes_cubit.dart';
import '../cubit/dashboard/dashboard_cubit.dart';
import '../cubit/interested_product/interested_product_cubit.dart';
import '../cubit/location/location_cubit.dart';
import '../cubit/navigation/navigation_cubit.dart';
import '../cubit/payment/payment_cubit.dart';
import '../cubit/payments/customer_payments_cubit.dart';
import '../cubit/price_list/price_list_cubit.dart';
import '../cubit/product_catalogue/product_catalogue_cubit.dart';
import '../cubit/scheme/scheme_cubit.dart';
import '../cubit/smr_report/smr_report_cubit.dart';
import '../cubit/update_checker/update_checker_cubit.dart';
import 'auth_state_handler.dart';

class AppProviders extends StatelessWidget {
  final Widget child;

  const AppProviders({required this.child, super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>(
          create: (context) => di.sl<AuthCubit>()..checkAuthStatus(),
        ),
        BlocProvider(create: (context) => di.sl<LocationCubit>()),
        BlocProvider(create: (context) => di.sl<HomeCubit>()..loadData()),
        BlocProvider<ConnectivityCubit>(
          create: (context) => di.sl<ConnectivityCubit>(),
          lazy: true,
        ),
        BlocProvider<NavigationCubit>(
          create: (context) => di.sl<NavigationCubit>(),
        ),
        BlocProvider(
          create: (context) => di.sl<PriceListCubit>()..loadPriceLists(),
        ),
        BlocProvider(
          create: (context) {
            final cubit = di.sl<ProductCatalogueCubit>();
            // Load products immediately when cubit is created
            WidgetsBinding.instance.addPostFrameCallback((_) {
              cubit.loadProductCatalogues();
            });
            return cubit;
          },
        ),
        BlocProvider(
          create:
              (context) =>
                  di.sl<InterestedProductCubit>()
                    ..syncInterestedProductsUseCase(),
        ),
        BlocProvider<UpdateCheckerCubit>(
          create: (context) => di.sl<UpdateCheckerCubit>(),
        ),
        // Add Dashboard Cubit
        BlocProvider<DashboardCubit>(
          create: (context) {
            final cubit = di.sl<DashboardCubit>();
            // Only load dashboard data after the widget tree is built and customer data is available
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // Ensure customer data is loaded before loading dashboard
              final authCubit = context.read<AuthCubit>();
              final state = authCubit.state;
              if (state is AuthSuccess) {
                cubit.loadDashboardData();
              }
            });
            return cubit;
          },
          lazy: false, // Set to false to ensure it's created immediately
        ),
        BlocProvider<CustomerCubit>(
          create: (context) => di.sl<CustomerCubit>()..loadCustomer(),
          lazy: true,
        ),
        BlocProvider<SchemeCubit>(
          create: (context) => di.sl<SchemeCubit>()..loadScheme(),
          lazy: true,
        ),
        BlocProvider<AccountStatementCubit>(
          create: (context) => di.sl<AccountStatementCubit>(),
          lazy: true,
        ),
        BlocProvider<FarmerVisitsCubit>(
          create: (context) => di.sl<FarmerVisitsCubit>(),
          lazy: true,
        ),
        BlocProvider<SMRReportCubit>(
          create: (context) => di.sl<SMRReportCubit>(),
          lazy: true,
        ),
        BlocProvider<BillingAndPaymentsPageCubit>(
          create: (context) => di.sl<BillingAndPaymentsPageCubit>(),
          lazy: true,
        ),
        BlocProvider<SalesOrderCubit>(
          create: (context) => di.sl<SalesOrderCubit>(),
          lazy: true,
        ),
        BlocProvider<DuesCubit>(
          create: (context) => di.sl<DuesCubit>(),
          lazy: true,
        ),
        BlocProvider<InvoicesCubit>(
          create: (context) {
            final cubit = di.sl<InvoicesCubit>();
            // Only load invoices after the widget tree is built and customer data is available
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // Ensure customer data is loaded before loading invoices
              final authCubit = context.read<AuthCubit>();
              final state = authCubit.state;
              if (state is AuthSuccess) {
                cubit.loadInvoices();
              }
            });
            return cubit;
          },
          lazy: false, // Set to false to ensure it's created immediately
        ),
        BlocProvider<CustomerPaymentsCubit>(
          create: (context) => di.sl<CustomerPaymentsCubit>(),
          lazy: true,
        ),
        BlocProvider<PaymentCubit>(
          create: (context) => di.sl<PaymentCubit>(),
          lazy: true,
        ),
        BlocProvider<CreditNotesCubit>(
          create: (context) => di.sl<CreditNotesCubit>(),
          lazy: true,
        ),
      ],
      child: AuthStateHandler(child: child),
    );
  }
}
