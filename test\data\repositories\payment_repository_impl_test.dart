import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:dartz/dartz.dart';

import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/data/repositories/payment_repository_impl.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_link.dart';
import 'package:aquapartner/domain/entities/payments/payment_result.dart';
import 'package:aquapartner/data/models/payment/payment_link_model.dart';

import 'payment_repository_impl_test.mocks.dart';

@GenerateMocks([PaymentRemoteDataSource, AppLogger])
void main() {
  late PaymentRepositoryImpl repository;
  late MockPaymentRemoteDataSource mockRemoteDataSource;
  late MockAppLogger mockLogger;

  setUp(() {
    mockRemoteDataSource = MockPaymentRemoteDataSource();
    mockLogger = MockAppLogger();
    repository = PaymentRepositoryImpl(
      remoteDataSource: mockRemoteDataSource,
      logger: mockLogger,
    );
  });

  group('PaymentRepositoryImpl', () {
    const tPaymentRequest = PaymentRequest(
      amount: 100.0,
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      currency: 'INR',
    );

    final tCreatedTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final tPaymentLinkModel = PaymentLinkModel(
      paymentLinkId: 'test_link_id',
      paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
      amount: 100.0,
      currency: 'INR',
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      status: 'active',
      createdTime: tCreatedTime,
      transactionId: 'test_transaction_id',
    );

    final tPaymentLink = PaymentLink(
      paymentLinkId: 'test_link_id',
      paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
      amount: 100.0,
      currency: 'INR',
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      status: 'active',
      createdTime: DateTime.fromMillisecondsSinceEpoch(tCreatedTime * 1000),
      transactionId: 'test_transaction_id',
    );

    group('createPaymentLink', () {
      test(
        'should return PaymentLink when remote data source succeeds',
        () async {
          // arrange
          when(
            mockRemoteDataSource.createPaymentLink(any),
          ).thenAnswer((_) async => tPaymentLinkModel);

          // act
          final result = await repository.createPaymentLink(tPaymentRequest);

          // assert
          expect(result, equals(Right(tPaymentLink)));
          verify(mockRemoteDataSource.createPaymentLink(any));
          verify(
            mockLogger.i(
              'Creating payment link for: ${tPaymentRequest.customerEmail}',
            ),
          );
          verify(
            mockLogger.i(
              'Payment link created successfully: ${tPaymentLink.paymentLinkId}',
            ),
          );
        },
      );

      test(
        'should return ServerFailure when remote data source throws ServerException',
        () async {
          // arrange
          when(
            mockRemoteDataSource.createPaymentLink(any),
          ).thenThrow(ServerException());

          // act
          final result = await repository.createPaymentLink(tPaymentRequest);

          // assert
          expect(result, equals(Left(ServerFailure())));
          verify(mockRemoteDataSource.createPaymentLink(any));
          verify(
            mockLogger.e('Failed to create payment link: ServerException'),
          );
        },
      );

      test(
        'should return NetworkFailure when remote data source throws NetworkException',
        () async {
          // arrange
          when(
            mockRemoteDataSource.createPaymentLink(any),
          ).thenThrow(NetworkException());

          // act
          final result = await repository.createPaymentLink(tPaymentRequest);

          // assert
          expect(result, equals(Left(NetworkFailure())));
          verify(mockRemoteDataSource.createPaymentLink(any));
          verify(
            mockLogger.e('Failed to create payment link: NetworkException'),
          );
        },
      );

      test(
        'should return UnexpectedFailure when remote data source throws unexpected exception',
        () async {
          // arrange
          when(
            mockRemoteDataSource.createPaymentLink(any),
          ).thenThrow(Exception('Unexpected error'));

          // act
          final result = await repository.createPaymentLink(tPaymentRequest);

          // assert
          expect(result, equals(Left(UnexpectedFailure('Unexpected error'))));
          verify(mockRemoteDataSource.createPaymentLink(any));
          verify(
            mockLogger.e(
              'Failed to create payment link: Exception: Unexpected error',
            ),
          );
        },
      );
    });

    group('createPaymentSession', () {
      test(
        'should return PaymentLink when remote data source succeeds',
        () async {
          // arrange
          when(
            mockRemoteDataSource.createPaymentSession(any),
          ).thenAnswer((_) async => tPaymentLinkModel);

          // act
          final result = await repository.createPaymentSession(tPaymentRequest);

          // assert
          expect(result, equals(Right(tPaymentLink)));
          verify(mockRemoteDataSource.createPaymentSession(any));
          verify(
            mockLogger.i(
              'Creating payment session for: ${tPaymentRequest.customerEmail}',
            ),
          );
          verify(
            mockLogger.i(
              'Payment session created successfully: ${tPaymentLink.paymentLinkId}',
            ),
          );
        },
      );

      test(
        'should return ServerFailure when remote data source throws ServerException',
        () async {
          // arrange
          when(
            mockRemoteDataSource.createPaymentSession(any),
          ).thenThrow(ServerException());

          // act
          final result = await repository.createPaymentSession(tPaymentRequest);

          // assert
          expect(result, equals(Left(ServerFailure())));
          verify(mockRemoteDataSource.createPaymentSession(any));
          verify(
            mockLogger.e('Failed to create payment session: ServerException'),
          );
        },
      );
    });

    group('validatePaymentUrl', () {
      test('should return true for valid Zoho payment URLs', () {
        // arrange
        const validUrl = 'https://payments.zoho.in/checkout/test_link_id';

        // act
        final result = repository.validatePaymentUrl(validUrl);

        // assert
        expect(result, true);
      });

      test(
        'should return true for valid Zoho payment URLs with different subdomains',
        () {
          // arrange
          const validUrl = 'https://payments.zoho.com/checkout/test_link_id';

          // act
          final result = repository.validatePaymentUrl(validUrl);

          // assert
          expect(result, true);
        },
      );

      test('should return false for invalid URLs', () {
        // arrange
        const invalidUrl = 'https://malicious-site.com/fake-payment';

        // act
        final result = repository.validatePaymentUrl(invalidUrl);

        // assert
        expect(result, false);
      });

      test('should return false for non-HTTPS URLs', () {
        // arrange
        const invalidUrl = 'http://payments.zoho.in/checkout/test_link_id';

        // act
        final result = repository.validatePaymentUrl(invalidUrl);

        // assert
        expect(result, false);
      });

      test('should return false for empty or null URLs', () {
        // act & assert
        expect(repository.validatePaymentUrl(''), false);
        expect(repository.validatePaymentUrl('   '), false);
      });
    });

    group('parsePaymentResult', () {
      test('should parse successful payment result from callback URL', () {
        // arrange
        const callbackUrl =
            'https://example.com/callback?'
            'status=success&'
            'payment_id=pay_123&'
            'invoice_number=INV-001&'
            'amount=100.0&'
            'payment_link_id=link_123';

        // act
        final result = repository.parsePaymentResult(callbackUrl);

        // assert
        expect(result.status, PaymentStatus.success);
        expect(result.paymentId, 'pay_123');
        expect(result.invoiceNumber, 'INV-001');
        expect(result.amount, 100.0);
        expect(result.paymentLinkId, 'link_123');
      });

      test('should parse failed payment result from callback URL', () {
        // arrange
        const callbackUrl =
            'https://example.com/callback?'
            'status=failed&'
            'error_message=Payment declined&'
            'payment_link_id=link_123';

        // act
        final result = repository.parsePaymentResult(callbackUrl);

        // assert
        expect(result.status, PaymentStatus.failed);
        expect(result.errorMessage, 'Payment declined');
        expect(result.paymentLinkId, 'link_123');
      });

      test('should parse cancelled payment result from callback URL', () {
        // arrange
        const callbackUrl =
            'https://example.com/callback?'
            'status=cancelled&'
            'payment_link_id=link_123';

        // act
        final result = repository.parsePaymentResult(callbackUrl);

        // assert
        expect(result.status, PaymentStatus.cancelled);
        expect(result.paymentLinkId, 'link_123');
      });

      test('should return unknown status for invalid callback URL', () {
        // arrange
        const callbackUrl = 'https://example.com/callback?invalid=params';

        // act
        final result = repository.parsePaymentResult(callbackUrl);

        // assert
        expect(result.status, PaymentStatus.unknown);
      });

      test('should handle malformed callback URLs gracefully', () {
        // arrange
        const callbackUrl = 'not-a-valid-url';

        // act
        final result = repository.parsePaymentResult(callbackUrl);

        // assert
        expect(result.status, PaymentStatus.unknown);
      });
    });
  });
}
