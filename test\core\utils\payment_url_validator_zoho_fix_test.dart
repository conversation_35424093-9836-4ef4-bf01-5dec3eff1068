import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/core/utils/payment_url_validator.dart';

/// Test suite to verify the PaymentUrlValidator fix for Zoho payment links
/// 
/// This test validates that the PaymentUrlValidator correctly validates
/// Zoho payment link URLs with the /paymentlinks/ path after the fix.
void main() {
  group('PaymentUrlValidator Zoho Payment Links Fix', () {
    test('should validate the actual Zoho payment link URL that was failing', () {
      // This is the exact URL that was causing the "Invalid payment URL" error
      const actualZohoPaymentLinkUrl = 'https://payments.zoho.in/paymentlinks/bcb708a8468d038aed8d34bf9f8db01c1bdc47ccef85c8e77c2e7f1ad67f9bfdd188810a474787a2d6d0448d99dd596c0865a135fe8728766601203e0935ab98';
      
      final isValid = PaymentUrlValidator.isValidPaymentUrl(actualZohoPaymentLinkUrl);
      
      expect(isValid, isTrue, reason: 'The actual Zoho payment link URL should be valid after adding /paymentlinks/ to allowed paths');
    });

    test('should validate various Zoho payment link URL formats', () {
      const zohoPaymentLinkUrls = [
        'https://payments.zoho.in/paymentlinks/short_hash',
        'https://payments.zoho.in/paymentlinks/another_long_hash_example_123456789abcdef',
        'https://payments.zoho.in/paymentlinks/test_payment_link_id_12345',
        'https://payments.zoho.in/paymentlinks/bcb708a8468d038aed8d34bf9f8db01c1bdc47ccef85c8e77c2e7f1ad67f9bfdd188810a474787a2d6d0448d99dd596c0865a135fe8728766601203e0935ab98',
      ];
      
      for (final url in zohoPaymentLinkUrls) {
        final isValid = PaymentUrlValidator.isValidPaymentUrl(url);
        expect(isValid, isTrue, reason: 'Should validate Zoho payment link URL: $url');
      }
    });

    test('should still validate existing allowed paths', () {
      const existingAllowedUrls = [
        'https://payments.zoho.in/checkout/session_123',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/success',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/failed',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/payment/callback',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/payment-page',
      ];
      
      for (final url in existingAllowedUrls) {
        final isValid = PaymentUrlValidator.isValidPaymentUrl(url);
        expect(isValid, isTrue, reason: 'Should still validate existing allowed URL: $url');
      }
    });

    test('should reject URLs with invalid domains', () {
      const invalidDomainUrls = [
        'https://malicious.com/paymentlinks/test_hash',
        'https://fake-payments.zoho.in/paymentlinks/test_hash',
        'https://payments.zoho.com/paymentlinks/test_hash', // .com instead of .in
      ];
      
      for (final url in invalidDomainUrls) {
        final isValid = PaymentUrlValidator.isValidPaymentUrl(url);
        expect(isValid, isFalse, reason: 'Should reject URL with invalid domain: $url');
      }
    });

    test('should reject URLs with invalid schemes', () {
      const invalidSchemeUrls = [
        'http://payments.zoho.in/paymentlinks/test_hash', // HTTP instead of HTTPS
        'ftp://payments.zoho.in/paymentlinks/test_hash',
        'file://payments.zoho.in/paymentlinks/test_hash',
      ];
      
      for (final url in invalidSchemeUrls) {
        final isValid = PaymentUrlValidator.isValidPaymentUrl(url);
        expect(isValid, isFalse, reason: 'Should reject URL with invalid scheme: $url');
      }
    });

    test('should reject URLs with invalid paths', () {
      const invalidPathUrls = [
        'https://payments.zoho.in/invalid/path',
        'https://payments.zoho.in/malicious/endpoint',
        'https://payments.zoho.in/admin/panel',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/admin/users',
      ];
      
      for (final url in invalidPathUrls) {
        final isValid = PaymentUrlValidator.isValidPaymentUrl(url);
        expect(isValid, isFalse, reason: 'Should reject URL with invalid path: $url');
      }
    });

    test('should handle edge cases gracefully', () {
      const edgeCaseUrls = [
        '', // Empty string
        'not-a-url',
        'https://',
        'https://payments.zoho.in',
        'https://payments.zoho.in/',
      ];
      
      for (final url in edgeCaseUrls) {
        final isValid = PaymentUrlValidator.isValidPaymentUrl(url);
        expect(isValid, isFalse, reason: 'Should handle edge case gracefully: "$url"');
      }
    });
  });

  group('PaymentUrlValidator Completion URL Detection', () {
    test('should detect completion URLs correctly', () {
      const completionUrls = [
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/success',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/payment/failed',
      ];
      
      for (final url in completionUrls) {
        final isCompletion = PaymentUrlValidator.isPaymentCompletionUrl(url);
        expect(isCompletion, isTrue, reason: 'Should detect completion URL: $url');
      }
    });

    test('should not detect non-completion URLs as completion URLs', () {
      const nonCompletionUrls = [
        'https://payments.zoho.in/paymentlinks/test_hash',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/checkout',
        'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/payment/create',
      ];
      
      for (final url in nonCompletionUrls) {
        final isCompletion = PaymentUrlValidator.isPaymentCompletionUrl(url);
        expect(isCompletion, isFalse, reason: 'Should not detect as completion URL: $url');
      }
    });
  });
}
