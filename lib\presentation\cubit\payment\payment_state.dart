import 'package:equatable/equatable.dart';
import '../../../domain/entities/payments/payment_link.dart';
import '../../../domain/entities/payments/payment_result.dart';

/// Base state for payment operations
abstract class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

/// Initial state when payment cubit is created
class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

/// State when creating a payment link
class PaymentLinkCreating extends PaymentState {
  final double amount;
  final String description;
  final String customerEmail;

  const PaymentLinkCreating({
    required this.amount,
    required this.description,
    required this.customerEmail,
  });

  @override
  List<Object?> get props => [amount, description, customerEmail];
}

/// State when payment link is created successfully
class PaymentLinkCreated extends PaymentState {
  final PaymentLink paymentLink;
  final DateTime createdAt;

  const PaymentLinkCreated({
    required this.paymentLink,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [paymentLink, createdAt];
}

/// State when payment is being processed in WebView
class PaymentProcessing extends PaymentState {
  final PaymentLink paymentLink;
  final String currentUrl;
  final bool isLoading;

  const PaymentProcessing({
    required this.paymentLink,
    required this.currentUrl,
    this.isLoading = true,
  });

  PaymentProcessing copyWith({
    PaymentLink? paymentLink,
    String? currentUrl,
    bool? isLoading,
  }) {
    return PaymentProcessing(
      paymentLink: paymentLink ?? this.paymentLink,
      currentUrl: currentUrl ?? this.currentUrl,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [paymentLink, currentUrl, isLoading];
}

/// State when payment is completed (success or failure)
class PaymentCompleted extends PaymentState {
  final PaymentResult result;
  final PaymentLink? originalPaymentLink;
  final DateTime completedAt;

  const PaymentCompleted({
    required this.result,
    this.originalPaymentLink,
    required this.completedAt,
  });

  /// Check if payment was successful
  bool get isSuccess => result.isSuccess;

  /// Check if payment failed
  bool get isFailed => result.isFailed;

  /// Check if payment was cancelled
  bool get isCancelled => result.isCancelled;

  @override
  List<Object?> get props => [result, originalPaymentLink, completedAt];
}

/// State when payment operation fails
class PaymentError extends PaymentState {
  final String message;
  final String? errorCode;
  final PaymentLink? paymentLink;
  final DateTime errorAt;

  const PaymentError({
    required this.message,
    this.errorCode,
    this.paymentLink,
    required this.errorAt,
  });

  @override
  List<Object?> get props => [message, errorCode, paymentLink, errorAt];
}

/// State when payment is cancelled by user
class PaymentCancelled extends PaymentState {
  final PaymentLink? paymentLink;
  final String reason;
  final DateTime cancelledAt;

  const PaymentCancelled({
    this.paymentLink,
    this.reason = 'User cancelled',
    required this.cancelledAt,
  });

  @override
  List<Object?> get props => [paymentLink, reason, cancelledAt];
}

/// State when validating payment URL
class PaymentUrlValidating extends PaymentState {
  final String url;

  const PaymentUrlValidating({required this.url});

  @override
  List<Object?> get props => [url];
}

/// State when payment URL validation is complete
class PaymentUrlValidated extends PaymentState {
  final String url;
  final bool isValid;
  final String? errorMessage;

  const PaymentUrlValidated({
    required this.url,
    required this.isValid,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [url, isValid, errorMessage];
}

/// State when payment timeout occurs
class PaymentTimeout extends PaymentState {
  final PaymentLink? paymentLink;
  final Duration timeoutDuration;
  final DateTime timeoutAt;

  const PaymentTimeout({
    this.paymentLink,
    required this.timeoutDuration,
    required this.timeoutAt,
  });

  @override
  List<Object?> get props => [paymentLink, timeoutDuration, timeoutAt];
}
