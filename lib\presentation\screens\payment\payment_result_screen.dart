import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import '../../../core/mixins/analytics_mixin.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/utils/currency_formatter.dart';
import '../../../domain/entities/payments/payment_result.dart';
import '../../../injection_container.dart' as di;

/// Screen for displaying payment results
class PaymentResultScreen extends StatefulWidget {
  final PaymentResult paymentResult;
  final VoidCallback? onContinue;
  final VoidCallback? onRetry;

  const PaymentResultScreen({
    super.key,
    required this.paymentResult,
    this.onContinue,
    this.onRetry,
  });

  @override
  State<PaymentResultScreen> createState() => _PaymentResultScreenState();
}

class _PaymentResultScreenState extends State<PaymentResultScreen>
    with AnalyticsMixin {
  @override
  String get screenName => 'payment_result_screen';

  @override
  AnalyticsService get analytics => di.sl<AnalyticsService>();

  @override
  void initState() {
    super.initState();
    _trackScreenView();
  }

  void _trackScreenView() {
    trackEvent(
      'payment_result_screen_viewed',
      params: {
        'status': widget.paymentResult.status.toString(),
        'payment_id': widget.paymentResult.paymentId ?? '',
        'amount': widget.paymentResult.amount?.toString() ?? '',
      },
    );
  }

  void _handleContinue() {
    trackEvent(
      'payment_result_continue_clicked',
      params: {'status': widget.paymentResult.status.toString()},
    );

    if (widget.onContinue != null) {
      widget.onContinue!();
    } else {
      Navigator.of(context).pop();
    }
  }

  void _handleRetry() {
    trackEvent(
      'payment_result_retry_clicked',
      params: {'status': widget.paymentResult.status.toString()},
    );

    if (widget.onRetry != null) {
      widget.onRetry!();
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: AquaText.headline('Payment Result'),
        backgroundColor: _getAppBarColor(),
        foregroundColor: acWhiteColor,
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildStatusIcon(),
                      const SizedBox(height: 24),
                      _buildStatusTitle(),
                      const SizedBox(height: 16),
                      _buildStatusMessage(),
                      const SizedBox(height: 32),
                      _buildPaymentDetails(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Color _getAppBarColor() {
    switch (widget.paymentResult.status) {
      case PaymentStatus.success:
        return Colors.green;
      case PaymentStatus.failed:
        return Colors.red;
      case PaymentStatus.cancelled:
        return Colors.orange;
      case PaymentStatus.unknown:
      default:
        return Colors.grey;
    }
  }

  Widget _buildStatusIcon() {
    IconData iconData;
    Color iconColor;

    switch (widget.paymentResult.status) {
      case PaymentStatus.success:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case PaymentStatus.failed:
        iconData = Icons.error;
        iconColor = Colors.red;
        break;
      case PaymentStatus.cancelled:
        iconData = Icons.cancel;
        iconColor = Colors.orange;
        break;
      case PaymentStatus.unknown:
      default:
        iconData = Icons.help;
        iconColor = Colors.grey;
    }

    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: iconColor.withOpacity(0.1),
        border: Border.all(color: iconColor.withOpacity(0.3), width: 2),
      ),
      child: Icon(iconData, size: 60, color: iconColor),
    );
  }

  Widget _buildStatusTitle() {
    String title;
    switch (widget.paymentResult.status) {
      case PaymentStatus.success:
        title = 'Payment Successful!';
        break;
      case PaymentStatus.failed:
        title = 'Payment Failed';
        break;
      case PaymentStatus.cancelled:
        title = 'Payment Cancelled';
        break;
      case PaymentStatus.unknown:
      default:
        title = 'Payment Status Unknown';
    }

    return AquaText.subheading(
      title,
      weight: AquaFontWeight.bold,
      textAlign: TextAlign.center,
    );
  }

  Widget _buildStatusMessage() {
    String message;
    switch (widget.paymentResult.status) {
      case PaymentStatus.success:
        message =
            'Your payment has been processed successfully. '
            'You will receive a confirmation email shortly.';
        break;
      case PaymentStatus.failed:
        message =
            widget.paymentResult.errorMessage ??
            'Your payment could not be processed. Please try again.';
        break;
      case PaymentStatus.cancelled:
        message = 'The payment was cancelled. You can try again if needed.';
        break;
      case PaymentStatus.unknown:
      default:
        message =
            'We could not determine the status of your payment. '
            'Please check your payment history or contact support.';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: AquaText.body(
        message,
        textAlign: TextAlign.center,
        color: Colors.grey.shade700,
      ),
    );
  }

  Widget _buildPaymentDetails() {
    if (widget.paymentResult.paymentId == null &&
        widget.paymentResult.amount == null &&
        widget.paymentResult.invoiceNumber == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AquaText.headline('Payment Details', weight: AquaFontWeight.bold),
            const SizedBox(height: 16),
            if (widget.paymentResult.paymentId != null) ...[
              _buildDetailRow('Payment ID', widget.paymentResult.paymentId!),
              const SizedBox(height: 8),
            ],
            if (widget.paymentResult.amount != null) ...[
              _buildDetailRow(
                'Amount',
                CurrencyFormatter.formatAsINR(widget.paymentResult.amount!),
              ),
              const SizedBox(height: 8),
            ],
            if (widget.paymentResult.invoiceNumber != null) ...[
              _buildDetailRow('Invoice', widget.paymentResult.invoiceNumber!),
              const SizedBox(height: 8),
            ],
            if (widget.paymentResult.paymentLinkId != null) ...[
              _buildDetailRow('Reference', widget.paymentResult.paymentLinkId!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: AquaText.caption('$label:', color: Colors.grey.shade600),
        ),
        Expanded(
          child: AquaText.body(
            value,
            weight: AquaFontWeight.medium,
            color: label == 'Amount' ? acPrimaryBlue : acBlackColor,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final isSuccess = widget.paymentResult.isSuccess;
    final isFailed = widget.paymentResult.isFailed;

    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Column(
        children: [
          if (isFailed && widget.onRetry != null) ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _handleRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: acPrimaryBlue,
                  foregroundColor: acWhiteColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: AquaText.body(
                  'Try Again',
                  weight: AquaFontWeight.bold,
                  color: acWhiteColor,
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _handleContinue,
              style: ElevatedButton.styleFrom(
                backgroundColor: isSuccess ? Colors.green : Colors.grey,
                foregroundColor: acWhiteColor,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: AquaText.body(
                isSuccess ? 'Continue' : 'Close',
                weight: AquaFontWeight.bold,
                color: acWhiteColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
