import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';

/// Comprehensive Payment API Test Suite
/// Tests all payment endpoints with various scenarios and edge cases
void main() {
  group('Comprehensive Payment API Tests', () {
    late Dio dio;
    String? createdPaymentLinkId;

    setUpAll(() {
      dio = Dio();
      dio.options.baseUrl =
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);
    });

    group('Payment Link Creation Tests', () {
      test(
        'should create payment link with valid data',
        () async {
          final requestData = {
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Comprehensive test payment',
            'customer_email': '<EMAIL>',
            'redirect_url': 'https://example.com/callback',
            'meta_data': [
              {'key': 'source', 'value': 'flutter_app'},
              {'key': 'test_type', 'value': 'comprehensive_test'},
            ],
            'send_email': true,
            'send_sms': false,
            'partial_payments': false,
          };

          try {
            final response = await dio.post(
              '/api/zoho/payments/create-link',
              data: requestData,
            );

            // Validate status code
            expect(response.statusCode, equals(201));

            // Validate response structure
            expect(response.data, isA<Map<String, dynamic>>());
            final data = response.data as Map<String, dynamic>;

            // Validate success response
            expect(data['success'], isTrue);
            expect(data['message'], isNotEmpty);
            expect(data['data'], isNotNull);

            // Validate payment data
            final paymentData = data['data'] as Map<String, dynamic>;
            expect(paymentData['payment_link_id'], isNotEmpty);
            expect(paymentData['amount'], equals('100.00'));
            expect(paymentData['currency'], equals('INR'));
            expect(
              paymentData['description'],
              equals('Comprehensive test payment'),
            );
            expect(paymentData['status'], equals('active'));
            expect(paymentData['created_time'], isA<int>());
            expect(paymentData['transaction_id'], isNotEmpty);

            // Validate payment link structure
            expect(data['payment_link'], isNotNull);
            final paymentLink = data['payment_link'] as Map<String, dynamic>;
            expect(paymentLink['url'], startsWith('https://payments.zoho.in/'));
            expect(
              paymentLink['email'],
              equals('<EMAIL>'),
            );
            expect(
              paymentLink['return_url'],
              equals('https://example.com/callback'),
            );

            // Store for later tests
            createdPaymentLinkId = paymentData['payment_link_id'] as String?;
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return; // Skip if network issue
            }
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );

      test(
        'should handle different currencies',
        () async {
          final currencies = ['INR', 'USD', 'EUR'];

          for (final currency in currencies) {
            final paymentData = {
              'amount': 50.0,
              'currency': currency,
              'description': 'Currency test for $currency',
              'customer_email': '<EMAIL>',
              'redirect_url': 'https://example.com/callback',
              'meta_data': [
                {'key': 'currency_test', 'value': currency},
              ],
            };

            try {
              final response = await dio.post(
                '/api/zoho/payments/create-link',
                data: paymentData,
              );

              if (response.statusCode == 201) {
                final data = response.data as Map<String, dynamic>;
                expect(data['success'], isTrue);
                final paymentData = data['data'] as Map<String, dynamic>;
                expect(paymentData['currency'], equals(currency));
              }
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue; // Skip if network issue
              }
              // Some currencies might not be supported, that's okay
              if (e is DioException && e.response?.statusCode == 400) {
                continue;
              }
              rethrow;
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 3)),
      );

      test(
        'should validate email formats',
        () async {
          final validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
          ];

          for (final email in validEmails) {
            final paymentData = {
              'amount': 25.0,
              'currency': 'INR',
              'description': 'Email validation test',
              'customer_email': email,
              'redirect_url': 'https://example.com/callback',
            };

            try {
              final response = await dio.post(
                '/api/zoho/payments/create-link',
                data: paymentData,
              );
              expect(response.statusCode, equals(201));

              final data = response.data as Map<String, dynamic>;
              expect(data['success'], isTrue);
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }
              rethrow;
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );

      test(
        'should reject invalid email formats',
        () async {
          final invalidEmails = ['invalid-email', '@domain.com', 'user@', ''];

          for (final email in invalidEmails) {
            final paymentData = {
              'amount': 25.0,
              'currency': 'INR',
              'description': 'Invalid email test',
              'customer_email': email,
              'redirect_url': 'https://example.com/callback',
            };

            try {
              final response = await dio.post(
                '/api/zoho/payments/create-link',
                data: paymentData,
              );

              // If it succeeds, check if it's marked as failed
              if (response.statusCode == 201) {
                final data = response.data as Map<String, dynamic>;
                if (data['success'] == true) {
                  fail('Expected validation error for invalid email: $email');
                }
              }
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }
              // Expected to fail with 400 Bad Request
              if (e is DioException) {
                expect(e.response?.statusCode, equals(400));
              }
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );

      test(
        'should validate amount ranges',
        () async {
          final testAmounts = [
            {'amount': 0.01, 'shouldSucceed': true},
            {'amount': 1.0, 'shouldSucceed': true},
            {'amount': 100000.0, 'shouldSucceed': true},
            {'amount': 0.0, 'shouldSucceed': false},
            {'amount': -10.0, 'shouldSucceed': false},
          ];

          for (final testCase in testAmounts) {
            final paymentData = {
              'amount': testCase['amount'],
              'currency': 'INR',
              'description': 'Amount validation test',
              'customer_email': '<EMAIL>',
              'redirect_url': 'https://example.com/callback',
            };

            try {
              final response = await dio.post(
                '/api/zoho/payments/create-link',
                data: paymentData,
              );

              if (testCase['shouldSucceed'] == true) {
                expect(response.statusCode, equals(201));
                final data = response.data as Map<String, dynamic>;
                expect(data['success'], isTrue);
              } else {
                fail(
                  'Expected validation error for amount: ${testCase['amount']}',
                );
              }
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }

              if (testCase['shouldSucceed'] == false) {
                // Expected to fail
                if (e is DioException) {
                  expect(e.response?.statusCode, equals(400));
                }
              } else {
                rethrow;
              }
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
    });

    group('Payment Session Tests', () {
      test(
        'should create payment session with required fields',
        () async {
          final sessionData = {
            'amount': 250.0,
            'currency': 'INR',
            'description': 'Payment session test',
            'customer_email': '<EMAIL>',
            'redirect_url': 'https://example.com/session-callback',
            'invoiceNo': 'SESSION-${DateTime.now().millisecondsSinceEpoch}',
            'customerId': 'test_customer_session',
          };

          try {
            final response = await dio.post(
              '/api/zoho/payments/create-session',
              data: sessionData,
            );

            expect(response.statusCode, anyOf([200, 201]));

            final data = response.data as Map<String, dynamic>;
            expect(data['success'], isTrue);
            expect(data['data'], isNotNull);

            final sessionInfo = data['data'] as Map<String, dynamic>;
            expect(sessionInfo['payment_link_id'], isNotEmpty);
            expect(sessionInfo['payment_link_url'], isNotEmpty);
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return;
            }
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
    });

    group('Payment Status Tests', () {
      test(
        'should get payment status for valid payment ID',
        () async {
          // Skip if we don't have a created payment ID
          if (createdPaymentLinkId == null) {
            return;
          }

          try {
            final response = await dio.get(
              '/api/payment/status/$createdPaymentLinkId',
            );

            expect(response.statusCode, equals(200));

            final data = response.data as Map<String, dynamic>;
            expect(data['success'], isTrue);
            expect(data['data'], isNotNull);
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return;
            }
            // Payment might not be found immediately, that's okay
            if (e is DioException && e.response?.statusCode == 404) {
              return;
            }
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );

      test(
        'should return 404 for non-existent payment ID',
        () async {
          const fakePaymentId = 'non_existent_payment_id_12345';

          try {
            final response = await dio.get(
              '/api/payment/status/$fakePaymentId',
            );

            // If it doesn't throw, it should return an error response
            expect(response.statusCode, anyOf([404, 400]));
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return;
            }

            // Expected to fail with 404
            if (e is DioException) {
              expect(e.response?.statusCode, equals(404));
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );
    });

    group('Error Handling Tests', () {
      test(
        'should handle missing required fields',
        () async {
          final incompleteData = {
            'amount': 100.0,
            // Missing description, customer_email, etc.
          };

          try {
            await dio.post(
              '/api/zoho/payments/create-link',
              data: incompleteData,
            );

            // Should not succeed
            fail('Expected validation error for incomplete data');
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return;
            }

            if (e is DioException) {
              expect(e.response?.statusCode, equals(400));

              final errorData = e.response?.data;
              if (errorData is Map) {
                expect(errorData['error'], isNotNull);
                expect(errorData['required_fields'], isNotNull);
              }
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );

      test(
        'should handle network timeouts gracefully',
        () async {
          final shortTimeoutDio = Dio();
          shortTimeoutDio.options.baseUrl = dio.options.baseUrl;
          shortTimeoutDio.options.connectTimeout = const Duration(
            milliseconds: 1,
          );
          shortTimeoutDio.options.receiveTimeout = const Duration(
            milliseconds: 1,
          );

          final paymentData = {
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Timeout test',
            'customer_email': '<EMAIL>',
            'redirect_url': 'https://example.com/callback',
          };

          try {
            await shortTimeoutDio.post(
              '/api/zoho/payments/create-link',
              data: paymentData,
            );

            // If it succeeds, that's unexpected but okay
          } catch (e) {
            // Expected to timeout
            expect(e, isA<DioException>());
            if (e is DioException) {
              expect(
                e.type,
                anyOf([
                  DioExceptionType.connectionTimeout,
                  DioExceptionType.receiveTimeout,
                  DioExceptionType.sendTimeout,
                ]),
              );
            }
          }
        },
        timeout: const Timeout(Duration(seconds: 30)),
      );
    });
  });
}
