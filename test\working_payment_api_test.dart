import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'dart:convert';

/// Working Payment API Test Suite
/// Tests payment endpoints with proper response handling
void main() {
  group('Working Payment API Tests', () {
    late Dio dio;
    String? createdPaymentLinkId;

    setUpAll(() {
      dio = Dio();
      dio.options.baseUrl =
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);
    });

    group('Payment Link Creation', () {
      test(
        'should create payment link successfully',
        () async {
          final requestData = {
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Working test payment',
            'customer_email': '<EMAIL>',
            'redirect_url': 'https://example.com/callback',
            'meta_data': [
              {'key': 'source', 'value': 'flutter_app'},
              {'key': 'test_type', 'value': 'working_test'},
            ],
            'send_email': true,
            'send_sms': false,
            'partial_payments': false,
          };

          try {
            final response = await dio.post(
              '/api/zoho/payments/create-link',
              data: requestData,
            );

            // Validate status code
            expect(response.statusCode, equals(201));

            // Handle response - could be String or Map
            Map<String, dynamic> data;
            if (response.data is String) {
              data = json.decode(response.data as String);
            } else {
              data = response.data as Map<String, dynamic>;
            }

            // Validate response structure
            expect(data['success'], isTrue);
            expect(data['message'], isNotEmpty);
            expect(data['data'], isNotNull);

            // Validate payment data
            final paymentData = data['data'] as Map<String, dynamic>;
            expect(paymentData['payment_link_id'], isNotEmpty);
            expect(paymentData['amount'], isNotEmpty);
            expect(paymentData['currency'], equals('INR'));
            expect(paymentData['description'], equals('Working test payment'));
            expect(paymentData['status'], equals('active'));
            expect(paymentData['created_time'], isA<int>());
            expect(paymentData['transaction_id'], isNotEmpty);

            // Validate payment link structure
            expect(data['payment_link'], isNotNull);
            final paymentLink = data['payment_link'] as Map<String, dynamic>;
            expect(paymentLink['url'], startsWith('https://payments.zoho.in/'));
            expect(paymentLink['email'], equals('<EMAIL>'));
            expect(
              paymentLink['return_url'],
              equals('https://example.com/callback'),
            );

            // Store for later tests
            createdPaymentLinkId = paymentData['payment_link_id'] as String?;

            // Print success info
            print('✅ Payment Link Created:');
            print('   ID: ${paymentData['payment_link_id']}');
            print('   URL: ${paymentLink['url']}');
            print(
              '   Amount: ${paymentData['amount']} ${paymentData['currency']}',
            );
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              print('⚠️  Network issue detected, skipping test');
              return;
            }
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );

      test(
        'should validate different amounts',
        () async {
          final testAmounts = [1.0, 50.0, 1000.0];

          for (final amount in testAmounts) {
            final requestData = {
              'amount': amount,
              'currency': 'INR',
              'description': 'Amount test for $amount',
              'customer_email': '<EMAIL>',
              'redirect_url': 'https://example.com/callback',
            };

            try {
              final response = await dio.post(
                '/api/zoho/payments/create-link',
                data: requestData,
              );
              expect(response.statusCode, equals(201));

              Map<String, dynamic> data;
              if (response.data is String) {
                data = json.decode(response.data as String);
              } else {
                data = response.data as Map<String, dynamic>;
              }

              expect(data['success'], isTrue);
              final paymentData = data['data'] as Map<String, dynamic>;
              expect(paymentData['amount'], equals(amount.toStringAsFixed(2)));

              print('✅ Amount $amount validated successfully');
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }
              print('❌ Amount validation failed for $amount: $e');
              rethrow;
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 3)),
      );

      test(
        'should reject invalid amounts',
        () async {
          final invalidAmounts = [0.0, -10.0];

          for (final amount in invalidAmounts) {
            final requestData = {
              'amount': amount,
              'currency': 'INR',
              'description': 'Invalid amount test',
              'customer_email': '<EMAIL>',
              'redirect_url': 'https://example.com/callback',
            };

            try {
              final response = await dio.post(
                '/api/zoho/payments/create-link',
                data: requestData,
              );

              // If it succeeds, check if it's marked as failed
              Map<String, dynamic> data;
              if (response.data is String) {
                data = json.decode(response.data as String);
              } else {
                data = response.data as Map<String, dynamic>;
              }

              if (data['success'] == true) {
                fail('Expected validation error for invalid amount: $amount');
              }
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }
              // Expected to fail with 400 Bad Request
              if (e is DioException) {
                expect(e.response?.statusCode, equals(400));
                print('✅ Invalid amount $amount properly rejected');
              }
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );

      test(
        'should validate email formats',
        () async {
          final validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
          ];

          for (final email in validEmails) {
            final requestData = {
              'amount': 25.0,
              'currency': 'INR',
              'description': 'Email validation test',
              'customer_email': email,
              'redirect_url': 'https://example.com/callback',
            };

            try {
              final response = await dio.post(
                '/api/zoho/payments/create-link',
                data: requestData,
              );
              expect(response.statusCode, equals(201));

              Map<String, dynamic> data;
              if (response.data is String) {
                data = json.decode(response.data as String);
              } else {
                data = response.data as Map<String, dynamic>;
              }

              expect(data['success'], isTrue);
              print('✅ Email $email validated successfully');
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }
              print('❌ Email validation failed for $email: $e');
              rethrow;
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
    });

    group('Payment Status Tests', () {
      test(
        'should handle payment status requests',
        () async {
          // Test with a dummy ID first
          const testPaymentId = 'dummy_payment_id_123';

          try {
            final response = await dio.get(
              '/api/payment/status/$testPaymentId',
            );

            Map<String, dynamic> data;
            if (response.data is String) {
              data = json.decode(response.data as String);
            } else {
              data = response.data as Map<String, dynamic>;
            }

            print('✅ Payment Status Response: ${data['message']}');
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return;
            }

            // Expected to fail with 404 for non-existent payment
            if (e is DioException && e.response?.statusCode == 404) {
              print('✅ Non-existent payment properly returns 404');
              return;
            }

            print('ℹ️  Payment Status Error: $e');
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );

      test(
        'should get status for created payment if available',
        () async {
          if (createdPaymentLinkId == null) {
            print('⚠️  No payment ID available for status test');
            return;
          }

          try {
            final response = await dio.get(
              '/api/payment/status/$createdPaymentLinkId',
            );

            expect(response.statusCode, equals(200));

            Map<String, dynamic> data;
            if (response.data is String) {
              data = json.decode(response.data as String);
            } else {
              data = response.data as Map<String, dynamic>;
            }

            expect(data['success'], isTrue);
            print('✅ Payment Status Retrieved: ${data['message']}');
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return;
            }

            // Payment might not be found immediately, that's okay
            if (e is DioException && e.response?.statusCode == 404) {
              print(
                'ℹ️  Payment not found in status check (expected for new payments)',
              );
              return;
            }

            print('❌ Payment Status Error: $e');
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );
    });

    group('Flutter Payment Tests', () {
      test(
        'should investigate Flutter payment endpoint parameters',
        () async {
          // Test different parameter structures for Flutter endpoint
          final flutterTestCases = [
            {
              'name': 'Standard Flutter format',
              'data': {
                'amount': 500.0,
                'currency': 'INR',
                'description': 'Flutter payment test',
                'customerEmail': '<EMAIL>',
                'callbackUrl': 'https://example.com/flutter-callback',
                'businessName': 'AquaPartner',
              },
            },
            {
              'name': 'Alternative Flutter format',
              'data': {
                'amount': 500.0,
                'currency': 'INR',
                'description': 'Flutter payment test alt',
                'customer_email': '<EMAIL>',
                'redirect_url': 'https://example.com/flutter-callback',
                'business_name': 'AquaPartner',
              },
            },
            {
              'name': 'Minimal Flutter format',
              'data': {
                'amount': 500.0,
                'description': 'Flutter payment minimal',
                'customerEmail': '<EMAIL>',
              },
            },
          ];

          for (final testCase in flutterTestCases) {
            try {
              final response = await dio.post(
                '/api/flutter/payment/initiate',
                data: testCase['data'],
              );

              Map<String, dynamic> data;
              if (response.data is String) {
                data = json.decode(response.data as String);
              } else {
                data = response.data as Map<String, dynamic>;
              }

              print('✅ Flutter Payment Success (${testCase['name']}):');
              print('   Response: $data');

              if (data['success'] == true && data['data'] != null) {
                final paymentData = data['data'] as Map<String, dynamic>;
                print('   Payment ID: ${paymentData['payment_session_id']}');
                print('   WebView URL: ${paymentData['webview_url']}');
              }
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }

              print('❌ Flutter Payment Failed (${testCase['name']}): $e');
              if (e is DioException && e.response != null) {
                print('   Status: ${e.response!.statusCode}');
                print('   Response: ${e.response!.data}');

                // Try to parse error response
                if (e.response!.data != null) {
                  try {
                    Map<String, dynamic> errorData;
                    if (e.response!.data is String) {
                      errorData = json.decode(e.response!.data as String);
                    } else {
                      errorData = e.response!.data as Map<String, dynamic>;
                    }

                    if (errorData['required_fields'] != null) {
                      print(
                        '   Required Fields: ${errorData['required_fields']}',
                      );
                    }
                  } catch (_) {
                    // Ignore parsing errors
                  }
                }
              }
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
    });

    group('Payment Session Tests', () {
      test(
        'should investigate payment session endpoint parameters',
        () async {
          final sessionTestCases = [
            {
              'name': 'With invoice and customer ID',
              'data': {
                'amount': 250.0,
                'currency': 'INR',
                'description': 'Payment session test',
                'customer_email': '<EMAIL>',
                'redirect_url': 'https://example.com/session-callback',
                'invoiceNo': 'SESSION-${DateTime.now().millisecondsSinceEpoch}',
                'customerId': 'test_customer_session',
              },
            },
            {
              'name': 'Alternative field names',
              'data': {
                'amount': 250.0,
                'currency': 'INR',
                'description': 'Payment session test alt',
                'customer_email': '<EMAIL>',
                'redirect_url': 'https://example.com/session-callback',
                'invoice_number':
                    'SESSION-ALT-${DateTime.now().millisecondsSinceEpoch}',
                'customer_id': 'test_customer_alt',
              },
            },
          ];

          for (final testCase in sessionTestCases) {
            try {
              final response = await dio.post(
                '/api/zoho/payments/create-session',
                data: testCase['data'],
              );

              Map<String, dynamic> data;
              if (response.data is String) {
                data = json.decode(response.data as String);
              } else {
                data = response.data as Map<String, dynamic>;
              }

              print('✅ Payment Session Success (${testCase['name']}):');
              print('   Response: $data');
            } catch (e) {
              if (e.toString().contains('SocketException')) {
                continue;
              }

              print('❌ Payment Session Failed (${testCase['name']}): $e');
              if (e is DioException && e.response != null) {
                print('   Status: ${e.response!.statusCode}');
                print('   Response: ${e.response!.data}');
              }
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
    });

    group('Error Handling', () {
      test(
        'should handle missing required fields',
        () async {
          final incompleteData = {
            'amount': 100.0,
            // Missing required fields
          };

          try {
            await dio.post(
              '/api/zoho/payments/create-link',
              data: incompleteData,
            );
            fail('Expected validation error for incomplete data');
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              return;
            }

            if (e is DioException) {
              expect(e.response?.statusCode, equals(400));
              print('✅ Missing fields properly rejected with 400');

              if (e.response?.data != null) {
                Map<String, dynamic> errorData;
                if (e.response!.data is String) {
                  errorData = json.decode(e.response!.data as String);
                } else {
                  errorData = e.response!.data as Map<String, dynamic>;
                }

                expect(errorData['error'], isNotNull);
                print('   Error: ${errorData['error']}');
              }
            }
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );

      test(
        'should handle server connectivity',
        () async {
          try {
            final response = await dio.get('/');
            expect(response.statusCode, anyOf([200, 404]));
            print('✅ Server is accessible');
          } catch (e) {
            if (e.toString().contains('SocketException')) {
              print(
                '⚠️  Network connectivity issue (expected in some environments)',
              );
              return;
            }
            print('❌ Server connectivity error: $e');
            rethrow;
          }
        },
        timeout: const Timeout(Duration(seconds: 30)),
      );
    });
  });
}
