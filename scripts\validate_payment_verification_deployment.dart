#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Deployment validation script for Payment Verification System
/// This script helps validate the deployment phases systematically
void main(List<String> args) async {
  print('🚀 Payment Verification System - Deployment Validation');
  print('=' * 60);

  if (args.isEmpty) {
    printUsage();
    return;
  }

  final phase = args[0];
  final environment = args.length > 1 ? args[1] : 'staging';

  switch (phase) {
    case 'phase1':
      await validatePhase1DependencyInjection();
      break;
    case 'phase2':
      await validatePhase2ServerEndpoint(environment);
      break;
    case 'phase3':
      await validatePhase3EndToEndFlow(environment);
      break;
    case 'phase4':
      await validatePhase4Monitoring(environment);
      break;
    case 'all':
      await runAllPhases(environment);
      break;
    default:
      print('❌ Unknown phase: $phase');
      printUsage();
  }
}

void printUsage() {
  print('''
Usage: dart scripts/validate_payment_verification_deployment.dart <phase> [environment]

Phases:
  phase1    - Validate dependency injection setup
  phase2    - Validate server-side endpoint
  phase3    - Validate end-to-end payment flow
  phase4    - Validate monitoring & analytics
  all       - Run all validation phases

Environments:
  staging   - Staging environment (default)
  production - Production environment

Examples:
  dart scripts/validate_payment_verification_deployment.dart phase1
  dart scripts/validate_payment_verification_deployment.dart phase2 staging
  dart scripts/validate_payment_verification_deployment.dart all production
''');
}

/// Phase 1: Validate Dependency Injection Setup
Future<void> validatePhase1DependencyInjection() async {
  print('📋 Phase 1: Validating Dependency Injection Setup');
  print('-' * 50);

  final checks = [
    _checkFileExists('lib/injection/payment_di.dart'),
    _checkFileExists('lib/domain/usecases/payments/verify_transaction_usecase.dart'),
    _checkFileExists('lib/core/services/payment_completion_service.dart'),
    _checkFileExists('lib/data/models/payment/payment_verification_response_model.dart'),
    _checkFileExists('lib/domain/entities/payments/payment_verification_response.dart'),
  ];

  final results = await Future.wait(checks);
  final allPassed = results.every((result) => result);

  if (allPassed) {
    print('✅ All dependency injection files are present');
    await _validateDependencyRegistration();
  } else {
    print('❌ Some required files are missing');
    exit(1);
  }
}

/// Phase 2: Validate Server-Side Endpoint
Future<void> validatePhase2ServerEndpoint(String environment) async {
  print('🌐 Phase 2: Validating Server-Side Endpoint ($environment)');
  print('-' * 50);

  final baseUrl = _getBaseUrl(environment);
  final testTransactionId = 'test_transaction_123';
  final endpointUrl = '$baseUrl/api/verify-transaction/$testTransactionId';

  print('Testing endpoint: $endpointUrl');

  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse(endpointUrl));
    request.headers.set('Content-Type', 'application/json');
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('Response Status: ${response.statusCode}');
    print('Response Body: $responseBody');

    if (response.statusCode == 404) {
      print('✅ Endpoint exists (404 expected for test transaction)');
      _validateResponseSchema(responseBody);
    } else if (response.statusCode == 200) {
      print('✅ Endpoint exists and returned data');
      _validateResponseSchema(responseBody);
    } else {
      print('❌ Unexpected status code: ${response.statusCode}');
      exit(1);
    }

    client.close();
  } catch (e) {
    print('❌ Failed to connect to endpoint: $e');
    exit(1);
  }
}

/// Phase 3: Validate End-to-End Payment Flow
Future<void> validatePhase3EndToEndFlow(String environment) async {
  print('🔄 Phase 3: Validating End-to-End Payment Flow ($environment)');
  print('-' * 50);

  print('Running Flutter tests for payment verification...');
  
  final testFiles = [
    'test/data/datasources/remote/payment_verification_test.dart',
    'test/data/repositories/payment_verification_repository_test.dart',
    'test/domain/usecases/payments/verify_transaction_usecase_test.dart',
    'test/integration/payment_completion_flow_test.dart',
  ];

  for (final testFile in testFiles) {
    if (await _checkFileExists(testFile)) {
      print('✅ Test file exists: $testFile');
    } else {
      print('❌ Missing test file: $testFile');
      exit(1);
    }
  }

  // Run the tests
  final result = await Process.run('flutter', ['test', ...testFiles]);
  
  if (result.exitCode == 0) {
    print('✅ All payment verification tests passed');
    print(result.stdout);
  } else {
    print('❌ Some tests failed');
    print(result.stderr);
    exit(1);
  }
}

/// Phase 4: Validate Monitoring & Analytics
Future<void> validatePhase4Monitoring(String environment) async {
  print('📊 Phase 4: Validating Monitoring & Analytics ($environment)');
  print('-' * 50);

  // Check monitoring service integration
  final monitoringChecks = [
    _checkFileContains(
      'lib/data/repositories/payment_repository_impl.dart',
      'PaymentMonitoringService.trackSecurityEvent',
    ),
    _checkFileContains(
      'lib/presentation/widgets/invoices_page.dart',
      'invoice_refresh_after_payment',
    ),
    _checkFileContains(
      'lib/core/services/payment_completion_service.dart',
      'payment_completion_success',
    ),
  ];

  final results = await Future.wait(monitoringChecks);
  final allPassed = results.every((result) => result);

  if (allPassed) {
    print('✅ All monitoring integrations are present');
  } else {
    print('❌ Some monitoring integrations are missing');
    exit(1);
  }
}

/// Run all validation phases
Future<void> runAllPhases(String environment) async {
  print('🎯 Running All Validation Phases for $environment');
  print('=' * 60);

  await validatePhase1DependencyInjection();
  print('');
  await validatePhase2ServerEndpoint(environment);
  print('');
  await validatePhase3EndToEndFlow(environment);
  print('');
  await validatePhase4Monitoring(environment);
  
  print('');
  print('🎉 All validation phases completed successfully!');
  print('✅ Payment Verification System is ready for deployment');
}

/// Helper Functions

Future<bool> _checkFileExists(String filePath) async {
  final file = File(filePath);
  final exists = await file.exists();
  
  if (exists) {
    print('✅ File exists: $filePath');
  } else {
    print('❌ File missing: $filePath');
  }
  
  return exists;
}

Future<bool> _checkFileContains(String filePath, String searchText) async {
  try {
    final file = File(filePath);
    final content = await file.readAsString();
    final contains = content.contains(searchText);
    
    if (contains) {
      print('✅ Found "$searchText" in $filePath');
    } else {
      print('❌ Missing "$searchText" in $filePath');
    }
    
    return contains;
  } catch (e) {
    print('❌ Error reading $filePath: $e');
    return false;
  }
}

Future<void> _validateDependencyRegistration() async {
  final diFile = File('lib/injection/payment_di.dart');
  final content = await diFile.readAsString();
  
  final requiredRegistrations = [
    'VerifyTransaction',
    'ForceRefreshTransaction',
    'PaymentCompletionService',
  ];
  
  for (final registration in requiredRegistrations) {
    if (content.contains(registration)) {
      print('✅ $registration is registered in DI');
    } else {
      print('❌ $registration is missing from DI');
      exit(1);
    }
  }
}

void _validateResponseSchema(String responseBody) {
  try {
    final json = jsonDecode(responseBody);
    
    if (json is Map<String, dynamic>) {
      final hasSuccess = json.containsKey('success');
      final hasMessage = json.containsKey('message');
      
      if (hasSuccess && hasMessage) {
        print('✅ Response schema is valid');
      } else {
        print('❌ Response schema is invalid - missing required fields');
      }
    } else {
      print('❌ Response is not valid JSON object');
    }
  } catch (e) {
    print('❌ Failed to parse response JSON: $e');
  }
}

String _getBaseUrl(String environment) {
  switch (environment) {
    case 'staging':
      return 'https://aquapartner-staging.centralindia-01.azurewebsites.net';
    case 'production':
      return 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
    default:
      throw ArgumentError('Unknown environment: $environment');
  }
}
