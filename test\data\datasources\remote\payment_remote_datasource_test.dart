import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:dio/dio.dart';

import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/data/models/payment/payment_request_model.dart';
import 'package:aquapartner/data/models/payment/payment_link_model.dart';
import 'package:aquapartner/data/models/payment/payment_link_response_model.dart';

import 'payment_remote_datasource_test.mocks.dart';

@GenerateMocks([ApiClient, AppLogger])
void main() {
  late PaymentRemoteDataSourceImpl dataSource;
  late MockApiClient mockApiClient;
  late MockAppLogger mockLogger;

  setUp(() {
    mockApiClient = MockApiClient();
    mockLogger = MockAppLogger();
    dataSource = PaymentRemoteDataSourceImpl(
      apiClient: mockApiClient,
      logger: mockLogger,
    );
  });

  group('PaymentRemoteDataSource', () {
    final tPaymentRequest = PaymentRequestModel(
      amount: 100.0,
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      currency: 'INR',
      redirectUrl: 'https://example.com/callback',
    );

    final tCreatedTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final tPaymentLinkModel = PaymentLinkModel(
      paymentLinkId: 'test_link_id',
      paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
      amount: 100.0,
      currency: 'INR',
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      status: 'active',
      createdTime: tCreatedTime,
      transactionId: 'test_transaction_id',
    );

    final tSuccessResponse = PaymentLinkResponseModel(
      success: true,
      message: 'Payment link created successfully',
      data: tPaymentLinkModel,
    );

    group('createPaymentLink', () {
      test(
        'should return PaymentLinkModel when API call is successful',
        () async {
          // arrange
          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: tSuccessResponse.toJson(),
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act
          final result = await dataSource.createPaymentLink(tPaymentRequest);

          // assert
          expect(result.paymentLinkId, equals(tPaymentLinkModel.paymentLinkId));
          expect(
            result.paymentLinkUrl,
            equals(tPaymentLinkModel.paymentLinkUrl),
          );
          expect(result.amount, equals(tPaymentLinkModel.amount));
          verify(
            mockApiClient.post(
              '/api/zoho/payments/create-link',
              data: tPaymentRequest.toJson(),
            ),
          );
          verify(
            mockLogger.i(
              'Creating payment link with amount: ${tPaymentRequest.amount}',
            ),
          );
          verify(
            mockLogger.i(
              'Payment link created successfully: ${tPaymentLinkModel.paymentLinkId}',
            ),
          );
        },
      );

      test(
        'should throw ServerException when API returns error response',
        () async {
          // arrange
          final errorResponse = PaymentLinkResponseModel(
            success: false,
            message: 'Payment link creation failed',
            data: null,
          );

          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: errorResponse.toJson(),
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act & assert
          expect(
            () => dataSource.createPaymentLink(tPaymentRequest),
            throwsA(isA<ServerException>()),
          );
          verify(
            mockLogger.e(
              'Payment link creation failed: ${errorResponse.message}',
            ),
          );
        },
      );

      test(
        'should throw ServerException when API returns non-200 status',
        () async {
          // arrange
          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: {},
              statusCode: 500,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act & assert
          expect(
            () => dataSource.createPaymentLink(tPaymentRequest),
            throwsA(isA<ServerException>()),
          );
          verify(mockLogger.e('Payment link creation failed with status: 500'));
        },
      );

      test('should throw ServerException when network error occurs', () async {
        // arrange
        when(mockApiClient.post(any, data: anyNamed('data'))).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: ''),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        // act & assert
        expect(
          () => dataSource.createPaymentLink(tPaymentRequest),
          throwsA(isA<ServerException>()),
        );
        verify(mockLogger.e(argThat(contains('Error creating payment link:'))));
      });
    });

    group('createPaymentSession', () {
      test(
        'should return PaymentLinkModel when API call is successful',
        () async {
          // arrange
          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: tSuccessResponse.toJson(),
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act
          final result = await dataSource.createPaymentSession(tPaymentRequest);

          // assert
          expect(result, equals(tPaymentLinkModel));
          verify(
            mockApiClient.post(
              '/api/zoho/payments/create-session',
              data: tPaymentRequest.toJson(),
            ),
          );
          verify(
            mockLogger.i(
              'Creating payment session with amount: ${tPaymentRequest.amount}',
            ),
          );
        },
      );

      test(
        'should throw ServerException when session creation fails',
        () async {
          // arrange
          final errorResponse = PaymentLinkResponseModel(
            success: false,
            message: 'Session creation failed',
            data: null,
          );

          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: errorResponse.toJson(),
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act & assert
          expect(
            () => dataSource.createPaymentSession(tPaymentRequest),
            throwsA(isA<ServerException>()),
          );
          verify(
            mockLogger.e(
              'Payment session creation failed: ${errorResponse.message}',
            ),
          );
        },
      );
    });

    group('createFlutterPayment', () {
      test(
        'should return PaymentLinkModel when Flutter API call is successful',
        () async {
          // arrange
          final flutterResponse = {
            'success': true,
            'data': {
              'payment_session_id': 'flutter_session_id',
              'webview_url': 'https://flutter.payments.com/checkout',
              'amount': 100.0,
              'currency': 'INR',
              'description': 'Test payment',
              'created_time': DateTime.now().millisecondsSinceEpoch ~/ 1000,
              'transaction_id': 'flutter_transaction_id',
              'invoice_number': 'INV-001',
            },
          };

          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: flutterResponse,
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act
          final result = await dataSource.createFlutterPayment(tPaymentRequest);

          // assert
          expect(result.paymentLinkId, equals('flutter_session_id'));
          expect(
            result.paymentLinkUrl,
            equals('https://flutter.payments.com/checkout'),
          );
          verify(
            mockApiClient.post(
              '/api/flutter/payment/initiate',
              data: tPaymentRequest.toFlutterJson(),
            ),
          );
          verify(
            mockLogger.i(
              'Creating Flutter payment with amount: ${tPaymentRequest.amount}',
            ),
          );
        },
      );

      test(
        'should throw ServerException when Flutter API returns error',
        () async {
          // arrange
          final errorResponse = {
            'success': false,
            'message': 'Flutter payment creation failed',
          };

          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: errorResponse,
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act & assert
          expect(
            () => dataSource.createFlutterPayment(tPaymentRequest),
            throwsA(isA<ServerException>()),
          );
          verify(
            mockLogger.e(
              'Flutter payment creation failed: Flutter payment creation failed',
            ),
          );
        },
      );
    });

    group('getPaymentStatus', () {
      const tPaymentLinkId = 'test_payment_link_id';

      test(
        'should return PaymentLinkModel when status retrieval is successful',
        () async {
          // arrange
          when(mockApiClient.get(any)).thenAnswer(
            (_) async => Response(
              data: tSuccessResponse.toJson(),
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act
          final result = await dataSource.getPaymentStatus(tPaymentLinkId);

          // assert
          expect(result, equals(tPaymentLinkModel));
          verify(mockApiClient.get('/api/payment/status/$tPaymentLinkId'));
          verify(mockLogger.i('Getting payment status for: $tPaymentLinkId'));
          verify(mockLogger.i('Payment status retrieved successfully'));
        },
      );

      test(
        'should throw ServerException when status retrieval fails',
        () async {
          // arrange
          final errorResponse = PaymentLinkResponseModel(
            success: false,
            message: 'Payment not found',
            data: null,
          );

          when(mockApiClient.get(any)).thenAnswer(
            (_) async => Response(
              data: errorResponse.toJson(),
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act & assert
          expect(
            () => dataSource.getPaymentStatus(tPaymentLinkId),
            throwsA(isA<ServerException>()),
          );
          verify(
            mockLogger.e(
              'Failed to get payment status: ${errorResponse.message}',
            ),
          );
        },
      );

      test(
        'should throw ServerException when API returns non-200 status',
        () async {
          // arrange
          when(mockApiClient.get(any)).thenAnswer(
            (_) async => Response(
              data: {},
              statusCode: 404,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act & assert
          expect(
            () => dataSource.getPaymentStatus(tPaymentLinkId),
            throwsA(isA<ServerException>()),
          );
          verify(
            mockLogger.e('Payment status request failed with status: 404'),
          );
        },
      );
    });

    group('timeout handling', () {
      test('should handle timeout for createPaymentLink', () async {
        // arrange
        when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
          (_) => Future.delayed(
            const Duration(seconds: 35),
            () => Response(
              data: tSuccessResponse.toJson(),
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ),
          ),
        );

        // act & assert
        expect(
          () => dataSource.createPaymentLink(tPaymentRequest),
          throwsA(isA<ServerException>()),
        );
      });
    });
  });
}
