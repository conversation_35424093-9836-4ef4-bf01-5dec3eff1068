{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/flutter_projects/aquapartner/android/app/.cxx/RelWithDebInfo/l54w3u4j/x86_64", "source": "C:/Users/<USER>/dev/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}