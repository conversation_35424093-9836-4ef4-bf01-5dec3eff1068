import 'package:equatable/equatable.dart';

/// Enum representing payment status
enum PaymentStatus { success, failed, cancelled, unknown }

/// Domain entity representing the result of a payment transaction
class PaymentResult extends Equatable {
  final PaymentStatus status;
  final String? paymentId;
  final String? transactionId;
  final String? invoiceNumber;
  final double? amount;
  final String? paymentLinkId;
  final String? errorMessage;
  final String? callbackSource;
  final DateTime timestamp;
  final Map<String, String>? additionalData;

  const PaymentResult({
    required this.status,
    this.paymentId,
    this.transactionId,
    this.invoiceNumber,
    this.amount,
    this.paymentLinkId,
    this.errorMessage,
    this.callbackSource,
    required this.timestamp,
    this.additionalData,
  });

  /// Factory constructor for successful payment
  factory PaymentResult.success({
    required String paymentId,
    String? transactionId,
    String? invoiceNumber,
    double? amount,
    String? paymentLinkId,
    String? callbackSource,
    Map<String, String>? additionalData,
  }) {
    return PaymentResult(
      status: PaymentStatus.success,
      paymentId: paymentId,
      transactionId: transactionId,
      invoiceNumber: invoiceNumber,
      amount: amount,
      paymentLinkId: paymentLinkId,
      callbackSource: callbackSource,
      timestamp: DateTime.now(),
      additionalData: additionalData,
    );
  }

  /// Factory constructor for failed payment
  factory PaymentResult.failed({
    String? errorMessage,
    String? transactionId,
    String? invoiceNumber,
    String? paymentLinkId,
    String? callbackSource,
    Map<String, String>? additionalData,
  }) {
    return PaymentResult(
      status: PaymentStatus.failed,
      errorMessage: errorMessage,
      transactionId: transactionId,
      invoiceNumber: invoiceNumber,
      paymentLinkId: paymentLinkId,
      callbackSource: callbackSource,
      timestamp: DateTime.now(),
      additionalData: additionalData,
    );
  }

  /// Factory constructor for cancelled payment
  factory PaymentResult.cancelled({
    String? invoiceNumber,
    String? paymentLinkId,
    Map<String, String>? additionalData,
  }) {
    return PaymentResult(
      status: PaymentStatus.cancelled,
      invoiceNumber: invoiceNumber,
      paymentLinkId: paymentLinkId,
      timestamp: DateTime.now(),
      additionalData: additionalData,
    );
  }

  /// Factory constructor for unknown status
  factory PaymentResult.unknown({
    String? errorMessage,
    Map<String, String>? additionalData,
  }) {
    return PaymentResult(
      status: PaymentStatus.unknown,
      errorMessage: errorMessage,
      timestamp: DateTime.now(),
      additionalData: additionalData,
    );
  }

  /// Check if payment was successful
  bool get isSuccess => status == PaymentStatus.success;

  /// Check if payment failed
  bool get isFailed => status == PaymentStatus.failed;

  /// Check if payment was cancelled
  bool get isCancelled => status == PaymentStatus.cancelled;

  /// Check if status is unknown
  bool get isUnknown => status == PaymentStatus.unknown;

  @override
  List<Object?> get props => [
    status,
    paymentId,
    transactionId,
    invoiceNumber,
    amount,
    paymentLinkId,
    errorMessage,
    callbackSource,
    timestamp,
    additionalData,
  ];

  @override
  String toString() {
    return 'PaymentResult{status: $status, paymentId: $paymentId, '
        'transactionId: $transactionId, invoiceNumber: $invoiceNumber, '
        'amount: $amount, errorMessage: $errorMessage, timestamp: $timestamp}';
  }
}
