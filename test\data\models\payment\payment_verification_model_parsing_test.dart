import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/payment/payment_verification_response_model.dart';

void main() {
  group('PaymentVerificationDataModel String Parsing Tests', () {
    test('should parse numeric and boolean strings correctly', () {
      // Arrange - API response with string values
      final jsonWithStrings = {
        'transaction_id': 'txn_test_123',
        'payment_id': 'pay_test_123',
        'status': 'succeeded',
        'amount': '25.50', // String instead of number
        'currency': 'INR',
        'invoice_number': 'INV-001',
        'customer_email': '<EMAIL>',
        'payment_completed_time': '2024-01-15T10:30:00.000Z',
        'invoice_status_updated': 'true', // String instead of boolean
        'invoice_payment_status': 'Paid',
        'force_refreshed': '1', // String instead of boolean
        'invoice_updated': 'false', // String instead of boolean
        'webhook_processed': '0', // String instead of boolean
      };

      // Act
      final model = PaymentVerificationDataModel.fromJson(jsonWithStrings);

      // Assert
      expect(model.transactionId, equals('txn_test_123'));
      expect(model.paymentId, equals('pay_test_123'));
      expect(model.status, equals('succeeded'));
      expect(model.amount, equals(25.50)); // Should be parsed from string "25.50"
      expect(model.currency, equals('INR'));
      expect(model.invoiceNumber, equals('INV-001'));
      expect(model.customerEmail, equals('<EMAIL>'));
      expect(model.paymentCompletedTime, equals('2024-01-15T10:30:00.000Z'));
      expect(model.invoiceStatusUpdated, isTrue); // Should be parsed from string "true"
      expect(model.invoicePaymentStatus, equals('Paid'));
      expect(model.forceRefreshed, isTrue); // Should be parsed from string "1"
      expect(model.invoiceUpdated, isFalse); // Should be parsed from string "false"
      expect(model.webhookProcessed, isFalse); // Should be parsed from string "0"
    });

    test('should handle mixed numeric and boolean types correctly', () {
      // Arrange - API response with mixed types
      final jsonWithMixedTypes = {
        'transaction_id': 'txn_test_456',
        'payment_id': 'pay_test_456',
        'status': 'completed',
        'amount': 100.0, // Double
        'currency': 'USD',
        'invoice_number': 'INV-002',
        'customer_email': '<EMAIL>',
        'payment_completed_time': '2024-01-15T11:00:00.000Z',
        'invoice_status_updated': true, // Boolean
        'invoice_payment_status': 'Completed',
        'force_refreshed': 1, // Int (should be true)
        'invoice_updated': false, // Boolean
        'webhook_processed': 0, // Int (should be false)
      };

      // Act
      final model = PaymentVerificationDataModel.fromJson(jsonWithMixedTypes);

      // Assert
      expect(model.amount, equals(100.0)); // Should handle double
      expect(model.invoiceStatusUpdated, isTrue); // Should handle boolean
      expect(model.forceRefreshed, isTrue); // Should handle int 1 as true
      expect(model.invoiceUpdated, isFalse); // Should handle boolean
      expect(model.webhookProcessed, isFalse); // Should handle int 0 as false
    });

    test('should handle invalid string values gracefully', () {
      // Arrange - API response with invalid string values
      final jsonWithInvalidStrings = {
        'transaction_id': 'txn_test_789',
        'payment_id': 'pay_test_789',
        'status': 'failed',
        'amount': 'invalid_amount', // Invalid string
        'currency': 'EUR',
        'invoice_number': 'INV-003',
        'customer_email': '<EMAIL>',
        'payment_completed_time': '2024-01-15T12:00:00.000Z',
        'invoice_status_updated': 'maybe', // Invalid boolean string
        'invoice_payment_status': 'Failed',
        'force_refreshed': 'invalid_bool', // Invalid boolean string
        'invoice_updated': 'unknown', // Invalid boolean string
        'webhook_processed': 'invalid', // Invalid boolean string
      };

      // Act
      final model = PaymentVerificationDataModel.fromJson(jsonWithInvalidStrings);

      // Assert - Should use default values for invalid strings
      expect(model.amount, equals(0.0)); // Should default to 0.0
      expect(model.invoiceStatusUpdated, isFalse); // Should use default value (false)
      expect(model.forceRefreshed, isFalse); // Should use default value (false)
      expect(model.invoiceUpdated, isFalse); // Should use default value (false)
      expect(model.webhookProcessed, isFalse); // Should use default value (false)
    });

    test('should handle null and missing values correctly', () {
      // Arrange - API response with minimal required data
      final jsonWithNulls = {
        'transaction_id': 'txn_test_minimal',
        'status': 'pending',
        'currency': 'INR',
        'invoice_number': 'INV-004',
        'customer_email': '<EMAIL>',
        // Missing amount, payment_id, optional fields, etc.
      };

      // Act
      final model = PaymentVerificationDataModel.fromJson(jsonWithNulls);

      // Assert - Should use default values
      expect(model.transactionId, equals('txn_test_minimal'));
      expect(model.paymentId, isNull); // Should be null when not provided
      expect(model.status, equals('pending'));
      expect(model.amount, equals(0.0)); // Default
      expect(model.currency, equals('INR'));
      expect(model.invoiceNumber, equals('INV-004'));
      expect(model.customerEmail, equals('<EMAIL>'));
      expect(model.paymentCompletedTime, isNull); // Should be null when not provided
      expect(model.invoiceStatusUpdated, isFalse); // Default
      expect(model.invoicePaymentStatus, isNull); // Should be null when not provided
      expect(model.forceRefreshed, isNull); // Should be null when not provided
      expect(model.invoiceUpdated, isNull); // Should be null when not provided
      expect(model.webhookProcessed, isNull); // Should be null when not provided
    });

    test('should handle edge case boolean values', () {
      // Arrange - API response with various boolean representations
      final jsonWithBooleanEdgeCases = {
        'transaction_id': 'txn_test_bool',
        'status': 'succeeded',
        'amount': '50.25',
        'currency': 'INR',
        'invoice_number': 'INV-005',
        'customer_email': '<EMAIL>',
        'invoice_status_updated': 'TRUE', // Uppercase string should be true
        'force_refreshed': 'FALSE', // Uppercase string should be false
        'invoice_updated': '1', // String "1" should be true
        'webhook_processed': '0', // String "0" should be false
      };

      // Act
      final model = PaymentVerificationDataModel.fromJson(jsonWithBooleanEdgeCases);

      // Assert
      expect(model.invoiceStatusUpdated, isTrue); // String "TRUE" should be true
      expect(model.forceRefreshed, isFalse); // String "FALSE" should be false
      expect(model.invoiceUpdated, isTrue); // String "1" should be true
      expect(model.webhookProcessed, isFalse); // String "0" should be false
    });

    test('should verify isPaymentSuccessful method works with string status', () {
      // Arrange - Test different status values
      final successfulStatuses = ['succeeded', 'completed', 'success'];
      
      for (final status in successfulStatuses) {
        final json = {
          'transaction_id': 'txn_test_success',
          'status': status,
          'amount': '10.00',
          'currency': 'INR',
          'invoice_number': 'INV-SUCCESS',
          'customer_email': '<EMAIL>',
          'invoice_status_updated': 'true',
        };

        // Act
        final model = PaymentVerificationDataModel.fromJson(json);

        // Assert
        expect(model.isPaymentSuccessful, isTrue, reason: 'Status "$status" should be successful');
      }

      // Test unsuccessful status
      final unsuccessfulJson = {
        'transaction_id': 'txn_test_failed',
        'status': 'failed',
        'amount': '10.00',
        'currency': 'INR',
        'invoice_number': 'INV-FAILED',
        'customer_email': '<EMAIL>',
        'invoice_status_updated': 'false',
      };

      final failedModel = PaymentVerificationDataModel.fromJson(unsuccessfulJson);
      expect(failedModel.isPaymentSuccessful, isFalse);
    });
  });
}
