#!/bin/bash

# Payment Verification System - Deployment Validation Script
# This script validates the complete payment verification and invoice update system

set -e

echo "🚀 Payment Verification System - Deployment Validation"
echo "=" | tr ' ' '=' | head -c 60; echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Function to check if file exists
check_file() {
    local file=$1
    if [ -f "$file" ]; then
        print_status "SUCCESS" "File exists: $file"
        return 0
    else
        print_status "ERROR" "File missing: $file"
        return 1
    fi
}

# Function to check if directory exists
check_directory() {
    local dir=$1
    if [ -d "$dir" ]; then
        print_status "SUCCESS" "Directory exists: $dir"
        return 0
    else
        print_status "ERROR" "Directory missing: $dir"
        return 1
    fi
}

# Phase 1: Validate File Structure
echo "📋 Phase 1: Validating File Structure"
echo "-" | tr ' ' '-' | head -c 50; echo

files_to_check=(
    "lib/data/models/payment/payment_verification_response_model.dart"
    "lib/domain/entities/payments/payment_verification_response.dart"
    "lib/domain/usecases/payments/verify_transaction_usecase.dart"
    "lib/core/services/payment_completion_service.dart"
    "lib/injection/payment_di.dart"
    "test/data/datasources/remote/payment_verification_test.dart"
    "test/data/repositories/payment_verification_repository_test.dart"
    "test/domain/usecases/payments/verify_transaction_usecase_test.dart"
    "test/integration/payment_completion_flow_test.dart"
    "test/integration/complete_payment_verification_flow_test.dart"
)

all_files_exist=true
for file in "${files_to_check[@]}"; do
    if ! check_file "$file"; then
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = true ]; then
    print_status "SUCCESS" "All required files are present"
else
    print_status "ERROR" "Some required files are missing"
    exit 1
fi

# Phase 2: Validate Dependencies
echo ""
echo "📦 Phase 2: Validating Dependencies"
echo "-" | tr ' ' '-' | head -c 50; echo

print_status "INFO" "Checking Flutter dependencies..."
if flutter pub deps > /dev/null 2>&1; then
    print_status "SUCCESS" "Flutter dependencies are resolved"
else
    print_status "ERROR" "Flutter dependency issues detected"
    exit 1
fi

# Phase 3: Run Static Analysis
echo ""
echo "🔍 Phase 3: Running Static Analysis"
echo "-" | tr ' ' '-' | head -c 50; echo

print_status "INFO" "Running Flutter analyze..."
if flutter analyze > /dev/null 2>&1; then
    print_status "SUCCESS" "Static analysis passed"
else
    print_status "WARNING" "Static analysis found issues (check with 'flutter analyze')"
fi

# Phase 4: Run Unit Tests
echo ""
echo "🧪 Phase 4: Running Unit Tests"
echo "-" | tr ' ' '-' | head -c 50; echo

test_files=(
    "test/data/datasources/remote/payment_verification_test.dart"
    "test/data/repositories/payment_verification_repository_test.dart"
    "test/domain/usecases/payments/verify_transaction_usecase_test.dart"
)

for test_file in "${test_files[@]}"; do
    if [ -f "$test_file" ]; then
        print_status "INFO" "Running tests: $test_file"
        if flutter test "$test_file" > /dev/null 2>&1; then
            print_status "SUCCESS" "Tests passed: $test_file"
        else
            print_status "ERROR" "Tests failed: $test_file"
            exit 1
        fi
    fi
done

# Phase 5: Run Integration Tests
echo ""
echo "🔄 Phase 5: Running Integration Tests"
echo "-" | tr ' ' '-' | head -c 50; echo

integration_tests=(
    "test/integration/payment_completion_flow_test.dart"
    "test/integration/complete_payment_verification_flow_test.dart"
)

for test_file in "${integration_tests[@]}"; do
    if [ -f "$test_file" ]; then
        print_status "INFO" "Running integration tests: $test_file"
        if flutter test "$test_file" > /dev/null 2>&1; then
            print_status "SUCCESS" "Integration tests passed: $test_file"
        else
            print_status "ERROR" "Integration tests failed: $test_file"
            exit 1
        fi
    fi
done

# Phase 6: Validate API Endpoint (if URL provided)
if [ ! -z "$1" ]; then
    echo ""
    echo "🌐 Phase 6: Validating API Endpoint"
    echo "-" | tr ' ' '-' | head -c 50; echo
    
    API_URL="$1"
    TEST_ENDPOINT="$API_URL/api/verify-transaction/test_transaction_123"
    
    print_status "INFO" "Testing endpoint: $TEST_ENDPOINT"
    
    if command -v curl > /dev/null 2>&1; then
        response=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_ENDPOINT" || echo "000")
        
        if [ "$response" = "404" ]; then
            print_status "SUCCESS" "Endpoint exists (404 expected for test transaction)"
        elif [ "$response" = "200" ]; then
            print_status "SUCCESS" "Endpoint exists and returned data"
        elif [ "$response" = "000" ]; then
            print_status "ERROR" "Failed to connect to endpoint"
        else
            print_status "WARNING" "Unexpected response code: $response"
        fi
    else
        print_status "WARNING" "curl not available, skipping endpoint test"
    fi
fi

# Phase 7: Build Validation
echo ""
echo "🏗️  Phase 7: Build Validation"
echo "-" | tr ' ' '-' | head -c 50; echo

print_status "INFO" "Running Flutter build validation..."
if flutter build apk --debug > /dev/null 2>&1; then
    print_status "SUCCESS" "Debug build successful"
else
    print_status "ERROR" "Debug build failed"
    exit 1
fi

# Summary
echo ""
echo "📊 Validation Summary"
echo "=" | tr ' ' '=' | head -c 60; echo

print_status "SUCCESS" "Payment Verification System validation completed successfully!"
echo ""
echo "✅ File structure validation passed"
echo "✅ Dependencies resolved"
echo "✅ Static analysis completed"
echo "✅ Unit tests passed"
echo "✅ Integration tests passed"
if [ ! -z "$1" ]; then
    echo "✅ API endpoint validation completed"
fi
echo "✅ Build validation passed"
echo ""
print_status "SUCCESS" "System is ready for deployment!"

# Usage instructions
echo ""
echo "📋 Next Steps:"
echo "1. Deploy to staging environment"
echo "2. Test with real payment transactions"
echo "3. Monitor payment completion metrics"
echo "4. Deploy to production when ready"
echo ""
echo "Usage: $0 [API_BASE_URL]"
echo "Example: $0 https://aquapartner-staging.centralindia-01.azurewebsites.net"
