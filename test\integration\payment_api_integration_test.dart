import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/data/models/payment/payment_request_model.dart';

void main() {
  group('Payment API Integration Tests', () {
    late PaymentRemoteDataSourceImpl dataSource;
    late ApiClient apiClient;
    late AppLogger logger;

    setUpAll(() {
      // Initialize real dependencies for integration testing
      final dio = Dio();
      dio.options.baseUrl =
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      const secureStorage = FlutterSecureStorage();
      apiClient = ApiClient(dio, secureStorage);
      logger = AppLogger();

      dataSource = PaymentRemoteDataSourceImpl(
        apiClient: apiClient,
        logger: logger,
      );
    });

    group('Payment Link Creation', () {
      test(
        'should create payment link with valid request',
        () async {
          // arrange
          final paymentRequest = PaymentRequestModel(
            amount: 100.0,
            description: 'Test payment for integration testing',
            customerEmail: '<EMAIL>',
            currency: 'INR',
            redirectUrl: 'https://example.com/callback',
            invoiceNumber: 'TEST-INV-${DateTime.now().millisecondsSinceEpoch}',
            customerId: 'test_customer_123',
            customerName: 'Test Customer',
            customerPhone: '+91-9876543210',
          );

          try {
            // act
            final result = await dataSource.createPaymentLink(paymentRequest);

            // assert
            expect(result.paymentLinkId, isNotEmpty);
            expect(result.paymentLinkUrl, isNotEmpty);
            expect(result.amount, equals(100.0));
            expect(result.currency, equals('INR'));
            expect(result.customerEmail, equals('<EMAIL>'));
            expect(result.status, isNotEmpty);
            expect(result.transactionId, isNotEmpty);

            print('✅ Payment Link Created Successfully:');
            print('   Payment Link ID: ${result.paymentLinkId}');
            print('   Payment URL: ${result.paymentLinkUrl}');
            print('   Amount: ${result.amount} ${result.currency}');
            print('   Status: ${result.status}');
          } catch (e) {
            print('❌ Payment Link Creation Failed: $e');
            // Don't fail the test if it's a network issue in CI/CD
            if (e.toString().contains('SocketException') ||
                e.toString().contains('TimeoutException')) {
              print('⚠️  Network issue detected, skipping test');
              return;
            }
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );

      test(
        'should create payment session with valid request',
        () async {
          // arrange
          final paymentRequest = PaymentRequestModel(
            amount: 250.0,
            description: 'Test payment session for integration testing',
            customerEmail: '<EMAIL>',
            currency: 'INR',
            redirectUrl: 'https://example.com/session-callback',
            invoiceNumber:
                'SESSION-INV-${DateTime.now().millisecondsSinceEpoch}',
          );

          try {
            // act
            final result = await dataSource.createPaymentSession(
              paymentRequest,
            );

            // assert
            expect(result.paymentLinkId, isNotEmpty);
            expect(result.paymentLinkUrl, isNotEmpty);
            expect(result.amount, equals(250.0));
            expect(result.currency, equals('INR'));

            print('✅ Payment Session Created Successfully:');
            print('   Session ID: ${result.paymentLinkId}');
            print('   Session URL: ${result.paymentLinkUrl}');
            print('   Amount: ${result.amount} ${result.currency}');
          } catch (e) {
            print('❌ Payment Session Creation Failed: $e');
            if (e.toString().contains('SocketException') ||
                e.toString().contains('TimeoutException')) {
              print('⚠️  Network issue detected, skipping test');
              return;
            }
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );

      test(
        'should create Flutter payment with valid request',
        () async {
          // arrange
          final paymentRequest = PaymentRequestModel(
            amount: 500.0,
            description: 'Test Flutter payment for integration testing',
            customerEmail: '<EMAIL>',
            currency: 'INR',
            redirectUrl: 'https://example.com/flutter-callback',
            invoiceNumber:
                'FLUTTER-INV-${DateTime.now().millisecondsSinceEpoch}',
          );

          try {
            // act
            final result = await dataSource.createFlutterPayment(
              paymentRequest,
            );

            // assert
            expect(result.paymentLinkId, isNotEmpty);
            expect(result.paymentLinkUrl, isNotEmpty);
            expect(result.amount, equals(500.0));

            print('✅ Flutter Payment Created Successfully:');
            print('   Payment ID: ${result.paymentLinkId}');
            print('   WebView URL: ${result.paymentLinkUrl}');
            print('   Amount: ${result.amount} ${result.currency}');
          } catch (e) {
            print('❌ Flutter Payment Creation Failed: $e');
            if (e.toString().contains('SocketException') ||
                e.toString().contains('TimeoutException')) {
              print('⚠️  Network issue detected, skipping test');
              return;
            }
            rethrow;
          }
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
    });

    group('Payment Status Retrieval', () {
      test(
        'should handle payment status request',
        () async {
          // This test would need a valid payment link ID
          // For now, we'll test with a dummy ID to see the error handling
          const testPaymentLinkId = 'test_payment_link_id_123';

          try {
            // act
            final result = await dataSource.getPaymentStatus(testPaymentLinkId);

            // If we get here, the payment exists
            print('✅ Payment Status Retrieved:');
            print('   Payment ID: ${result.paymentLinkId}');
            print('   Status: ${result.status}');
            print('   Amount: ${result.amount} ${result.currency}');
          } catch (e) {
            // Expected for non-existent payment ID
            print('ℹ️  Payment Status Request Result: $e');
            // This is expected behavior for non-existent payment
            expect(e.toString(), contains('ServerException'));
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );
    });

    group('Error Handling', () {
      test(
        'should handle invalid payment request gracefully',
        () async {
          // arrange - invalid request with negative amount
          final invalidRequest = PaymentRequestModel(
            amount: -100.0, // Invalid negative amount
            description: '', // Empty description
            customerEmail: 'invalid-email', // Invalid email format
            currency: 'INVALID',
            redirectUrl: 'not-a-url',
          );

          try {
            // act
            await dataSource.createPaymentLink(invalidRequest);

            // If we get here without exception, that's unexpected
            fail('Expected ServerException for invalid request');
          } catch (e) {
            // assert - should throw ServerException for invalid data
            print('✅ Invalid Request Handled Correctly: $e');
            expect(e.toString(), contains('ServerException'));
          }
        },
        timeout: const Timeout(Duration(minutes: 1)),
      );
    });

    group('API Endpoint Validation', () {
      test(
        'should validate API endpoints are accessible',
        () async {
          final dio = Dio();
          dio.options.baseUrl =
              'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';

          try {
            // Test basic connectivity to the API server
            final response = await dio.get('/');
            print('✅ API Server is accessible');
            print('   Status Code: ${response.statusCode}');
          } catch (e) {
            print('⚠️  API Server connectivity issue: $e');
            if (e.toString().contains('SocketException') ||
                e.toString().contains('TimeoutException')) {
              print(
                '⚠️  Network issue detected, this is expected in some environments',
              );
              return;
            }
          }
        },
        timeout: const Timeout(Duration(seconds: 30)),
      );
    });
  });
}
