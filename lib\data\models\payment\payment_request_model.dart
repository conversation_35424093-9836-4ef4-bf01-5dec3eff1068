import '../../../domain/entities/payments/payment_request.dart';

/// Data model for payment request API calls
class PaymentRequestModel {
  final double amount;
  final String description;
  final String customerEmail;
  final String currency;
  final String? invoiceNumber;
  final String? customerId;
  final String? customerName;
  final String? customerPhone;
  final String? referenceId;
  final String redirectUrl;
  final List<Map<String, String>> metaData;
  final bool sendEmail;
  final bool sendSms;
  final int? expiresAt;
  final bool partialPayments;
  final double? minimumPartialAmount;

  PaymentRequestModel({
    required this.amount,
    required this.description,
    required this.customerEmail,
    this.currency = 'INR',
    this.invoiceNumber,
    this.customerId,
    this.customerName,
    this.customerPhone,
    this.referenceId,
    required this.redirectUrl,
    this.metaData = const [],
    this.sendEmail = true,
    this.sendSms = false,
    this.expiresAt,
    this.partialPayments = false,
    this.minimumPartialAmount,
  });

  /// Create model from domain entity
  factory PaymentRequestModel.fromEntity(
    PaymentRequest entity, {
    required String redirectUrl,
  }) {
    final metaData = <Map<String, String>>[
      {'key': 'source', 'value': 'flutter_app'},
      {'key': 'timestamp', 'value': DateTime.now().millisecondsSinceEpoch.toString()},
    ];

    // Add custom metadata if provided
    if (entity.metadata != null) {
      for (final entry in entity.metadata!.entries) {
        metaData.add({'key': entry.key, 'value': entry.value});
      }
    }

    return PaymentRequestModel(
      amount: entity.amount,
      description: entity.description,
      customerEmail: entity.customerEmail,
      currency: entity.currency,
      invoiceNumber: entity.invoiceNumber,
      customerId: entity.customerId,
      customerName: entity.customerName,
      customerPhone: entity.customerPhone,
      referenceId: entity.referenceId,
      redirectUrl: redirectUrl,
      metaData: metaData,
      sendEmail: entity.sendEmail,
      sendSms: entity.sendSms,
      expiresAt: entity.expiresAt?.millisecondsSinceEpoch,
      partialPayments: entity.partialPayments,
      minimumPartialAmount: entity.minimumPartialAmount,
    );
  }

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'amount': amount,
      'currency': currency,
      'description': description,
      'customer_email': customerEmail,
      'redirect_url': redirectUrl,
      'meta_data': metaData,
      'send_email': sendEmail,
      'send_sms': sendSms,
      'partial_payments': partialPayments,
    };

    // Add optional fields if they exist
    if (invoiceNumber != null) json['invoice_number'] = invoiceNumber;
    if (customerId != null) json['customer_id'] = customerId;
    if (customerName != null) json['customer_name'] = customerName;
    if (customerPhone != null) json['customer_phone'] = customerPhone;
    if (referenceId != null) json['reference_id'] = referenceId;
    if (expiresAt != null) json['expires_at'] = expiresAt;
    if (minimumPartialAmount != null) json['minimum_partial_amount'] = minimumPartialAmount;

    return json;
  }

  /// Convert to JSON for Flutter-specific API endpoint
  Map<String, dynamic> toFlutterJson() {
    final json = <String, dynamic>{
      'amount': amount,
      'currency': currency,
      'description': description,
      'customerEmail': customerEmail,
      'callbackUrl': redirectUrl,
      'businessName': 'AquaPartner',
    };

    // Add optional fields with Flutter API naming
    if (invoiceNumber != null) json['invoiceNo'] = invoiceNumber;
    if (customerId != null) json['customerId'] = customerId;
    if (customerName != null) json['customerName'] = customerName;
    if (customerPhone != null) json['customerPhone'] = customerPhone;
    if (referenceId != null) json['referenceNumber'] = referenceId;

    // Convert metadata to Flutter API format
    if (metaData.isNotEmpty) {
      final metadata = <String, String>{};
      for (final item in metaData) {
        metadata[item['key']!] = item['value']!;
      }
      json['metadata'] = metadata;
    }

    return json;
  }
}
