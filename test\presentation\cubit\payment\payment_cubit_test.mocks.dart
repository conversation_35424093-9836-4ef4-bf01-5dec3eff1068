// Mocks generated by Mockito 5.4.6 from annotations
// in aquapartner/test/presentation/cubit/payment/payment_cubit_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;

import 'package:aquapartner/core/error/failures.dart' as _i11;
import 'package:aquapartner/core/services/analytics_service.dart' as _i15;
import 'package:aquapartner/core/services/payment_service.dart' as _i9;
import 'package:aquapartner/core/utils/logger.dart' as _i5;
import 'package:aquapartner/domain/entities/customer.dart' as _i16;
import 'package:aquapartner/domain/entities/payments/payment_link.dart' as _i12;
import 'package:aquapartner/domain/entities/payments/payment_request.dart'
    as _i7;
import 'package:aquapartner/domain/entities/payments/payment_result.dart'
    as _i13;
import 'package:aquapartner/domain/usecases/payment/create_payment_link_usecase.dart'
    as _i2;
import 'package:aquapartner/domain/usecases/payment/parse_payment_result_usecase.dart'
    as _i4;
import 'package:aquapartner/domain/usecases/payment/validate_payment_url_usecase.dart'
    as _i3;
import 'package:dartz/dartz.dart' as _i6;
import 'package:firebase_analytics/firebase_analytics.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i14;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCreatePaymentLinkUseCase_0 extends _i1.SmartFake
    implements _i2.CreatePaymentLinkUseCase {
  _FakeCreatePaymentLinkUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeValidatePaymentUrlUseCase_1 extends _i1.SmartFake
    implements _i3.ValidatePaymentUrlUseCase {
  _FakeValidatePaymentUrlUseCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeParsePaymentResultUseCase_2 extends _i1.SmartFake
    implements _i4.ParsePaymentResultUseCase {
  _FakeParsePaymentResultUseCase_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAppLogger_3 extends _i1.SmartFake implements _i5.AppLogger {
  _FakeAppLogger_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_4<L, R> extends _i1.SmartFake implements _i6.Either<L, R> {
  _FakeEither_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaymentRequest_5 extends _i1.SmartFake
    implements _i7.PaymentRequest {
  _FakePaymentRequest_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseAnalytics_6 extends _i1.SmartFake
    implements _i8.FirebaseAnalytics {
  _FakeFirebaseAnalytics_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PaymentService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPaymentService extends _i1.Mock implements _i9.PaymentService {
  MockPaymentService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.CreatePaymentLinkUseCase get createPaymentLinkUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#createPaymentLinkUseCase),
            returnValue: _FakeCreatePaymentLinkUseCase_0(
              this,
              Invocation.getter(#createPaymentLinkUseCase),
            ),
          )
          as _i2.CreatePaymentLinkUseCase);

  @override
  _i3.ValidatePaymentUrlUseCase get validatePaymentUrlUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#validatePaymentUrlUseCase),
            returnValue: _FakeValidatePaymentUrlUseCase_1(
              this,
              Invocation.getter(#validatePaymentUrlUseCase),
            ),
          )
          as _i3.ValidatePaymentUrlUseCase);

  @override
  _i4.ParsePaymentResultUseCase get parsePaymentResultUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#parsePaymentResultUseCase),
            returnValue: _FakeParsePaymentResultUseCase_2(
              this,
              Invocation.getter(#parsePaymentResultUseCase),
            ),
          )
          as _i4.ParsePaymentResultUseCase);

  @override
  _i5.AppLogger get logger =>
      (super.noSuchMethod(
            Invocation.getter(#logger),
            returnValue: _FakeAppLogger_3(this, Invocation.getter(#logger)),
          )
          as _i5.AppLogger);

  @override
  _i10.Future<_i6.Either<_i11.Failure, _i12.PaymentLink>> createPaymentLink(
    _i7.PaymentRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentLink, [request]),
            returnValue:
                _i10.Future<_i6.Either<_i11.Failure, _i12.PaymentLink>>.value(
                  _FakeEither_4<_i11.Failure, _i12.PaymentLink>(
                    this,
                    Invocation.method(#createPaymentLink, [request]),
                  ),
                ),
          )
          as _i10.Future<_i6.Either<_i11.Failure, _i12.PaymentLink>>);

  @override
  _i10.Future<_i6.Either<_i11.Failure, bool>> validatePaymentUrl(String? url) =>
      (super.noSuchMethod(
            Invocation.method(#validatePaymentUrl, [url]),
            returnValue: _i10.Future<_i6.Either<_i11.Failure, bool>>.value(
              _FakeEither_4<_i11.Failure, bool>(
                this,
                Invocation.method(#validatePaymentUrl, [url]),
              ),
            ),
          )
          as _i10.Future<_i6.Either<_i11.Failure, bool>>);

  @override
  _i10.Future<_i6.Either<_i11.Failure, _i13.PaymentResult>> parsePaymentResult(
    String? callbackUrl,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#parsePaymentResult, [callbackUrl]),
            returnValue:
                _i10.Future<_i6.Either<_i11.Failure, _i13.PaymentResult>>.value(
                  _FakeEither_4<_i11.Failure, _i13.PaymentResult>(
                    this,
                    Invocation.method(#parsePaymentResult, [callbackUrl]),
                  ),
                ),
          )
          as _i10.Future<_i6.Either<_i11.Failure, _i13.PaymentResult>>);

  @override
  bool isPaymentCompletionUrl(String? url) =>
      (super.noSuchMethod(
            Invocation.method(#isPaymentCompletionUrl, [url]),
            returnValue: false,
          )
          as bool);

  @override
  String getErrorMessage(_i11.Failure? failure) =>
      (super.noSuchMethod(
            Invocation.method(#getErrorMessage, [failure]),
            returnValue: _i14.dummyValue<String>(
              this,
              Invocation.method(#getErrorMessage, [failure]),
            ),
          )
          as String);

  @override
  _i7.PaymentRequest createPaymentRequest({
    required double? amount,
    required String? description,
    required String? customerEmail,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? referenceId,
    Map<String, String>? metadata,
    String? currency = 'INR',
    bool? sendEmail = true,
    bool? sendSms = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentRequest, [], {
              #amount: amount,
              #description: description,
              #customerEmail: customerEmail,
              #invoiceNumber: invoiceNumber,
              #customerId: customerId,
              #customerName: customerName,
              #customerPhone: customerPhone,
              #referenceId: referenceId,
              #metadata: metadata,
              #currency: currency,
              #sendEmail: sendEmail,
              #sendSms: sendSms,
            }),
            returnValue: _FakePaymentRequest_5(
              this,
              Invocation.method(#createPaymentRequest, [], {
                #amount: amount,
                #description: description,
                #customerEmail: customerEmail,
                #invoiceNumber: invoiceNumber,
                #customerId: customerId,
                #customerName: customerName,
                #customerPhone: customerPhone,
                #referenceId: referenceId,
                #metadata: metadata,
                #currency: currency,
                #sendEmail: sendEmail,
                #sendSms: sendSms,
              }),
            ),
          )
          as _i7.PaymentRequest);

  @override
  void logPaymentEvent({
    required String? eventName,
    Map<String, dynamic>? parameters,
  }) => super.noSuchMethod(
    Invocation.method(#logPaymentEvent, [], {
      #eventName: eventName,
      #parameters: parameters,
    }),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AppLogger].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppLogger extends _i1.Mock implements _i5.AppLogger {
  MockAppLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void d(String? message) => super.noSuchMethod(
    Invocation.method(#d, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void i(String? message) => super.noSuchMethod(
    Invocation.method(#i, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void w(String? message) => super.noSuchMethod(
    Invocation.method(#w, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void e(String? message, [dynamic error, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#e, [message, error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void enableFirebaseVerboseLogging() => super.noSuchMethod(
    Invocation.method(#enableFirebaseVerboseLogging, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsService extends _i1.Mock implements _i15.AnalyticsService {
  MockAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_6(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i8.FirebaseAnalytics);

  @override
  _i10.Future<void> logUserLogin(String? userId, String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#logUserLogin, [userId, userType]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logUserLogout(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#logUserLogout, [userId]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logEvent({
    required String? name,
    required Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logEvent, [], {
              #name: name,
              #parameters: parameters,
            }),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
            }),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> setUserProperties(String? userId, String? userRole) =>
      (super.noSuchMethod(
            Invocation.method(#setUserProperties, [userId, userRole]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logFeatureUsage(String? featureName) =>
      (super.noSuchMethod(
            Invocation.method(#logFeatureUsage, [featureName]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logProductView(String? productId, String? productName) =>
      (super.noSuchMethod(
            Invocation.method(#logProductView, [productId, productName]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  void setCurrentUser(_i16.Customer? customer) => super.noSuchMethod(
    Invocation.method(#setCurrentUser, [customer]),
    returnValueForMissingStub: null,
  );

  @override
  _i10.Future<void> logScreenDuration({
    required String? screenName,
    required int? durationMs,
    String? screenClass,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenDuration, [], {
              #screenName: screenName,
              #durationMs: durationMs,
              #screenClass: screenClass,
            }),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logOrderCreated(String? orderId, double? amount) =>
      (super.noSuchMethod(
            Invocation.method(#logOrderCreated, [orderId, amount]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logCustomerInteraction(
    String? customerId,
    String? interactionType,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#logCustomerInteraction, [
              customerId,
              interactionType,
            ]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logError({
    required String? errorType,
    required String? errorMessage,
    String? screenName,
    Map<String, Object>? additionalParams = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logError, [], {
              #errorType: errorType,
              #errorMessage: errorMessage,
              #screenName: screenName,
              #additionalParams: additionalParams,
            }),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logUserEngagement(int? durationMs) =>
      (super.noSuchMethod(
            Invocation.method(#logUserEngagement, [durationMs]),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logSessionStart() =>
      (super.noSuchMethod(
            Invocation.method(#logSessionStart, []),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logUserInteraction({
    required String? screenName,
    required String? actionName,
    required String? elementType,
    String? elementId,
    Map<String, Object>? additionalParams = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logUserInteraction, [], {
              #screenName: screenName,
              #actionName: actionName,
              #elementType: elementType,
              #elementId: elementId,
              #additionalParams: additionalParams,
            }),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> logUserFlow({
    required String? flowName,
    required String? stepName,
    required String? status,
    Map<String, Object>? additionalParams = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logUserFlow, [], {
              #flowName: flowName,
              #stepName: stepName,
              #status: status,
              #additionalParams: additionalParams,
            }),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);

  @override
  _i10.Future<void> updateUserBehaviorProperties() =>
      (super.noSuchMethod(
            Invocation.method(#updateUserBehaviorProperties, []),
            returnValue: _i10.Future<void>.value(),
            returnValueForMissingStub: _i10.Future<void>.value(),
          )
          as _i10.Future<void>);
}
