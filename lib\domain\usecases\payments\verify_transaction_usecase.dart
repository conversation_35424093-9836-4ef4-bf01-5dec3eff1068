import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/payment_verification_response.dart';
import '../../repositories/payment_repository.dart';

/// Use case for verifying payment transaction status and invoice updates
class VerifyTransaction {
  final PaymentRepository repository;

  VerifyTransaction(this.repository);

  /// Verify transaction and check if invoice status was updated
  /// Returns [PaymentVerificationResponse] on success or [Failure] on error
  Future<Either<Failure, PaymentVerificationResponse>> call(String transactionId) async {
    return await repository.verifyTransaction(transactionId);
  }
}

/// Use case for force refreshing payment transaction status
class ForceRefreshTransaction {
  final PaymentRepository repository;

  ForceRefreshTransaction(this.repository);

  /// Force refresh transaction status with optional invoice update
  /// Returns [PaymentVerificationResponse] on success or [Failure] on error
  Future<Either<Failure, PaymentVerificationResponse>> call(
    String transactionId, {
    bool updateInvoice = true,
  }) async {
    return await repository.forceRefreshTransaction(
      transactionId,
      updateInvoice: updateInvoice,
    );
  }
}
