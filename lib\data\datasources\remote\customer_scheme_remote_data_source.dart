import 'dart:convert';

import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/data/models/customer_scheme_model.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';

abstract class CustomerSchemeRemoteDataSource {
  Future<CustomerSchemeModel?> getCustomerScheme(String customerId);
  void clearCache();
  void clearCacheForCustomer(String customerId);
}

class CustomerSchemeRemoteDataSourceImpl
    implements CustomerSchemeRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  // Add a timeout for operations
  final Duration operationTimeout = const Duration(seconds: 15);

  CustomerSchemeRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<CustomerSchemeModel?> getCustomerScheme(String customerId) async {
    try {
      logger.i(
        "Fetching Customer Schemem data from API for customer: $customerId",
      );
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/api/getSchemeWithSupportUserDetails/$customerId',
      );

      if (response.statusCode == 200) {
        // The response data is already a string, so we don't need to decode it first
        // Check if the response data is a string or already parsed JSON
        final dynamic responseData = response.data;
        Map<String, dynamic> data;

        if (responseData is String) {
          data = jsonDecode(responseData);
        } else if (responseData is Map<String, dynamic>) {
          data = responseData;
        } else {
          logger.e("Unexpected response format: ${responseData.runtimeType}");
          throw ServerException();
        }

        // Check if result is null or empty
        if (data.isEmpty) {
          return null;
        }

        // Create dashboard model from response with sync status
        final customerSchemeModel = CustomerSchemeModel.fromJson(data);

        return customerSchemeModel;
      }
      return null;
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  void clearCache() {
    // This method is kept for interface compatibility
    // Actual cache clearing should be handled by the local data source
    logger.i("Remote cache clearing requested - no action needed");
  }

  @override
  void clearCacheForCustomer(String customerId) {
    // This method is kept for interface compatibility
    // Actual cache clearing should be handled by the local data source
    logger.i("Remote cache clearing for customer requested - no action needed");
  }
}
