import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dio/dio.dart';

import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/data/models/payment/payment_request_model.dart';

// Mock classes
class MockApiClient extends Mock implements ApiClient {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('PaymentRemoteDataSource String Response Test', () {
    late PaymentRemoteDataSourceImpl dataSource;
    late MockApiClient mockApiClient;
    late MockAppLogger mockLogger;

    setUp(() {
      mockApiClient = MockApiClient();
      mockLogger = MockAppLogger();
      dataSource = PaymentRemoteDataSourceImpl(
        apiClient: mockApiClient,
        logger: mockLogger,
      );

      // Setup logger mocks
      when(() => mockLogger.i(any())).thenReturn(null);
      when(() => mockLogger.d(any())).thenReturn(null);
      when(() => mockLogger.e(any())).thenReturn(null);
    });

    test('should handle String response from API correctly', () async {
      // Arrange
      final request = PaymentRequestModel(
        amount: 100.0,
        description: 'Test payment',
        customerEmail: '<EMAIL>',
        currency: 'INR',
        redirectUrl: 'https://example.com/callback',
        metaData: [],
        sendEmail: true,
        sendSms: false,
        partialPayments: false,
      );

      final responseData = {
        'success': true,
        'message': 'Payment link created successfully',
        'data': {
          'payment_link_id': 'pl_test_123',
          'payment_link_url': 'https://payment.example.com/pay/pl_test_123',
          'amount': 100.0,
          'currency': 'INR',
          'description': 'Test payment',
          'customer_email': '<EMAIL>',
          'status': 'active',
          'created_time': 1640995200,
          'transaction_id': 'txn_test_123',
        },
      };

      // Convert to JSON string (simulating API returning String)
      final jsonString = json.encode(responseData);

      final mockResponse = Response(
        data: jsonString, // String response
        statusCode: 201,
        requestOptions: RequestOptions(path: '/api/zoho/payments/create-link'),
      );

      when(
        () => mockApiClient.post(
          '/api/zoho/payments/create-link',
          data: any(named: 'data'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act
      final result = await dataSource.createPaymentLink(request);

      // Assert
      expect(result.paymentLinkId, equals('pl_test_123'));
      expect(
        result.paymentLinkUrl,
        equals('https://payment.example.com/pay/pl_test_123'),
      );
      expect(result.amount, equals(100.0));
      expect(result.currency, equals('INR'));
      expect(result.description, equals('Test payment'));
      expect(result.customerEmail, equals('<EMAIL>'));
      expect(result.status, equals('active'));
      expect(result.transactionId, equals('txn_test_123'));

      // Verify that the logger was called with the correct messages
      verify(
        () => mockLogger.d('API returned String response, parsing JSON'),
      ).called(1);
      verify(
        () => mockLogger.i('Payment link created successfully: pl_test_123'),
      ).called(1);
    });

    test('should handle Map response from API correctly', () async {
      // Arrange
      final request = PaymentRequestModel(
        amount: 100.0,
        description: 'Test payment',
        customerEmail: '<EMAIL>',
        currency: 'INR',
        redirectUrl: 'https://example.com/callback',
        metaData: [],
        sendEmail: true,
        sendSms: false,
        partialPayments: false,
      );

      final responseData = {
        'success': true,
        'message': 'Payment link created successfully',
        'data': {
          'payment_link_id': 'pl_test_456',
          'payment_link_url': 'https://payment.example.com/pay/pl_test_456',
          'amount': 100.0,
          'currency': 'INR',
          'description': 'Test payment',
          'customer_email': '<EMAIL>',
          'status': 'active',
          'created_time': 1640995200,
          'transaction_id': 'txn_test_456',
        },
      };

      final mockResponse = Response(
        data: responseData, // Map response
        statusCode: 201,
        requestOptions: RequestOptions(path: '/api/zoho/payments/create-link'),
      );

      when(
        () => mockApiClient.post(
          '/api/zoho/payments/create-link',
          data: any(named: 'data'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act
      final result = await dataSource.createPaymentLink(request);

      // Assert
      expect(result.paymentLinkId, equals('pl_test_456'));
      expect(
        result.paymentLinkUrl,
        equals('https://payment.example.com/pay/pl_test_456'),
      );
      expect(result.amount, equals(100.0));
      expect(result.currency, equals('INR'));
      expect(result.description, equals('Test payment'));
      expect(result.customerEmail, equals('<EMAIL>'));
      expect(result.status, equals('active'));
      expect(result.transactionId, equals('txn_test_456'));

      // Verify that the logger was called with the correct messages
      verify(() => mockLogger.d('API returned Map response')).called(1);
      verify(
        () => mockLogger.i('Payment link created successfully: pl_test_456'),
      ).called(1);
    });

    test('should throw ServerException when JSON parsing fails', () async {
      // Arrange
      final request = PaymentRequestModel(
        amount: 100.0,
        description: 'Test payment',
        customerEmail: '<EMAIL>',
        currency: 'INR',
        redirectUrl: 'https://example.com/callback',
        metaData: [],
        sendEmail: true,
        sendSms: false,
        partialPayments: false,
      );

      final invalidJsonString = 'invalid json string';

      final mockResponse = Response(
        data: invalidJsonString, // Invalid JSON string
        statusCode: 201,
        requestOptions: RequestOptions(path: '/api/zoho/payments/create-link'),
      );

      when(
        () => mockApiClient.post(
          '/api/zoho/payments/create-link',
          data: any(named: 'data'),
        ),
      ).thenAnswer((_) async => mockResponse);

      // Act & Assert
      expect(
        () => dataSource.createPaymentLink(request),
        throwsA(isA<ServerException>()),
      );

      // Verify that the error was logged
      await expectLater(
        dataSource.createPaymentLink(request),
        throwsA(isA<ServerException>()),
      );
    });
  });
}
