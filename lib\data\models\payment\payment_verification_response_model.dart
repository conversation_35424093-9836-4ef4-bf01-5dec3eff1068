import '../../../domain/entities/payments/payment_verification_response.dart';

/// Data model for payment verification API response
class PaymentVerificationResponseModel {
  final bool success;
  final String message;
  final PaymentVerificationDataModel? data;
  final String? error;

  PaymentVerificationResponseModel({
    required this.success,
    required this.message,
    this.data,
    this.error,
  });

  /// Create model from JSON response
  factory PaymentVerificationResponseModel.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationResponseModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data:
          json['data'] != null
              ? PaymentVerificationDataModel.fromJson(json['data'])
              : null,
      error: json['error'],
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
      'error': error,
    };
  }

  /// Convert to domain entity
  PaymentVerificationResponse toEntity() {
    return PaymentVerificationResponse(
      success: success,
      message: message,
      data: data?.toEntity(),
      error: error,
    );
  }
}

/// Data model for payment verification data
class PaymentVerificationDataModel {
  final String transactionId;
  final String? paymentId;
  final String status;
  final double amount;
  final String currency;
  final String invoiceNumber;
  final String customerEmail;
  final String? paymentCompletedTime;
  final bool invoiceStatusUpdated;
  final String? invoicePaymentStatus;
  final bool? forceRefreshed;
  final bool? invoiceUpdated;
  final bool? webhookProcessed;

  PaymentVerificationDataModel({
    required this.transactionId,
    this.paymentId,
    required this.status,
    required this.amount,
    required this.currency,
    required this.invoiceNumber,
    required this.customerEmail,
    this.paymentCompletedTime,
    required this.invoiceStatusUpdated,
    this.invoicePaymentStatus,
    this.forceRefreshed,
    this.invoiceUpdated,
    this.webhookProcessed,
  });

  /// Create model from JSON response
  factory PaymentVerificationDataModel.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationDataModel(
      transactionId: json['transaction_id'] ?? '',
      paymentId: json['payment_id'],
      status: json['status'] ?? '',
      amount: _parseDouble(json['amount']),
      currency: json['currency'] ?? 'INR',
      invoiceNumber: json['invoice_number'] ?? '',
      customerEmail: json['customer_email'] ?? '',
      paymentCompletedTime: json['payment_completed_time'],
      invoiceStatusUpdated: _parseBool(
        json['invoice_status_updated'],
        defaultValue: false,
      ),
      invoicePaymentStatus: json['invoice_payment_status'],
      forceRefreshed:
          json['force_refreshed'] != null
              ? _parseBool(json['force_refreshed'], defaultValue: false)
              : null,
      invoiceUpdated:
          json['invoice_updated'] != null
              ? _parseBool(json['invoice_updated'], defaultValue: false)
              : null,
      webhookProcessed:
          json['webhook_processed'] != null
              ? _parseBool(json['webhook_processed'], defaultValue: false)
              : null,
    );
  }

  /// Safely parse double from dynamic value (handles both String and num)
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// Safely parse bool from dynamic value (handles String, bool, and int)
  static bool _parseBool(dynamic value, {required bool defaultValue}) {
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1') return true;
      if (lowerValue == 'false' || lowerValue == '0') return false;
      return defaultValue;
    }
    if (value is int) return value != 0;
    return defaultValue;
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionId,
      'payment_id': paymentId,
      'status': status,
      'amount': amount,
      'currency': currency,
      'invoice_number': invoiceNumber,
      'customer_email': customerEmail,
      'payment_completed_time': paymentCompletedTime,
      'invoice_status_updated': invoiceStatusUpdated,
      'invoice_payment_status': invoicePaymentStatus,
      'force_refreshed': forceRefreshed,
      'invoice_updated': invoiceUpdated,
      'webhook_processed': webhookProcessed,
    };
  }

  /// Convert to domain entity
  PaymentVerificationData toEntity() {
    return PaymentVerificationData(
      transactionId: transactionId,
      paymentId: paymentId,
      status: status,
      amount: amount,
      currency: currency,
      invoiceNumber: invoiceNumber,
      customerEmail: customerEmail,
      paymentCompletedTime: paymentCompletedTime,
      invoiceStatusUpdated: invoiceStatusUpdated,
      invoicePaymentStatus: invoicePaymentStatus,
      forceRefreshed: forceRefreshed,
      invoiceUpdated: invoiceUpdated,
      webhookProcessed: webhookProcessed,
    );
  }

  /// Check if payment was successful
  bool get isPaymentSuccessful {
    return status.toLowerCase() == 'succeeded' ||
        status.toLowerCase() == 'completed' ||
        status.toLowerCase() == 'success';
  }

  /// Check if invoice status was automatically updated
  bool get wasInvoiceAutoUpdated {
    return invoiceStatusUpdated &&
        invoicePaymentStatus?.toLowerCase() == 'paid';
  }

  /// Check if webhook was processed successfully
  bool get wasWebhookProcessed {
    return webhookProcessed ?? false;
  }
}
