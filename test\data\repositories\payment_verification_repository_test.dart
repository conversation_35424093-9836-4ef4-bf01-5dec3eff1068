import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';

import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/data/models/payment/payment_verification_response_model.dart';
import 'package:aquapartner/data/repositories/payment_repository_impl.dart';
import 'package:aquapartner/domain/entities/payments/payment_verification_response.dart';

class MockPaymentRemoteDataSource extends Mock implements PaymentRemoteDataSource {}
class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('PaymentRepositoryImpl - Transaction Verification', () {
    late PaymentRepositoryImpl repository;
    late MockPaymentRemoteDataSource mockRemoteDataSource;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRemoteDataSource = MockPaymentRemoteDataSource();
      mockLogger = MockAppLogger();
      repository = PaymentRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        logger: mockLogger,
      );
    });

    group('verifyTransaction', () {
      const testTransactionId = 'txn_test123';
      
      test('should return PaymentVerificationResponse when verification is successful', () async {
        // Arrange
        final responseModel = PaymentVerificationResponseModel(
          success: true,
          message: 'Transaction verified successfully',
          data: PaymentVerificationDataModel(
            transactionId: testTransactionId,
            paymentId: 'pay_123',
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: 'INV-2024-001',
            customerEmail: '<EMAIL>',
            paymentCompletedTime: '2024-01-15T10:30:00.000Z',
            invoiceStatusUpdated: true,
            invoicePaymentStatus: 'Paid',
          ),
        );

        when(() => mockRemoteDataSource.verifyTransaction(testTransactionId))
            .thenAnswer((_) async => responseModel);

        // Act
        final result = await repository.verifyTransaction(testTransactionId);

        // Assert
        expect(result, isA<Right<Failure, PaymentVerificationResponse>>());
        
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (response) {
            expect(response.success, true);
            expect(response.data?.transactionId, testTransactionId);
            expect(response.data?.status, 'succeeded');
            expect(response.data?.invoiceStatusUpdated, true);
            expect(response.data?.invoicePaymentStatus, 'Paid');
            expect(response.isPaymentSuccessful, true);
            expect(response.wasInvoiceAutoUpdated, true);
          },
        );
        
        verify(() => mockLogger.i('Verifying transaction: $testTransactionId')).called(1);
        verify(() => mockLogger.i('Payment verification successful for transaction: $testTransactionId')).called(1);
        verify(() => mockLogger.i('Invoice status automatically updated to: Paid')).called(1);
      });

      test('should return ServerFailure when remote data source throws ServerException', () async {
        // Arrange
        when(() => mockRemoteDataSource.verifyTransaction(testTransactionId))
            .thenThrow(ServerException());

        // Act
        final result = await repository.verifyTransaction(testTransactionId);

        // Assert
        expect(result, isA<Left<Failure, PaymentVerificationResponse>>());
        
        result.fold(
          (failure) => expect(failure, isA<ServerFailure>()),
          (response) => fail('Expected Left but got Right'),
        );
        
        verify(() => mockLogger.e('Server error while verifying transaction: $testTransactionId')).called(1);
      });

      test('should return NetworkFailure when remote data source throws NetworkException', () async {
        // Arrange
        when(() => mockRemoteDataSource.verifyTransaction(testTransactionId))
            .thenThrow(NetworkException());

        // Act
        final result = await repository.verifyTransaction(testTransactionId);

        // Assert
        expect(result, isA<Left<Failure, PaymentVerificationResponse>>());
        
        result.fold(
          (failure) => expect(failure, isA<NetworkFailure>()),
          (response) => fail('Expected Left but got Right'),
        );
        
        verify(() => mockLogger.e('Network error while verifying transaction: $testTransactionId')).called(1);
      });

      test('should return UnexpectedFailure when remote data source throws unexpected exception', () async {
        // Arrange
        const errorMessage = 'Unexpected error occurred';
        when(() => mockRemoteDataSource.verifyTransaction(testTransactionId))
            .thenThrow(Exception(errorMessage));

        // Act
        final result = await repository.verifyTransaction(testTransactionId);

        // Assert
        expect(result, isA<Left<Failure, PaymentVerificationResponse>>());
        
        result.fold(
          (failure) {
            expect(failure, isA<UnexpectedFailure>());
            expect((failure as UnexpectedFailure).message, contains(errorMessage));
          },
          (response) => fail('Expected Left but got Right'),
        );
        
        verify(() => mockLogger.e('Unexpected error while verifying transaction: Exception: $errorMessage')).called(1);
      });
    });

    group('forceRefreshTransaction', () {
      const testTransactionId = 'txn_test123';
      
      test('should return PaymentVerificationResponse when force refresh is successful', () async {
        // Arrange
        final responseModel = PaymentVerificationResponseModel(
          success: true,
          message: 'Transaction verification completed with updates',
          data: PaymentVerificationDataModel(
            transactionId: testTransactionId,
            paymentId: 'pay_123',
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: 'INV-2024-001',
            customerEmail: '<EMAIL>',
            paymentCompletedTime: '2024-01-15T10:30:00.000Z',
            invoiceStatusUpdated: true,
            invoicePaymentStatus: 'Paid',
            forceRefreshed: true,
            invoiceUpdated: true,
          ),
        );

        when(() => mockRemoteDataSource.forceRefreshTransaction(testTransactionId, updateInvoice: true))
            .thenAnswer((_) async => responseModel);

        // Act
        final result = await repository.forceRefreshTransaction(testTransactionId, updateInvoice: true);

        // Assert
        expect(result, isA<Right<Failure, PaymentVerificationResponse>>());
        
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (response) {
            expect(response.success, true);
            expect(response.data?.transactionId, testTransactionId);
            expect(response.data?.status, 'succeeded');
            expect(response.data?.invoiceStatusUpdated, true);
            expect(response.data?.forceRefreshed, true);
            expect(response.isPaymentSuccessful, true);
            expect(response.wasInvoiceAutoUpdated, true);
          },
        );
        
        verify(() => mockLogger.i('Force refreshing transaction: $testTransactionId (updateInvoice: true)')).called(1);
        verify(() => mockLogger.i('Force refresh successful for transaction: $testTransactionId')).called(1);
        verify(() => mockLogger.i('Invoice status force updated to: Paid')).called(1);
      });

      test('should pass correct parameters to remote data source', () async {
        // Arrange
        final responseModel = PaymentVerificationResponseModel(
          success: true,
          message: 'Transaction verification completed',
          data: PaymentVerificationDataModel(
            transactionId: testTransactionId,
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: 'INV-2024-001',
            customerEmail: '<EMAIL>',
            invoiceStatusUpdated: false,
            forceRefreshed: true,
          ),
        );

        when(() => mockRemoteDataSource.forceRefreshTransaction(testTransactionId, updateInvoice: false))
            .thenAnswer((_) async => responseModel);

        // Act
        final result = await repository.forceRefreshTransaction(testTransactionId, updateInvoice: false);

        // Assert
        expect(result, isA<Right<Failure, PaymentVerificationResponse>>());
        
        verify(() => mockRemoteDataSource.forceRefreshTransaction(testTransactionId, updateInvoice: false)).called(1);
      });

      test('should return ServerFailure when remote data source throws ServerException', () async {
        // Arrange
        when(() => mockRemoteDataSource.forceRefreshTransaction(testTransactionId, updateInvoice: true))
            .thenThrow(ServerException());

        // Act
        final result = await repository.forceRefreshTransaction(testTransactionId);

        // Assert
        expect(result, isA<Left<Failure, PaymentVerificationResponse>>());
        
        result.fold(
          (failure) => expect(failure, isA<ServerFailure>()),
          (response) => fail('Expected Left but got Right'),
        );
        
        verify(() => mockLogger.e('Server error while force refreshing transaction: $testTransactionId')).called(1);
      });
    });
  });
}
