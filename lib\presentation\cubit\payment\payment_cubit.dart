import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/services/payment_service.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/payments/payment_request.dart';
import '../../../domain/entities/payments/payment_result.dart';
import 'payment_state.dart';

/// Cubit for managing payment operations
class PaymentCubit extends Cubit<PaymentState> {
  final PaymentService paymentService;
  final AppLogger logger;
  final AnalyticsService analyticsService;

  Timer? _paymentTimeout;
  static const Duration _defaultTimeout = Duration(minutes: 10);

  PaymentCubit({
    required this.paymentService,
    required this.logger,
    required this.analyticsService,
  }) : super(const PaymentInitial());

  /// Create a payment link and initiate payment process
  Future<void> createPaymentLink(PaymentRequest request) async {
    try {
      emit(
        PaymentLinkCreating(
          amount: request.amount,
          description: request.description,
          customerEmail: request.customerEmail,
        ),
      );

      // Track payment initiation
      analyticsService.logEvent(
        name: 'payment_link_creation_started',
        parameters: {
          'amount': request.amount,
          'currency': request.currency,
          'customer_email': request.customerEmail,
        },
      );

      logger.i('Creating payment link for amount: ${request.amount}');

      final result = await paymentService.createPaymentLink(request);

      result.fold(
        (failure) {
          final errorMessage = paymentService.getErrorMessage(failure);
          logger.e('Failed to create payment link: $errorMessage');

          // Track error
          analyticsService.logEvent(
            name: 'payment_link_creation_failed',
            parameters: {
              'error_message': errorMessage,
              'amount': request.amount,
            },
          );

          emit(PaymentError(message: errorMessage, errorAt: DateTime.now()));
        },
        (paymentLink) {
          logger.i(
            'Payment link created successfully: ${paymentLink.paymentLinkId}',
          );

          // Track success
          analyticsService.logEvent(
            name: 'payment_link_created',
            parameters: {
              'payment_link_id': paymentLink.paymentLinkId,
              'amount': paymentLink.amount,
              'currency': paymentLink.currency,
            },
          );

          emit(
            PaymentLinkCreated(
              paymentLink: paymentLink,
              createdAt: DateTime.now(),
            ),
          );

          // Start payment timeout
          _startPaymentTimeout(paymentLink);
        },
      );
    } catch (e) {
      logger.e('Unexpected error creating payment link: $e');

      analyticsService.logEvent(
        name: 'payment_link_creation_error',
        parameters: {'error': e.toString()},
      );

      emit(
        PaymentError(
          message: 'An unexpected error occurred. Please try again.',
          errorAt: DateTime.now(),
        ),
      );
    }
  }

  /// Start payment processing in WebView
  void startPaymentProcessing(String paymentUrl) {
    final currentState = state;
    if (currentState is PaymentLinkCreated) {
      logger.i('Starting payment processing');

      analyticsService.logEvent(
        name: 'payment_processing_started',
        parameters: {'payment_link_id': currentState.paymentLink.paymentLinkId},
      );

      emit(
        PaymentProcessing(
          paymentLink: currentState.paymentLink,
          currentUrl: paymentUrl,
          isLoading: true,
        ),
      );
    }
  }

  /// Update WebView loading state
  void updateWebViewLoading(bool isLoading) {
    final currentState = state;
    if (currentState is PaymentProcessing) {
      emit(currentState.copyWith(isLoading: isLoading));
    }
  }

  /// Update current URL in WebView
  void updateCurrentUrl(String url) {
    final currentState = state;
    if (currentState is PaymentProcessing) {
      logger.i('WebView URL updated: ${_sanitizeUrl(url)}');
      emit(currentState.copyWith(currentUrl: url));

      // Check if this is a completion URL
      if (paymentService.isPaymentCompletionUrl(url)) {
        _handlePaymentCompletion(url);
      }
    }
  }

  /// Handle payment completion from callback URL
  Future<void> _handlePaymentCompletion(String callbackUrl) async {
    try {
      logger.i('Processing payment completion');

      final result = await paymentService.parsePaymentResult(callbackUrl);

      result.fold(
        (failure) {
          final errorMessage = paymentService.getErrorMessage(failure);
          logger.e('Failed to parse payment result: $errorMessage');

          emit(PaymentError(message: errorMessage, errorAt: DateTime.now()));
        },
        (paymentResult) {
          _completePayment(paymentResult);
        },
      );
    } catch (e) {
      logger.e('Error handling payment completion: $e');
      emit(
        PaymentError(
          message: 'Failed to process payment result',
          errorAt: DateTime.now(),
        ),
      );
    }
  }

  /// Complete the payment with result
  void _completePayment(PaymentResult result) {
    final currentState = state;
    final paymentLink =
        currentState is PaymentProcessing ? currentState.paymentLink : null;

    logger.i('Payment completed with status: ${result.status}');

    // Track completion
    analyticsService.logEvent(
      name: 'payment_completed',
      parameters: {
        'status': result.status.toString(),
        'payment_id': result.paymentId ?? '',
        'amount': result.amount?.toString() ?? '',
      },
    );

    // Cancel timeout
    _cancelPaymentTimeout();

    emit(
      PaymentCompleted(
        result: result,
        originalPaymentLink: paymentLink,
        completedAt: DateTime.now(),
      ),
    );
  }

  /// Cancel payment process
  void cancelPayment({String reason = 'User cancelled'}) {
    final currentState = state;
    final paymentLink =
        currentState is PaymentProcessing
            ? currentState.paymentLink
            : (currentState is PaymentLinkCreated
                ? currentState.paymentLink
                : null);

    logger.i('Payment cancelled: $reason');

    analyticsService.logEvent(
      name: 'payment_cancelled',
      parameters: {
        'reason': reason,
        'payment_link_id': paymentLink?.paymentLinkId ?? '',
      },
    );

    // Cancel timeout
    _cancelPaymentTimeout();

    emit(
      PaymentCancelled(
        paymentLink: paymentLink,
        reason: reason,
        cancelledAt: DateTime.now(),
      ),
    );
  }

  /// Validate payment URL
  Future<void> validatePaymentUrl(String url) async {
    emit(PaymentUrlValidating(url: url));

    final result = await paymentService.validatePaymentUrl(url);

    result.fold(
      (failure) {
        final errorMessage = paymentService.getErrorMessage(failure);
        emit(
          PaymentUrlValidated(
            url: url,
            isValid: false,
            errorMessage: errorMessage,
          ),
        );
      },
      (isValid) {
        emit(PaymentUrlValidated(url: url, isValid: isValid));
      },
    );
  }

  /// Reset payment state to initial
  void resetPayment() {
    logger.i('Resetting payment state');
    _cancelPaymentTimeout();
    emit(const PaymentInitial());
  }

  /// Start payment timeout timer
  void _startPaymentTimeout(dynamic paymentLink, {Duration? timeout}) {
    _cancelPaymentTimeout();

    final timeoutDuration = timeout ?? _defaultTimeout;
    logger.i('Starting payment timeout: ${timeoutDuration.inMinutes} minutes');

    _paymentTimeout = Timer(timeoutDuration, () {
      logger.w('Payment timeout occurred');

      analyticsService.logEvent(
        name: 'payment_timeout',
        parameters: {
          'timeout_minutes': timeoutDuration.inMinutes,
          'payment_link_id': paymentLink?.paymentLinkId ?? '',
        },
      );

      emit(
        PaymentTimeout(
          paymentLink: paymentLink,
          timeoutDuration: timeoutDuration,
          timeoutAt: DateTime.now(),
        ),
      );
    });
  }

  /// Cancel payment timeout timer
  void _cancelPaymentTimeout() {
    _paymentTimeout?.cancel();
    _paymentTimeout = null;
  }

  /// Sanitize URL for logging (remove sensitive data)
  String _sanitizeUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final sanitizedParams = <String, String>{};

      // Keep only safe parameters for logging
      const safeParams = ['status', 'payment_link_id', 'callback_source'];
      for (final param in safeParams) {
        if (uri.queryParameters.containsKey(param)) {
          sanitizedParams[param] = uri.queryParameters[param]!;
        }
      }

      return uri.replace(queryParameters: sanitizedParams).toString();
    } catch (e) {
      return 'Invalid URL';
    }
  }

  @override
  Future<void> close() {
    _cancelPaymentTimeout();
    return super.close();
  }
}
