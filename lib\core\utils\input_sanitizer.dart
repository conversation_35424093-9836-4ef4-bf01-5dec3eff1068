import 'dart:convert';
import 'logger.dart';

/// Utility class for sanitizing user inputs and preventing injection attacks
class InputSanitizer {
  static final AppLogger _logger = AppLogger();

  /// Sanitize string input by removing potentially dangerous characters
  static String sanitizeString(String input) {
    if (input.isEmpty) return input;

    // Remove null bytes and control characters
    String sanitized = input.replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '');

    // Remove HTML tags
    sanitized = sanitized.replaceAll(RegExp(r'<[^>]*>'), '');

    // Remove script tags and their content
    sanitized = sanitized.replaceAll(
      RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false, dotAll: true),
      '',
    );

    // Remove SQL injection patterns
    sanitized = _removeSqlInjectionPatterns(sanitized);

    // Remove JavaScript injection patterns
    sanitized = _removeJavaScriptInjectionPatterns(sanitized);

    // Trim whitespace
    sanitized = sanitized.trim();

    return sanitized;
  }

  /// Sanitize email input
  static String sanitizeEmail(String email) {
    if (email.isEmpty) return email;

    // Basic email sanitization
    String sanitized = email.toLowerCase().trim();

    // Remove dangerous characters but keep valid email characters
    sanitized = sanitized.replaceAll(RegExp(r'[^\w@.\-+]'), '');

    return sanitized;
  }

  /// Sanitize phone number input
  static String sanitizePhoneNumber(String phone) {
    if (phone.isEmpty) return phone;

    // Keep only digits, +, -, (, ), and spaces
    String sanitized = phone.replaceAll(RegExp(r'[^\d+\-() ]'), '');

    return sanitized.trim();
  }

  /// Sanitize amount input
  static String sanitizeAmount(String amount) {
    if (amount.isEmpty) return amount;

    // Keep only digits and decimal point
    String sanitized = amount.replaceAll(RegExp(r'[^\d.]'), '');

    // Ensure only one decimal point
    final parts = sanitized.split('.');
    if (parts.length > 2) {
      sanitized = '${parts[0]}.${parts.sublist(1).join('')}';
    }

    return sanitized;
  }

  /// Sanitize description input
  static String sanitizeDescription(String description) {
    if (description.isEmpty) return description;

    String sanitized = sanitizeString(description);

    // Limit length to prevent buffer overflow
    if (sanitized.length > 500) {
      sanitized = sanitized.substring(0, 500);
      _logger.w('Description truncated to 500 characters');
    }

    return sanitized;
  }

  /// Sanitize metadata values
  static Map<String, String> sanitizeMetadata(Map<String, String>? metadata) {
    if (metadata == null || metadata.isEmpty) return {};

    final sanitized = <String, String>{};

    for (final entry in metadata.entries) {
      final key = sanitizeString(entry.key);
      final value = sanitizeString(entry.value);

      // Skip empty keys or values
      if (key.isNotEmpty && value.isNotEmpty) {
        // Limit key and value length
        final sanitizedKey = key.length > 50 ? key.substring(0, 50) : key;
        final sanitizedValue =
            value.length > 200 ? value.substring(0, 200) : value;

        sanitized[sanitizedKey] = sanitizedValue;
      }
    }

    return sanitized;
  }

  /// Validate and sanitize URL
  static String? sanitizeUrl(String url) {
    if (url.isEmpty) return null;

    try {
      final uri = Uri.parse(url);

      // Check for valid scheme
      if (!['http', 'https'].contains(uri.scheme)) {
        _logger.w('Invalid URL scheme: ${uri.scheme}');
        return null;
      }

      // Check for valid host
      if (uri.host.isEmpty) {
        _logger.w('Invalid URL host');
        return null;
      }

      // Remove dangerous query parameters
      final sanitizedParams = <String, String>{};
      for (final entry in uri.queryParameters.entries) {
        final key = sanitizeString(entry.key);
        final value = sanitizeString(entry.value);
        if (key.isNotEmpty && value.isNotEmpty) {
          sanitizedParams[key] = value;
        }
      }

      return uri.replace(queryParameters: sanitizedParams).toString();
    } catch (e) {
      _logger.e('Error sanitizing URL: $e');
      return null;
    }
  }

  /// Remove SQL injection patterns
  static String _removeSqlInjectionPatterns(String input) {
    final sqlPatterns = [
      RegExp(
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)',
        caseSensitive: false,
      ),
      RegExp(r'(--|#|/\*|\*/)', caseSensitive: false),
      RegExp(r'(\bOR\b|\bAND\b)(\s+\d+\s*=\s*\d+)', caseSensitive: false),
      RegExp(r"('\s*(OR|AND)\s*'\s*=\s*')", caseSensitive: false),
    ];

    String sanitized = input;
    for (final pattern in sqlPatterns) {
      sanitized = sanitized.replaceAll(pattern, '');
    }

    return sanitized;
  }

  /// Remove JavaScript injection patterns
  static String _removeJavaScriptInjectionPatterns(String input) {
    final jsPatterns = [
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'vbscript:', caseSensitive: false),
      RegExp(r'onload\s*=', caseSensitive: false),
      RegExp(r'onerror\s*=', caseSensitive: false),
      RegExp(r'onclick\s*=', caseSensitive: false),
      RegExp(r'onmouseover\s*=', caseSensitive: false),
      RegExp(r'eval\s*\(', caseSensitive: false),
      RegExp(r'document\.', caseSensitive: false),
      RegExp(r'window\.', caseSensitive: false),
    ];

    String sanitized = input;
    for (final pattern in jsPatterns) {
      sanitized = sanitized.replaceAll(pattern, '');
    }

    return sanitized;
  }

  /// Validate input length
  static bool validateLength(String input, int maxLength) {
    return input.length <= maxLength;
  }

  /// Validate email format
  static bool validateEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// Validate phone number format
  static bool validatePhoneNumber(String phone) {
    final phoneRegex = RegExp(r'^\+?[\d\s\-()]{10,15}$');
    return phoneRegex.hasMatch(phone);
  }

  /// Validate amount format
  static bool validateAmount(String amount) {
    final amountRegex = RegExp(r'^\d+(\.\d{1,2})?$');
    return amountRegex.hasMatch(amount);
  }

  /// Encode data for safe transmission
  static String encodeForTransmission(String data) {
    try {
      return base64Encode(utf8.encode(data));
    } catch (e) {
      _logger.e('Error encoding data: $e');
      return '';
    }
  }

  /// Decode data from transmission
  static String decodeFromTransmission(String encodedData) {
    try {
      return utf8.decode(base64Decode(encodedData));
    } catch (e) {
      _logger.e('Error decoding data: $e');
      return '';
    }
  }

  /// Check for suspicious patterns in input
  static bool containsSuspiciousPatterns(String input) {
    final suspiciousPatterns = [
      RegExp(r'<script', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'data:text/html', caseSensitive: false),
      RegExp(r'vbscript:', caseSensitive: false),
      RegExp(r'onload=', caseSensitive: false),
      RegExp(r'onerror=', caseSensitive: false),
      RegExp(r'eval\(', caseSensitive: false),
      RegExp(r'expression\(', caseSensitive: false),
      RegExp(r'url\(', caseSensitive: false),
      RegExp(r'@import', caseSensitive: false),
    ];

    for (final pattern in suspiciousPatterns) {
      if (pattern.hasMatch(input)) {
        _logger.w('Suspicious pattern detected in input');
        return true;
      }
    }

    return false;
  }

  /// Log sanitization event for security monitoring
  static void logSanitizationEvent(
    String inputType,
    String originalLength,
    String sanitizedLength,
  ) {
    _logger.i(
      'Input sanitized - Type: $inputType, Original: $originalLength chars, Sanitized: $sanitizedLength chars',
    );
  }
}
