import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/payments/payment_link.dart';
import '../entities/payments/payment_request.dart';
import '../entities/payments/payment_result.dart';
import '../entities/payments/payment_verification_response.dart';

/// Repository interface for payment operations
abstract class PaymentRepository {
  /// Create a payment link for the given request
  /// Returns [PaymentLink] on success or [Failure] on error
  Future<Either<Failure, PaymentLink>> createPaymentLink(
    PaymentRequest request,
  );

  /// Create a payment session (legacy approach)
  /// Returns [PaymentLink] on success or [Failure] on error
  Future<Either<Failure, PaymentLink>> createPaymentSession(
    PaymentRequest request,
  );

  /// Validate payment URL for security
  /// Returns true if URL is valid and safe to load
  bool validatePaymentUrl(String url);

  /// Parse payment result from callback URL
  /// Returns [PaymentResult] parsed from the URL parameters
  PaymentResult parsePaymentResult(String callbackUrl);

  /// Get payment status by payment link ID
  /// Returns [PaymentResult] on success or [Failure] on error
  Future<Either<Failure, PaymentResult>> getPaymentStatus(String paymentLinkId);

  /// Cancel a payment link
  /// Returns true on success or [Failure] on error
  Future<Either<Failure, bool>> cancelPaymentLink(String paymentLinkId);

  /// Verify payment transaction status and check invoice updates
  /// Returns [PaymentVerificationResponse] on success or [Failure] on error
  Future<Either<Failure, PaymentVerificationResponse>> verifyTransaction(
    String transactionId,
  );

  /// Force refresh transaction status with invoice updates
  /// Returns [PaymentVerificationResponse] on success or [Failure] on error
  Future<Either<Failure, PaymentVerificationResponse>> forceRefreshTransaction(
    String transactionId, {
    bool updateInvoice = true,
  });
}
