import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/mixins/analytics_mixin.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/utils/currency_formatter.dart';
import '../../../domain/entities/payments/payment_request.dart';
import '../../cubit/payment/payment_cubit.dart';
import '../../cubit/payment/payment_state.dart';
import '../../widgets/payment/payment_webview.dart';
import '../../../injection_container.dart' as di;

/// Screen for initiating payment process
class PaymentInitiationScreen extends StatefulWidget {
  final double amount;
  final String description;
  final String customerEmail;
  final String? invoiceNumber;
  final String? customerId;
  final String? customerName;
  final String? customerPhone;
  final String? referenceId;
  final Map<String, String>? metadata;

  const PaymentInitiationScreen({
    super.key,
    required this.amount,
    required this.description,
    required this.customerEmail,
    this.invoiceNumber,
    this.customerId,
    this.customerName,
    this.customerPhone,
    this.referenceId,
    this.metadata,
  });

  @override
  State<PaymentInitiationScreen> createState() =>
      _PaymentInitiationScreenState();
}

class _PaymentInitiationScreenState extends State<PaymentInitiationScreen>
    with AnalyticsMixin {
  @override
  String get screenName => 'payment_initiation';

  AnalyticsService get analytics => di.sl<AnalyticsService>();

  @override
  void initState() {
    super.initState();
    _trackScreenView();
  }

  void _trackScreenView() {
    trackEvent(
      'payment_initiation_screen_viewed',
      params: {
        'amount': widget.amount,
        'currency': 'INR',
        'has_invoice': widget.invoiceNumber != null,
        'has_customer_id': widget.customerId != null,
      },
    );
  }

  void _initiatePayment() {
    final paymentRequest = PaymentRequest(
      amount: widget.amount,
      description: widget.description,
      customerEmail: widget.customerEmail,
      invoiceNumber: widget.invoiceNumber,
      customerId: widget.customerId,
      customerName: widget.customerName,
      customerPhone: widget.customerPhone,
      referenceId: widget.referenceId,
      metadata: widget.metadata,
    );

    trackEvent(
      'payment_initiation_requested',
      params: {'amount': widget.amount, 'customer_email': widget.customerEmail},
    );

    context.read<PaymentCubit>().createPaymentLink(paymentRequest);
  }

  void _handlePaymentLinkCreated(PaymentLinkCreated state) {
    trackEvent(
      'payment_link_created_success',
      params: {
        'payment_link_id': state.paymentLink.paymentLinkId,
        'amount': state.paymentLink.amount,
      },
    );

    // Navigate to WebView
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => BlocProvider.value(
              value: context.read<PaymentCubit>(),
              child: PaymentWebView(
                paymentLink: state.paymentLink,
                onCancel: () => Navigator.of(context).pop(),
              ),
            ),
      ),
    );
  }

  void _handlePaymentCompleted(PaymentCompleted state) {
    trackEvent(
      'payment_completed_from_initiation',
      params: {
        'status': state.result.status.toString(),
        'payment_id': state.result.paymentId ?? '',
        'transaction_id': state.result.transactionId ?? '',
        'amount': state.result.amount?.toString() ?? '',
      },
    );

    // Show result dialog and return the PaymentResult
    _showPaymentResultDialog(state);
  }

  void _showPaymentResultDialog(PaymentCompleted state) {
    final isSuccess = state.isSuccess;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: isSuccess ? Colors.green : Colors.red,
                size: 28,
              ),
              const SizedBox(width: 8),
              AquaText.headline(
                isSuccess ? 'Payment Successful' : 'Payment Failed',
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isSuccess) ...[
                AquaText.body('Your payment has been processed successfully.'),
                const SizedBox(height: 16),
                if (state.result.paymentId != null) ...[
                  AquaText.caption('Payment ID:'),
                  AquaText.body(
                    state.result.paymentId!,
                    weight: AquaFontWeight.semibold,
                  ),
                  const SizedBox(height: 8),
                ],
                if (state.result.amount != null) ...[
                  AquaText.caption('Amount:'),
                  AquaText.body(
                    CurrencyFormatter.formatAsINR(state.result.amount!),
                    weight: AquaFontWeight.semibold,
                    color: acPrimaryBlue,
                  ),
                  const SizedBox(height: 8),
                ],
                if (state.result.invoiceNumber != null) ...[
                  AquaText.caption('Invoice:'),
                  AquaText.body(
                    state.result.invoiceNumber!,
                    weight: AquaFontWeight.semibold,
                  ),
                ],
              ] else ...[
                AquaText.body('Your payment could not be processed.'),
                const SizedBox(height: 16),
                if (state.result.errorMessage != null) ...[
                  AquaText.caption('Error:'),
                  AquaText.body(
                    state.result.errorMessage!,
                    color: Colors.red.shade700,
                  ),
                  const SizedBox(height: 8),
                ],
                if (state.result.invoiceNumber != null) ...[
                  AquaText.caption('Invoice:'),
                  AquaText.body(state.result.invoiceNumber!),
                ],
              ],
            ],
          ),
          actions: [
            if (!isSuccess)
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  Navigator.of(
                    context,
                  ).pop(null); // Return null for failed payment
                },
                child: AquaText.body('Close'),
              ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                // Return the PaymentResult to the calling screen
                Navigator.of(context).pop(state.result);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: isSuccess ? Colors.green : acPrimaryBlue,
                foregroundColor: acWhiteColor,
              ),
              child: AquaText.body('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: AquaText.headline('Payment'),
        backgroundColor: acPrimaryBlue,
        foregroundColor: acWhiteColor,
      ),
      body: BlocListener<PaymentCubit, PaymentState>(
        listener: (context, state) {
          if (state is PaymentLinkCreated) {
            _handlePaymentLinkCreated(state);
          } else if (state is PaymentCompleted) {
            _handlePaymentCompleted(state);
          } else if (state is PaymentCancelled) {
            Navigator.of(context).pop();
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildPaymentDetailsCard(),
              const SizedBox(height: 24),
              _buildPaymentButton(),
              const SizedBox(height: 16),
              _buildSecurityInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentDetailsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AquaText.headline('Payment Details', weight: AquaFontWeight.bold),
            const SizedBox(height: 16),
            _buildDetailRow(
              'Amount',
              CurrencyFormatter.formatAsINR(widget.amount),
            ),
            const SizedBox(height: 8),
            _buildDetailRow('Description', widget.description),
            const SizedBox(height: 8),
            _buildDetailRow('Email', widget.customerEmail),
            if (widget.invoiceNumber != null) ...[
              const SizedBox(height: 8),
              _buildDetailRow('Invoice', widget.invoiceNumber!),
            ],
            if (widget.customerName != null) ...[
              const SizedBox(height: 8),
              _buildDetailRow('Customer', widget.customerName!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: AquaText.caption('$label:', color: Colors.grey.shade600),
        ),
        Expanded(
          child:
              label == 'Amount'
                  ? AquaText.body(
                    value,
                    weight: AquaFontWeight.bold,
                    color: acPrimaryBlue,
                  )
                  : AquaText.body(value, weight: AquaFontWeight.medium),
        ),
      ],
    );
  }

  Widget _buildPaymentButton() {
    return BlocBuilder<PaymentCubit, PaymentState>(
      builder: (context, state) {
        final isLoading = state is PaymentLinkCreating;

        return ElevatedButton(
          onPressed: isLoading ? null : _initiatePayment,
          style: ElevatedButton.styleFrom(
            backgroundColor: acPrimaryBlue,
            foregroundColor: acWhiteColor,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child:
              isLoading
                  ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            acWhiteColor,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      AquaText.body(
                        'Creating Payment Link...',
                        color: acWhiteColor,
                      ),
                    ],
                  )
                  : AquaText.body(
                    'Pay Now',
                    weight: AquaFontWeight.bold,
                    color: acWhiteColor,
                  ),
        );
      },
    );
  }

  Widget _buildSecurityInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.security, color: Colors.blue.shade600, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: AquaText.caption(
              'Your payment is secured with industry-standard encryption. '
              'We do not store your payment information.',
              color: Colors.blue.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
