#!/bin/bash

# AquaPartner Payment System Build Script
# This script builds the Flutter app with secure payment configuration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to validate environment variables
validate_environment() {
    local env=$1
    print_status "Validating environment: $env"
    
    case $env in
        "dev")
            print_status "Development environment - using default values"
            ;;
        "staging")
            if [[ -z "$PAYMENT_SECRET_KEY" ]]; then
                print_error "PAYMENT_SECRET_KEY is required for staging environment"
                exit 1
            fi
            if [[ -z "$PAYMENT_API_KEY" ]]; then
                print_error "PAYMENT_API_KEY is required for staging environment"
                exit 1
            fi
            ;;
        "production")
            if [[ -z "$PAYMENT_SECRET_KEY" ]]; then
                print_error "PAYMENT_SECRET_KEY is required for production environment"
                exit 1
            fi
            if [[ -z "$PAYMENT_API_KEY" ]]; then
                print_error "PAYMENT_API_KEY is required for production environment"
                exit 1
            fi
            if [[ -z "$PAYMENT_ENCRYPTION_KEY" ]]; then
                print_error "PAYMENT_ENCRYPTION_KEY is required for production environment"
                exit 1
            fi
            # Validate key lengths for production
            if [[ ${#PAYMENT_SECRET_KEY} -lt 32 ]]; then
                print_error "PAYMENT_SECRET_KEY must be at least 32 characters for production"
                exit 1
            fi
            if [[ ${#PAYMENT_ENCRYPTION_KEY} -lt 32 ]]; then
                print_error "PAYMENT_ENCRYPTION_KEY must be at least 32 characters for production"
                exit 1
            fi
            ;;
        *)
            print_error "Invalid environment: $env. Must be dev, staging, or production"
            exit 1
            ;;
    esac
    
    print_success "Environment validation passed"
}

# Function to build the app with payment configuration
build_app() {
    local env=$1
    local platform=$2
    
    print_status "Building AquaPartner for $platform in $env environment"
    
    # Base dart-define arguments
    local dart_defines=(
        "--dart-define=ENVIRONMENT=$env"
    )
    
    # Add environment-specific secrets
    if [[ -n "$PAYMENT_SECRET_KEY" ]]; then
        dart_defines+=("--dart-define=PAYMENT_SECRET_KEY=$PAYMENT_SECRET_KEY")
    fi
    
    if [[ -n "$PAYMENT_API_KEY" ]]; then
        dart_defines+=("--dart-define=PAYMENT_API_KEY=$PAYMENT_API_KEY")
    fi
    
    if [[ -n "$PAYMENT_ENCRYPTION_KEY" ]]; then
        dart_defines+=("--dart-define=PAYMENT_ENCRYPTION_KEY=$PAYMENT_ENCRYPTION_KEY")
    fi
    
    # Build based on platform
    case $platform in
        "android")
            print_status "Building Android APK..."
            flutter build apk "${dart_defines[@]}" --release
            print_success "Android APK built successfully"
            ;;
        "ios")
            print_status "Building iOS app..."
            flutter build ios "${dart_defines[@]}" --release
            print_success "iOS app built successfully"
            ;;
        "web")
            print_status "Building web app..."
            flutter build web "${dart_defines[@]}" --release
            print_success "Web app built successfully"
            ;;
        *)
            print_error "Invalid platform: $platform. Must be android, ios, or web"
            exit 1
            ;;
    esac
}

# Function to run security checks
run_security_checks() {
    local env=$1
    
    print_status "Running security checks for $env environment"
    
    # Check for hardcoded secrets in code
    print_status "Checking for hardcoded secrets..."
    if grep -r "aquapartner_payment_secret" lib/ --exclude-dir=.git; then
        print_error "Found hardcoded payment secrets in code!"
        exit 1
    fi
    
    # Check for development keys in production
    if [[ "$env" == "production" ]]; then
        if [[ "$PAYMENT_SECRET_KEY" == *"dev_"* ]]; then
            print_error "Development keys detected in production build!"
            exit 1
        fi
    fi
    
    print_success "Security checks passed"
}

# Function to generate build report
generate_build_report() {
    local env=$1
    local platform=$2
    
    print_status "Generating build report..."
    
    local report_file="build_report_${env}_${platform}_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
AquaPartner Payment System Build Report
=====================================

Build Information:
- Environment: $env
- Platform: $platform
- Build Date: $(date)
- Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "N/A")
- Git Branch: $(git branch --show-current 2>/dev/null || echo "N/A")

Security Configuration:
- Payment Secret Key: $(if [[ -n "$PAYMENT_SECRET_KEY" ]]; then echo "Configured"; else echo "Not configured"; fi)
- Payment API Key: $(if [[ -n "$PAYMENT_API_KEY" ]]; then echo "Configured"; else echo "Not configured"; fi)
- Payment Encryption Key: $(if [[ -n "$PAYMENT_ENCRYPTION_KEY" ]]; then echo "Configured"; else echo "Not configured"; fi)

Environment-Specific Settings:
$(case $env in
    "dev")
        echo "- SSL Pinning: Disabled"
        echo "- Rate Limiting: Disabled"
        echo "- Debug Logging: Enabled"
        ;;
    "staging")
        echo "- SSL Pinning: Enabled"
        echo "- Rate Limiting: Enabled"
        echo "- Debug Logging: Enabled"
        ;;
    "production")
        echo "- SSL Pinning: Enabled"
        echo "- Rate Limiting: Enabled"
        echo "- Debug Logging: Disabled"
        echo "- Security Monitoring: Enabled"
        ;;
esac)

Build Artifacts:
$(case $platform in
    "android")
        echo "- APK: build/app/outputs/flutter-apk/app-release.apk"
        ;;
    "ios")
        echo "- iOS App: build/ios/iphoneos/Runner.app"
        ;;
    "web")
        echo "- Web App: build/web/"
        ;;
esac)

Security Checklist:
- [✓] No hardcoded secrets in source code
- [✓] Environment-appropriate security settings
- [✓] Proper key length validation (production)
- [✓] SSL pinning configured (staging/production)
- [✓] Rate limiting enabled (staging/production)

EOF

    print_success "Build report generated: $report_file"
}

# Main script execution
main() {
    print_status "AquaPartner Payment System Build Script"
    print_status "========================================"
    
    # Parse command line arguments
    local environment=${1:-"dev"}
    local platform=${2:-"android"}
    
    print_status "Environment: $environment"
    print_status "Platform: $platform"
    
    # Validate Flutter installation
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Validate environment and secrets
    validate_environment "$environment"
    
    # Run security checks
    run_security_checks "$environment"
    
    # Clean previous builds
    print_status "Cleaning previous builds..."
    flutter clean
    flutter pub get
    
    # Build the application
    build_app "$environment" "$platform"
    
    # Generate build report
    generate_build_report "$environment" "$platform"
    
    print_success "Build completed successfully!"
    print_status "Environment: $environment"
    print_status "Platform: $platform"
    
    # Show next steps
    echo ""
    print_status "Next Steps:"
    case $environment in
        "dev")
            print_status "- Test the app thoroughly in development"
            print_status "- Verify payment flows work correctly"
            ;;
        "staging")
            print_status "- Deploy to staging environment"
            print_status "- Run integration tests"
            print_status "- Verify SSL pinning works"
            ;;
        "production")
            print_status "- Review security checklist"
            print_status "- Deploy to production with monitoring"
            print_status "- Monitor payment success rates"
            ;;
    esac
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    echo "Usage: $0 <environment> <platform>"
    echo ""
    echo "Environments: dev, staging, production"
    echo "Platforms: android, ios, web"
    echo ""
    echo "Examples:"
    echo "  $0 dev android"
    echo "  $0 staging ios"
    echo "  $0 production android"
    echo ""
    echo "Environment Variables (required for staging/production):"
    echo "  PAYMENT_SECRET_KEY - Secret key for payment operations"
    echo "  PAYMENT_API_KEY - API key for payment service"
    echo "  PAYMENT_ENCRYPTION_KEY - Encryption key for sensitive data"
    exit 1
fi

# Run main function
main "$@"
