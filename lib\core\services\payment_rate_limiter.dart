import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../config/payment_environment_config.dart';
import '../utils/logger.dart';

/// Production-grade rate limiting service for payment operations
class PaymentRateLimiter {
  static final AppLogger _logger = AppLogger();
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  
  // Storage keys for rate limiting data
  static const String _rateLimitPrefix = 'payment_rate_limit_';
  static const String _globalLimitKey = 'payment_global_limit';
  
  /// Check if request is within rate limits
  static Future<bool> checkRateLimit(String identifier) async {
    try {
      final config = PaymentEnvironmentConfig.rateLimitConfig;
      
      // Skip rate limiting if disabled (e.g., in development)
      if (!config.enabled) {
        _logger.d('Rate limiting disabled for environment: ${PaymentEnvironmentConfig.environment}');
        return true;
      }
      
      final now = DateTime.now().millisecondsSinceEpoch;
      
      // Check per-identifier rate limit
      final identifierAllowed = await _checkIdentifierRateLimit(identifier, now, config);
      if (!identifierAllowed) {
        _logger.w('Rate limit exceeded for identifier: $identifier');
        return false;
      }
      
      // Check global rate limit
      final globalAllowed = await _checkGlobalRateLimit(now, config);
      if (!globalAllowed) {
        _logger.w('Global rate limit exceeded');
        return false;
      }
      
      // Record the request
      await _recordRequest(identifier, now);
      await _recordGlobalRequest(now);
      
      return true;
    } catch (e) {
      _logger.e('Error checking rate limit: $e');
      // In case of error, allow the request but log the issue
      return true;
    }
  }
  
  /// Check rate limit for specific identifier (user/IP)
  static Future<bool> _checkIdentifierRateLimit(
    String identifier,
    int now,
    RateLimitConfig config,
  ) async {
    try {
      final key = '$_rateLimitPrefix$identifier';
      final dataStr = await _storage.read(key: key);
      
      if (dataStr == null) {
        return true; // No previous requests
      }
      
      final data = json.decode(dataStr) as Map<String, dynamic>;
      final requests = List<int>.from(data['requests'] ?? []);
      
      // Remove old requests (older than 1 hour)
      requests.removeWhere((timestamp) => now - timestamp > 3600000);
      
      // Check minute limit
      final recentRequests = requests.where((timestamp) => 
          now - timestamp <= 60000).length;
      
      if (recentRequests >= config.maxRequestsPerMinute) {
        return false;
      }
      
      // Check hour limit
      if (requests.length >= config.maxRequestsPerHour) {
        return false;
      }
      
      return true;
    } catch (e) {
      _logger.e('Error checking identifier rate limit: $e');
      return true; // Allow on error
    }
  }
  
  /// Check global rate limit
  static Future<bool> _checkGlobalRateLimit(int now, RateLimitConfig config) async {
    try {
      final dataStr = await _storage.read(key: _globalLimitKey);
      
      if (dataStr == null) {
        return true; // No previous requests
      }
      
      final data = json.decode(dataStr) as Map<String, dynamic>;
      final requests = List<int>.from(data['requests'] ?? []);
      
      // Remove old requests (older than 1 hour)
      requests.removeWhere((timestamp) => now - timestamp > 3600000);
      
      // Global limit is 10x the per-user limit
      final globalHourlyLimit = config.maxRequestsPerHour * 10;
      final globalMinuteLimit = config.maxRequestsPerMinute * 10;
      
      // Check minute limit
      final recentRequests = requests.where((timestamp) => 
          now - timestamp <= 60000).length;
      
      if (recentRequests >= globalMinuteLimit) {
        return false;
      }
      
      // Check hour limit
      if (requests.length >= globalHourlyLimit) {
        return false;
      }
      
      return true;
    } catch (e) {
      _logger.e('Error checking global rate limit: $e');
      return true; // Allow on error
    }
  }
  
  /// Record a request for the identifier
  static Future<void> _recordRequest(String identifier, int timestamp) async {
    try {
      final key = '$_rateLimitPrefix$identifier';
      final dataStr = await _storage.read(key: key);
      
      Map<String, dynamic> data;
      if (dataStr != null) {
        data = json.decode(dataStr) as Map<String, dynamic>;
      } else {
        data = {'requests': <int>[]};
      }
      
      final requests = List<int>.from(data['requests'] ?? []);
      
      // Remove old requests (older than 1 hour)
      requests.removeWhere((ts) => timestamp - ts > 3600000);
      
      // Add current request
      requests.add(timestamp);
      
      // Update data
      data['requests'] = requests;
      data['lastRequest'] = timestamp;
      
      // Store updated data
      await _storage.write(key: key, value: json.encode(data));
    } catch (e) {
      _logger.e('Error recording request: $e');
    }
  }
  
  /// Record a global request
  static Future<void> _recordGlobalRequest(int timestamp) async {
    try {
      final dataStr = await _storage.read(key: _globalLimitKey);
      
      Map<String, dynamic> data;
      if (dataStr != null) {
        data = json.decode(dataStr) as Map<String, dynamic>;
      } else {
        data = {'requests': <int>[]};
      }
      
      final requests = List<int>.from(data['requests'] ?? []);
      
      // Remove old requests (older than 1 hour)
      requests.removeWhere((ts) => timestamp - ts > 3600000);
      
      // Add current request
      requests.add(timestamp);
      
      // Update data
      data['requests'] = requests;
      data['lastRequest'] = timestamp;
      
      // Store updated data
      await _storage.write(key: _globalLimitKey, value: json.encode(data));
    } catch (e) {
      _logger.e('Error recording global request: $e');
    }
  }
  
  /// Get rate limit status for identifier
  static Future<RateLimitStatus> getRateLimitStatus(String identifier) async {
    try {
      final config = PaymentEnvironmentConfig.rateLimitConfig;
      final now = DateTime.now().millisecondsSinceEpoch;
      
      if (!config.enabled) {
        return RateLimitStatus(
          identifier: identifier,
          requestsInLastMinute: 0,
          requestsInLastHour: 0,
          minuteLimit: config.maxRequestsPerMinute,
          hourLimit: config.maxRequestsPerHour,
          isLimited: false,
          resetTime: DateTime.now(),
        );
      }
      
      final key = '$_rateLimitPrefix$identifier';
      final dataStr = await _storage.read(key: key);
      
      if (dataStr == null) {
        return RateLimitStatus(
          identifier: identifier,
          requestsInLastMinute: 0,
          requestsInLastHour: 0,
          minuteLimit: config.maxRequestsPerMinute,
          hourLimit: config.maxRequestsPerHour,
          isLimited: false,
          resetTime: DateTime.now(),
        );
      }
      
      final data = json.decode(dataStr) as Map<String, dynamic>;
      final requests = List<int>.from(data['requests'] ?? []);
      
      // Count recent requests
      final requestsInLastMinute = requests.where((timestamp) => 
          now - timestamp <= 60000).length;
      final requestsInLastHour = requests.where((timestamp) => 
          now - timestamp <= 3600000).length;
      
      // Calculate reset time (when oldest request in current window expires)
      DateTime resetTime = DateTime.now();
      if (requests.isNotEmpty) {
        final oldestRequest = requests.reduce((a, b) => a < b ? a : b);
        resetTime = DateTime.fromMillisecondsSinceEpoch(oldestRequest + 3600000);
      }
      
      final isLimited = requestsInLastMinute >= config.maxRequestsPerMinute ||
                       requestsInLastHour >= config.maxRequestsPerHour;
      
      return RateLimitStatus(
        identifier: identifier,
        requestsInLastMinute: requestsInLastMinute,
        requestsInLastHour: requestsInLastHour,
        minuteLimit: config.maxRequestsPerMinute,
        hourLimit: config.maxRequestsPerHour,
        isLimited: isLimited,
        resetTime: resetTime,
      );
    } catch (e) {
      _logger.e('Error getting rate limit status: $e');
      return RateLimitStatus(
        identifier: identifier,
        requestsInLastMinute: 0,
        requestsInLastHour: 0,
        minuteLimit: 0,
        hourLimit: 0,
        isLimited: false,
        resetTime: DateTime.now(),
      );
    }
  }
  
  /// Clear rate limit data for identifier (admin function)
  static Future<void> clearRateLimitData(String identifier) async {
    try {
      final key = '$_rateLimitPrefix$identifier';
      await _storage.delete(key: key);
      _logger.i('Rate limit data cleared for identifier: $identifier');
    } catch (e) {
      _logger.e('Error clearing rate limit data: $e');
    }
  }
  
  /// Clear all rate limit data (admin function)
  static Future<void> clearAllRateLimitData() async {
    try {
      // This is a simplified approach - in production you might want
      // to iterate through all keys with the rate limit prefix
      await _storage.deleteAll();
      _logger.i('All rate limit data cleared');
    } catch (e) {
      _logger.e('Error clearing all rate limit data: $e');
    }
  }
}

/// Rate limit status information
class RateLimitStatus {
  final String identifier;
  final int requestsInLastMinute;
  final int requestsInLastHour;
  final int minuteLimit;
  final int hourLimit;
  final bool isLimited;
  final DateTime resetTime;
  
  const RateLimitStatus({
    required this.identifier,
    required this.requestsInLastMinute,
    required this.requestsInLastHour,
    required this.minuteLimit,
    required this.hourLimit,
    required this.isLimited,
    required this.resetTime,
  });
  
  @override
  String toString() {
    return 'RateLimitStatus{identifier: $identifier, '
           'minute: $requestsInLastMinute/$minuteLimit, '
           'hour: $requestsInLastHour/$hourLimit, '
           'limited: $isLimited, resetTime: $resetTime}';
  }
}
