import 'payment_link_model.dart';

/// Model for payment link API response wrapper
class PaymentLinkResponseModel {
  final bool success;
  final String message;
  final PaymentLinkModel? data;
  final String? error;
  final List<String>? requiredFields;

  PaymentLinkResponseModel({
    required this.success,
    required this.message,
    this.data,
    this.error,
    this.requiredFields,
  });

  /// Create model from JSON response
  factory PaymentLinkResponseModel.fromJson(Map<String, dynamic> json) {
    return PaymentLinkResponseModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null 
          ? PaymentLinkModel.fromJson(json['data']) 
          : null,
      error: json['error'],
      requiredFields: json['required_fields'] != null
          ? List<String>.from(json['required_fields'])
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'success': success,
      'message': message,
    };

    if (data != null) json['data'] = data!.toJson();
    if (error != null) json['error'] = error;
    if (requiredFields != null) json['required_fields'] = requiredFields;

    return json;
  }
}
