// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in aquapartner/test/data/datasources/remote/payment_api_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:aquapartner/core/network/api_client.dart' as _i4;
import 'package:aquapartner/core/utils/logger.dart' as _i6;
import 'package:dio/dio.dart' as _i2;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDio_0 extends _i1.SmartFake implements _i2.Dio {
  _FakeDio_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFlutterSecureStorage_1 extends _i1.SmartFake
    implements _i3.FlutterSecureStorage {
  _FakeFlutterSecureStorage_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponse_2<T> extends _i1.SmartFake implements _i2.Response<T> {
  _FakeResponse_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ApiClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiClient extends _i1.Mock implements _i4.ApiClient {
  MockApiClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.Dio get dio =>
      (super.noSuchMethod(
            Invocation.getter(#dio),
            returnValue: _FakeDio_0(this, Invocation.getter(#dio)),
          )
          as _i2.Dio);

  @override
  _i3.FlutterSecureStorage get secureStorage =>
      (super.noSuchMethod(
            Invocation.getter(#secureStorage),
            returnValue: _FakeFlutterSecureStorage_1(
              this,
              Invocation.getter(#secureStorage),
            ),
          )
          as _i3.FlutterSecureStorage);

  @override
  _i5.Future<_i2.Response<dynamic>> get(
    String? path, {
    Map<String, dynamic>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#get, [path], {#queryParams: queryParams}),
            returnValue: _i5.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_2<dynamic>(
                this,
                Invocation.method(#get, [path], {#queryParams: queryParams}),
              ),
            ),
          )
          as _i5.Future<_i2.Response<dynamic>>);

  @override
  _i5.Future<_i2.Response<dynamic>> post(String? path, {dynamic data}) =>
      (super.noSuchMethod(
            Invocation.method(#post, [path], {#data: data}),
            returnValue: _i5.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_2<dynamic>(
                this,
                Invocation.method(#post, [path], {#data: data}),
              ),
            ),
          )
          as _i5.Future<_i2.Response<dynamic>>);

  @override
  _i5.Future<_i2.Response<dynamic>> put(String? path, {dynamic data}) =>
      (super.noSuchMethod(
            Invocation.method(#put, [path], {#data: data}),
            returnValue: _i5.Future<_i2.Response<dynamic>>.value(
              _FakeResponse_2<dynamic>(
                this,
                Invocation.method(#put, [path], {#data: data}),
              ),
            ),
          )
          as _i5.Future<_i2.Response<dynamic>>);
}

/// A class which mocks [AppLogger].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppLogger extends _i1.Mock implements _i6.AppLogger {
  MockAppLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void d(String? message) => super.noSuchMethod(
    Invocation.method(#d, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void i(String? message) => super.noSuchMethod(
    Invocation.method(#i, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void w(String? message) => super.noSuchMethod(
    Invocation.method(#w, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void e(String? message, [dynamic error, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#e, [message, error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void enableFirebaseVerboseLogging() => super.noSuchMethod(
    Invocation.method(#enableFirebaseVerboseLogging, []),
    returnValueForMissingStub: null,
  );
}
