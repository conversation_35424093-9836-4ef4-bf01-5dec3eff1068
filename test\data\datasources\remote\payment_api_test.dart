import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:dio/dio.dart';

import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/data/models/payment/payment_request_model.dart';
import 'package:aquapartner/data/models/payment/payment_link_model.dart';
import 'package:aquapartner/data/models/payment/payment_link_response_model.dart';

import 'payment_api_test.mocks.dart';

@GenerateMocks([ApiClient, AppLogger])
void main() {
  late PaymentRemoteDataSourceImpl dataSource;
  late MockApiClient mockApiClient;
  late MockAppLogger mockLogger;

  setUp(() {
    mockApiClient = MockApiClient();
    mockLogger = MockAppLogger();
    dataSource = PaymentRemoteDataSourceImpl(
      apiClient: mockApiClient,
      logger: mockLogger,
    );
  });

  group('Payment API Tests', () {
    final tPaymentRequest = PaymentRequestModel(
      amount: 100.0,
      description: 'Test payment',
      customerEmail: '<EMAIL>',
      currency: 'INR',
      redirectUrl: 'https://example.com/callback',
    );

    group('createPaymentLink', () {
      test('should call correct API endpoint with correct data', () async {
        // arrange
        final paymentLinkModel = PaymentLinkModel(
          paymentLinkId: 'test_link_id',
          paymentLinkUrl: 'https://payments.zoho.in/checkout/test_link_id',
          amount: 100.0,
          currency: 'INR',
          description: 'Test payment',
          customerEmail: '<EMAIL>',
          status: 'active',
          createdTime: 1640995200,
          transactionId: 'test_transaction_id',
        );

        final mockResponse =
            PaymentLinkResponseModel(
              success: true,
              message: 'Payment link created successfully',
              data: paymentLinkModel,
            ).toJson();

        when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
          (_) async => Response(
            data: mockResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: ''),
          ),
        );

        // act
        final result = await dataSource.createPaymentLink(tPaymentRequest);

        // assert
        expect(result.paymentLinkId, equals('test_link_id'));
        expect(result.amount, equals(100.0));
        expect(result.customerEmail, equals('<EMAIL>'));

        verify(
          mockApiClient.post(
            '/api/zoho/payments/create-link',
            data: tPaymentRequest.toJson(),
          ),
        );
      });

      test('should throw ServerException when API returns error', () async {
        // arrange
        final errorResponse =
            PaymentLinkResponseModel(
              success: false,
              message: 'Payment link creation failed',
              data: null,
            ).toJson();

        when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
          (_) async => Response(
            data: errorResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: ''),
          ),
        );

        // act & assert
        expect(
          () => dataSource.createPaymentLink(tPaymentRequest),
          throwsA(isA<ServerException>()),
        );
      });

      test(
        'should throw ServerException when API returns non-200 status',
        () async {
          // arrange
          when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
            (_) async => Response(
              data: {},
              statusCode: 500,
              requestOptions: RequestOptions(path: ''),
            ),
          );

          // act & assert
          expect(
            () => dataSource.createPaymentLink(tPaymentRequest),
            throwsA(isA<ServerException>()),
          );
        },
      );
    });

    group('createPaymentSession', () {
      test('should call correct API endpoint', () async {
        // arrange
        final mockResponse = {
          'success': true,
          'message': 'Payment session created successfully',
          'data': {
            'payment_link_id': 'session_link_id',
            'payment_link_url':
                'https://payments.zoho.in/session/session_link_id',
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Test payment',
            'customer_email': '<EMAIL>',
            'status': 'active',
            'created_time': 1640995200,
            'transaction_id': 'session_transaction_id',
          },
        };

        when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
          (_) async => Response(
            data: mockResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: ''),
          ),
        );

        // act
        final result = await dataSource.createPaymentSession(tPaymentRequest);

        // assert
        expect(result.paymentLinkId, equals('session_link_id'));
        verify(
          mockApiClient.post(
            '/api/zoho/payments/create-session',
            data: tPaymentRequest.toJson(),
          ),
        );
      });
    });

    group('createFlutterPayment', () {
      test('should call Flutter-specific API endpoint', () async {
        // arrange
        final flutterResponse = {
          'success': true,
          'data': {
            'payment_session_id': 'flutter_session_id',
            'webview_url': 'https://flutter.payments.com/checkout',
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Test payment',
            'created_time': 1640995200,
            'transaction_id': 'flutter_transaction_id',
            'invoice_number': 'INV-001',
          },
        };

        when(mockApiClient.post(any, data: anyNamed('data'))).thenAnswer(
          (_) async => Response(
            data: flutterResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: ''),
          ),
        );

        // act
        final result = await dataSource.createFlutterPayment(tPaymentRequest);

        // assert
        expect(result.paymentLinkId, equals('flutter_session_id'));
        expect(
          result.paymentLinkUrl,
          equals('https://flutter.payments.com/checkout'),
        );
        verify(
          mockApiClient.post(
            '/api/flutter/payment/initiate',
            data: tPaymentRequest.toFlutterJson(),
          ),
        );
      });
    });

    group('getPaymentStatus', () {
      test('should call correct status endpoint', () async {
        // arrange
        const paymentLinkId = 'test_payment_link_id';
        final mockResponse = {
          'success': true,
          'message': 'Payment status retrieved',
          'data': {
            'payment_link_id': paymentLinkId,
            'payment_link_url':
                'https://payments.zoho.in/checkout/$paymentLinkId',
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Test payment',
            'customer_email': '<EMAIL>',
            'status': 'completed',
            'created_time': 1640995200,
            'transaction_id': 'completed_transaction_id',
          },
        };

        when(mockApiClient.get(any)).thenAnswer(
          (_) async => Response(
            data: mockResponse,
            statusCode: 200,
            requestOptions: RequestOptions(path: ''),
          ),
        );

        // act
        final result = await dataSource.getPaymentStatus(paymentLinkId);

        // assert
        expect(result.paymentLinkId, equals(paymentLinkId));
        expect(result.status, equals('completed'));
        verify(mockApiClient.get('/api/payment/status/$paymentLinkId'));
      });
    });

    group('error handling', () {
      test('should handle network exceptions', () async {
        // arrange
        when(mockApiClient.post(any, data: anyNamed('data'))).thenThrow(
          DioException(
            requestOptions: RequestOptions(path: ''),
            type: DioExceptionType.connectionTimeout,
          ),
        );

        // act & assert
        expect(
          () => dataSource.createPaymentLink(tPaymentRequest),
          throwsA(isA<ServerException>()),
        );
      });

      test('should handle unexpected exceptions', () async {
        // arrange
        when(
          mockApiClient.post(any, data: anyNamed('data')),
        ).thenThrow(Exception('Unexpected error'));

        // act & assert
        expect(
          () => dataSource.createPaymentLink(tPaymentRequest),
          throwsA(isA<ServerException>()),
        );
      });
    });
  });
}
