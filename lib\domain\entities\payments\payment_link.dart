import 'package:equatable/equatable.dart';

/// Domain entity representing a payment link
class PaymentLink extends Equatable {
  final String paymentLinkId;
  final String paymentLinkUrl;
  final double amount;
  final String currency;
  final String description;
  final String customerEmail;
  final String status;
  final DateTime createdTime;
  final DateTime? expiresAt;
  final String transactionId;
  final String? invoiceNumber;
  final String? customerId;
  final String? customerName;
  final String? customerPhone;
  final String? referenceId;
  final Map<String, String>? metadata;

  const PaymentLink({
    required this.paymentLinkId,
    required this.paymentLinkUrl,
    required this.amount,
    required this.currency,
    required this.description,
    required this.customerEmail,
    required this.status,
    required this.createdTime,
    this.expiresAt,
    required this.transactionId,
    this.invoiceNumber,
    this.customerId,
    this.customerName,
    this.customerPhone,
    this.referenceId,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        paymentLinkId,
        paymentLinkUrl,
        amount,
        currency,
        description,
        customerEmail,
        status,
        createdTime,
        expiresAt,
        transactionId,
        invoiceNumber,
        customerId,
        customerName,
        customerPhone,
        referenceId,
        metadata,
      ];
}
