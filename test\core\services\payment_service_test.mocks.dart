// Mocks generated by Mockito 5.4.6 from annotations
// in aquapartner/test/core/services/payment_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:aquapartner/core/error/failures.dart' as _i6;
import 'package:aquapartner/core/utils/logger.dart' as _i12;
import 'package:aquapartner/domain/entities/payments/payment_link.dart' as _i7;
import 'package:aquapartner/domain/entities/payments/payment_request.dart'
    as _i8;
import 'package:aquapartner/domain/entities/payments/payment_result.dart'
    as _i11;
import 'package:aquapartner/domain/repositories/payment_repository.dart' as _i2;
import 'package:aquapartner/domain/usecases/payment/create_payment_link_usecase.dart'
    as _i4;
import 'package:aquapartner/domain/usecases/payment/parse_payment_result_usecase.dart'
    as _i10;
import 'package:aquapartner/domain/usecases/payment/validate_payment_url_usecase.dart'
    as _i9;
import 'package:dartz/dartz.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePaymentRepository_0 extends _i1.SmartFake
    implements _i2.PaymentRepository {
  _FakePaymentRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [CreatePaymentLinkUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockCreatePaymentLinkUseCase extends _i1.Mock
    implements _i4.CreatePaymentLinkUseCase {
  MockCreatePaymentLinkUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PaymentRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakePaymentRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.PaymentRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.PaymentLink>> call(
    _i8.PaymentRequest? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i7.PaymentLink>>.value(
                  _FakeEither_1<_i6.Failure, _i7.PaymentLink>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i7.PaymentLink>>);
}

/// A class which mocks [ValidatePaymentUrlUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockValidatePaymentUrlUseCase extends _i1.Mock
    implements _i9.ValidatePaymentUrlUseCase {
  MockValidatePaymentUrlUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PaymentRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakePaymentRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.PaymentRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, bool>> call(String? url) =>
      (super.noSuchMethod(
            Invocation.method(#call, [url]),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, bool>>.value(
              _FakeEither_1<_i6.Failure, bool>(
                this,
                Invocation.method(#call, [url]),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, bool>>);
}

/// A class which mocks [ParsePaymentResultUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockParsePaymentResultUseCase extends _i1.Mock
    implements _i10.ParsePaymentResultUseCase {
  MockParsePaymentResultUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PaymentRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakePaymentRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.PaymentRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i11.PaymentResult>> call(
    String? callbackUrl,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [callbackUrl]),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i11.PaymentResult>>.value(
                  _FakeEither_1<_i6.Failure, _i11.PaymentResult>(
                    this,
                    Invocation.method(#call, [callbackUrl]),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i11.PaymentResult>>);
}

/// A class which mocks [AppLogger].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppLogger extends _i1.Mock implements _i12.AppLogger {
  MockAppLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void d(String? message) => super.noSuchMethod(
    Invocation.method(#d, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void i(String? message) => super.noSuchMethod(
    Invocation.method(#i, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void w(String? message) => super.noSuchMethod(
    Invocation.method(#w, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void e(String? message, [dynamic error, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#e, [message, error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void enableFirebaseVerboseLogging() => super.noSuchMethod(
    Invocation.method(#enableFirebaseVerboseLogging, []),
    returnValueForMissingStub: null,
  );
}
