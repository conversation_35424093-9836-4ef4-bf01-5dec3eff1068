import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/entities/payments/payment_verification_response.dart';
import 'package:aquapartner/domain/repositories/payment_repository.dart';
import 'package:aquapartner/domain/usecases/payments/verify_transaction_usecase.dart';

class MockPaymentRepository extends Mock implements PaymentRepository {}

void main() {
  group('VerifyTransaction UseCase', () {
    late VerifyTransaction useCase;
    late MockPaymentRepository mockRepository;

    setUp(() {
      mockRepository = MockPaymentRepository();
      useCase = VerifyTransaction(mockRepository);
    });

    const testTransactionId = 'txn_test123';

    test(
      'should return PaymentVerificationResponse when repository call is successful',
      () async {
        // Arrange
        final expectedResponse = PaymentVerificationResponse(
          success: true,
          message: 'Transaction verified successfully',
          data: PaymentVerificationData(
            transactionId: testTransactionId,
            paymentId: 'pay_123',
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: 'INV-2024-001',
            customerEmail: '<EMAIL>',
            paymentCompletedTime: '2024-01-15T10:30:00.000Z',
            invoiceStatusUpdated: true,
            invoicePaymentStatus: 'Paid',
          ),
        );

        when(
          () => mockRepository.verifyTransaction(testTransactionId),
        ).thenAnswer((_) async => Right(expectedResponse));

        // Act
        final result = await useCase(testTransactionId);

        // Assert
        expect(result, Right(expectedResponse));
        verify(
          () => mockRepository.verifyTransaction(testTransactionId),
        ).called(1);
      },
    );

    test('should return Failure when repository call fails', () async {
      // Arrange
      final expectedFailure = ServerFailure();

      when(
        () => mockRepository.verifyTransaction(testTransactionId),
      ).thenAnswer((_) async => Left(expectedFailure));

      // Act
      final result = await useCase(testTransactionId);

      // Assert
      expect(result, Left(expectedFailure));
      verify(
        () => mockRepository.verifyTransaction(testTransactionId),
      ).called(1);
    });
  });

  group('ForceRefreshTransaction UseCase', () {
    late ForceRefreshTransaction useCase;
    late MockPaymentRepository mockRepository;

    setUp(() {
      mockRepository = MockPaymentRepository();
      useCase = ForceRefreshTransaction(mockRepository);
    });

    const testTransactionId = 'txn_test123';

    test(
      'should return PaymentVerificationResponse when repository call is successful',
      () async {
        // Arrange
        const expectedResponse = PaymentVerificationResponse(
          success: true,
          message: 'Transaction verification completed with updates',
          data: PaymentVerificationData(
            transactionId: testTransactionId,
            paymentId: 'pay_123',
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: 'INV-2024-001',
            customerEmail: '<EMAIL>',
            paymentCompletedTime: '2024-01-15T10:30:00.000Z',
            invoiceStatusUpdated: true,
            invoicePaymentStatus: 'Paid',
            forceRefreshed: true,
            invoiceUpdated: true,
          ),
        );

        when(
          () => mockRepository.forceRefreshTransaction(
            testTransactionId,
            updateInvoice: true,
          ),
        ).thenAnswer((_) async => const Right(expectedResponse));

        // Act
        final result = await useCase(testTransactionId, updateInvoice: true);

        // Assert
        expect(result, const Right(expectedResponse));
        verify(
          () => mockRepository.forceRefreshTransaction(
            testTransactionId,
            updateInvoice: true,
          ),
        ).called(1);
      },
    );

    test(
      'should use default updateInvoice parameter when not specified',
      () async {
        // Arrange
        const expectedResponse = PaymentVerificationResponse(
          success: true,
          message: 'Transaction verification completed with updates',
          data: PaymentVerificationData(
            transactionId: testTransactionId,
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: 'INV-2024-001',
            customerEmail: '<EMAIL>',
            invoiceStatusUpdated: true,
            invoicePaymentStatus: 'Paid',
          ),
        );

        when(
          () => mockRepository.forceRefreshTransaction(
            testTransactionId,
            updateInvoice: true,
          ),
        ).thenAnswer((_) async => const Right(expectedResponse));

        // Act
        final result = await useCase(testTransactionId);

        // Assert
        expect(result, const Right(expectedResponse));
        verify(
          () => mockRepository.forceRefreshTransaction(
            testTransactionId,
            updateInvoice: true,
          ),
        ).called(1);
      },
    );

    test('should pass updateInvoice parameter correctly', () async {
      // Arrange
      const expectedResponse = PaymentVerificationResponse(
        success: true,
        message: 'Transaction verification completed',
        data: PaymentVerificationData(
          transactionId: testTransactionId,
          status: 'succeeded',
          amount: 1500.50,
          currency: 'INR',
          invoiceNumber: 'INV-2024-001',
          customerEmail: '<EMAIL>',
          invoiceStatusUpdated: false,
          forceRefreshed: true,
        ),
      );

      when(
        () => mockRepository.forceRefreshTransaction(
          testTransactionId,
          updateInvoice: false,
        ),
      ).thenAnswer((_) async => const Right(expectedResponse));

      // Act
      final result = await useCase(testTransactionId, updateInvoice: false);

      // Assert
      expect(result, const Right(expectedResponse));
      verify(
        () => mockRepository.forceRefreshTransaction(
          testTransactionId,
          updateInvoice: false,
        ),
      ).called(1);
    });

    test('should return Failure when repository call fails', () async {
      // Arrange
      final expectedFailure = NetworkFailure();

      when(
        () => mockRepository.forceRefreshTransaction(
          testTransactionId,
          updateInvoice: true,
        ),
      ).thenAnswer((_) async => Left(expectedFailure));

      // Act
      final result = await useCase(testTransactionId);

      // Assert
      expect(result, Left(expectedFailure));
      verify(
        () => mockRepository.forceRefreshTransaction(
          testTransactionId,
          updateInvoice: true,
        ),
      ).called(1);
    });
  });
}
