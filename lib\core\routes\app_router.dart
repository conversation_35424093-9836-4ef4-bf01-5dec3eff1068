import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/domain/entities/sales_order/sales_order.dart';
import 'package:aquapartner/presentation/screens/invoice_details_screen.dart';
import 'package:aquapartner/presentation/screens/q3_scheme_rules_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/entities/farmer_visit/farmer.dart';
import '../../domain/entities/invoices/invoice.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/payments/payment_result.dart';
import '../../injection_container.dart' as di;
import '../../presentation/cubit/payment/payment_cubit.dart';
import '../../presentation/screens/dashboard_screen.dart';
import '../../presentation/screens/famer_details_screen.dart';
import '../../presentation/screens/login_screen.dart';
import '../../presentation/screens/home_screen.dart';
import '../../presentation/screens/payment/payment_initiation_screen.dart';
import '../../presentation/screens/payment/payment_result_screen.dart';
import '../../presentation/screens/product_details_screen.dart';
import '../../presentation/screens/sales_order_details_screen.dart';
import '../../presentation/screens/verify_otp_screen.dart';
import '../../presentation/screens/version_test_screen.dart';
import '../services/analytics_service.dart';
import '../services/navigation_service.dart';

class AppRouter {
  static const String login = '/login';
  static const String verifyOtp = '/verify-otp';
  static const String home = '/home';
  static const String productDetails = '/product-details';
  static const String dashboard = '/dashboard';
  static const String q3SchemeRules = '/q3-scheme-rules';
  static const String salesOrderDetails = '/sales-order-details';
  static const String invoiceDetails = '/invoice-details';
  static const String farmerDetails = '/farmer-details';
  static const String versionTest = '/version-test';
  static const String paymentInitiation = '/payment-initiation';
  static const String paymentResult = '/payment-result';

  static NavigationService get _navigationService => di.sl<NavigationService>();

  static Future<dynamic> navigateToLogin({String? phoneNumber, String? name}) {
    return _navigationService.pushReplacement(
      login,
      arguments: {'phoneNumber': phoneNumber, 'name': name},
    );
  }

  static Future<dynamic> navigateToVerifyOtp({
    required String verificationId,
    required String phoneNumber,
    required String name,
  }) {
    return _navigationService.navigateTo(
      verifyOtp,
      arguments: {
        'verificationId': verificationId,
        'phoneNumber': phoneNumber,
        'name': name,
      },
    );
  }

  static Future<dynamic> navigateToHome() {
    // Use pushNamedAndRemoveUntil to clear the navigation stack
    // This prevents going back to the OTP verification screen
    return _navigationService.navigatorKey.currentState!
        .pushNamedAndRemoveUntil(
          home,
          (route) => false, // Remove all previous routes
        );
  }

  static Future<dynamic> navigateToProductDetails({Product? product}) {
    // Track navigation to product details with analytics
    final analyticsService = di.sl<AnalyticsService>();
    analyticsService.logScreenView(
      screenName: 'ProductDetailsScreen',
      screenClass: 'ProductDetailsScreen',
    );

    return _navigationService.pushNamed(
      productDetails,
      arguments: {'productDetails': product},
    );
  }

  static Future<dynamic> navigateToDashboard({required String customerId}) {
    return _navigationService.pushNamed(
      dashboard,
      arguments: {'customerId': customerId},
    );
  }

  static Future<dynamic> navigateToSchemeRules() {
    return _navigationService.pushNamed(q3SchemeRules);
  }

  //

  static Future<dynamic> navigateToSalesOrderDetails({
    required SalesOrder salesOrder,
  }) {
    return _navigationService.pushNamed(
      salesOrderDetails,
      arguments: {'salesOrder': salesOrder},
    );
  }

  static Future<dynamic> navigateToInvoiceDetails({required Invoice invoice}) {
    return _navigationService.pushNamed(
      invoiceDetails,
      arguments: {'invoice': invoice},
    );
  }

  static Future<dynamic> navigateToFarmerDetails({required Farmer farmer}) {
    return _navigationService.pushNamed(
      farmerDetails,
      arguments: {'farmer': farmer},
    );
  }

  static Future<dynamic> navigateToVersionTest() {
    return _navigationService.pushNamed(versionTest);
  }

  static Future<dynamic> navigateToPaymentInitiation({
    required double amount,
    required String description,
    required String customerEmail,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? referenceId,
    Map<String, String>? metadata,
  }) {
    // Track navigation to payment initiation with analytics
    final analyticsService = di.sl<AnalyticsService>();
    analyticsService.logScreenView(
      screenName: 'PaymentInitiationScreen',
      screenClass: 'PaymentInitiationScreen',
    );

    analyticsService.logEvent(
      name: 'payment_navigation_initiated',
      parameters: {
        'amount': amount,
        'currency': 'INR',
        'has_invoice': invoiceNumber != null,
        'has_customer_id': customerId != null,
      },
    );

    return _navigationService.pushNamed(
      paymentInitiation,
      arguments: {
        'amount': amount,
        'description': description,
        'customerEmail': customerEmail,
        'invoiceNumber': invoiceNumber,
        'customerId': customerId,
        'customerName': customerName,
        'customerPhone': customerPhone,
        'referenceId': referenceId,
        'metadata': metadata,
      },
    );
  }

  static Future<dynamic> navigateToPaymentResult({
    required PaymentResult result,
    VoidCallback? onContinue,
    VoidCallback? onRetry,
  }) {
    // Track navigation to payment result with analytics
    final analyticsService = di.sl<AnalyticsService>();
    analyticsService.logScreenView(
      screenName: 'PaymentResultScreen',
      screenClass: 'PaymentResultScreen',
    );

    analyticsService.logEvent(
      name: 'payment_result_navigation',
      parameters: {
        'status': result.status.toString(),
        'payment_id': result.paymentId ?? '',
        'amount': result.amount?.toString() ?? '',
      },
    );

    return _navigationService.pushNamed(
      paymentResult,
      arguments: {
        'paymentResult': result,
        'onContinue': onContinue,
        'onRetry': onRetry,
      },
    );
  }

  static Route<dynamic> generateRoute(RouteSettings settings) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    switch (settings.name) {
      case login:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => LoginScreen(initialPhoneNumber: args['phoneNumber']),
        );
      case verifyOtp:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => VerifyOtpScreen(phoneNumber: args['phoneNumber']),
        );
      case home:
        return MaterialPageRoute(builder: (_) => HomeScreen());

      case productDetails:
        final agrs = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) =>
                  ProductDetailsScreen(productDetails: agrs['productDetails']),
        );

      case dashboard:
        return MaterialPageRoute(builder: (_) => DashboardScreen());

      case q3SchemeRules:
        return MaterialPageRoute(builder: (_) => Q3SchemeRulesScreen());

      case salesOrderDetails:
        final agrs = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => SalesOrderDetailsScreen(salesOrder: agrs['salesOrder']),
        );

      case invoiceDetails:
        final agrs = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => InvoiceDetailsScreen(invoice: agrs['invoice']),
        );

      case farmerDetails:
        final agrs = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => FarmerDetailsScreen(farmer: agrs['farmer']),
        );

      case versionTest:
        return MaterialPageRoute(builder: (_) => const VersionTestScreen());

      case paymentInitiation:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => BlocProvider(
                create: (context) => di.sl<PaymentCubit>(),
                child: PaymentInitiationScreen(
                  amount: args['amount'],
                  description: args['description'],
                  customerEmail: args['customerEmail'],
                  invoiceNumber: args['invoiceNumber'],
                  customerId: args['customerId'],
                  customerName: args['customerName'],
                  customerPhone: args['customerPhone'],
                  referenceId: args['referenceId'],
                  metadata: args['metadata'],
                ),
              ),
        );

      case paymentResult:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => PaymentResultScreen(
                paymentResult: args['paymentResult'],
                onContinue: args['onContinue'],
                onRetry: args['onRetry'],
              ),
        );

      default:
        return _errorRoute('No route defined for ${settings.name}');
    }
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder:
          (_) => Scaffold(
            appBar: AppBar(title: AquaText.subheadline('Error')),
            body: Center(child: AquaText.body(message)),
          ),
    );
  }
}
