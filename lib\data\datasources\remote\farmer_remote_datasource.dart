import 'dart:convert';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/farmer_visits/farmer_data_model.dart';

/// Abstract interface for the remote data source (API calls).
abstract class FarmerRemoteDataSource {
  Future<FarmerDataModel?> getFarmerData(String customerId);
}

/// Placeholder implementation for the remote data source.
/// Replace this with your actual API call logic (e.g., using http, dio).
class FarmerRemoteDataSourceImpl implements FarmerRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  FarmerRemoteDataSourceImpl({required this.apiClient, required this.logger});

  @override
  Future<FarmerDataModel?> getFarmerData(String customerId) async {
    try {
      logger.i('Fetching farmer visits from API for customer: $customerId');
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/api/farmerVisits/$customerId',
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.data) as Map<String, dynamic>;
        final model = FarmerDataModel.fromJson(jsonData);
        logger.i(
          'Successfully fetched farmer data: ${model.results.length} farmers found',
        );
        return model;
      } else if (response.statusCode == 404) {
        // If 404, it might mean no farmers for this customer
        logger.w('No farmers found (404) for customer: $customerId');
        return null;
      } else {
        logger.e('Server error: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error fetching farmer data: ${e.toString()}');
      throw ServerException();
    }
  }
}
