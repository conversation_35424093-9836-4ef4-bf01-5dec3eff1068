import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/services/payment_completion_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/payments/payment_verification_response.dart';
import 'package:aquapartner/domain/usecases/payments/verify_transaction_usecase.dart';

class MockVerifyTransaction extends Mock implements VerifyTransaction {}

class MockForceRefreshTransaction extends Mock
    implements ForceRefreshTransaction {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('Payment Completion Flow Integration Test', () {
    late PaymentCompletionService service;
    late MockVerifyTransaction mockVerifyTransaction;
    late MockForceRefreshTransaction mockForceRefreshTransaction;
    late MockAppLogger mockLogger;

    setUp(() {
      mockVerifyTransaction = MockVerifyTransaction();
      mockForceRefreshTransaction = MockForceRefreshTransaction();
      mockLogger = MockAppLogger();
      service = PaymentCompletionService(
        verifyTransaction: mockVerifyTransaction,
        forceRefreshTransaction: mockForceRefreshTransaction,
        logger: mockLogger,
      );
    });

    const testTransactionId = 'txn_test123';
    const testInvoiceNumber = 'INV-2024-001';

    group('Successful Payment Flow', () {
      test(
        'should complete successfully when payment is verified and invoice is auto-updated',
        () async {
          // Arrange
          const verificationResponse = PaymentVerificationResponse(
            success: true,
            message: 'Transaction verified successfully',
            data: PaymentVerificationData(
              transactionId: testTransactionId,
              paymentId: 'pay_123',
              status: 'succeeded',
              amount: 1500.50,
              currency: 'INR',
              invoiceNumber: testInvoiceNumber,
              customerEmail: '<EMAIL>',
              paymentCompletedTime: '2024-01-15T10:30:00.000Z',
              invoiceStatusUpdated: true,
              invoicePaymentStatus: 'Paid',
            ),
          );

          when(
            () => mockVerifyTransaction(testTransactionId),
          ).thenAnswer((_) async => const Right(verificationResponse));

          // Act
          final result = await service.completePaymentFlow(
            transactionId: testTransactionId,
            invoiceNumber: testInvoiceNumber,
          );

          // Assert
          expect(result.isSuccess, true);
          expect(result.wasInvoiceUpdated, true);
          expect(result.wasAutoUpdated, true);
          expect(result.wasForceRefreshed, false);
          expect(result.statusMessage, contains('automatically updated'));

          verify(
            () => mockLogger.i(
              'Starting payment completion flow for transaction: $testTransactionId',
            ),
          ).called(1);
          verify(
            () =>
                mockLogger.i('Transaction verification successful: succeeded'),
          ).called(1);
          verify(
            () => mockLogger.i(
              'Invoice status was automatically updated to: Paid',
            ),
          ).called(1);

          // Should not call force refresh since auto-update was successful
          verifyNever(
            () => mockForceRefreshTransaction(
              any(),
              updateInvoice: any(named: 'updateInvoice'),
            ),
          );
        },
      );

      test(
        'should complete successfully with force refresh when initial verification shows no auto-update',
        () async {
          // Arrange
          const initialVerificationResponse = PaymentVerificationResponse(
            success: true,
            message: 'Transaction verified successfully',
            data: PaymentVerificationData(
              transactionId: testTransactionId,
              paymentId: 'pay_123',
              status: 'succeeded',
              amount: 1500.50,
              currency: 'INR',
              invoiceNumber: testInvoiceNumber,
              customerEmail: '<EMAIL>',
              paymentCompletedTime: '2024-01-15T10:30:00.000Z',
              invoiceStatusUpdated: false, // Not auto-updated initially
              invoicePaymentStatus: 'Overdue',
            ),
          );

          const forceRefreshResponse = PaymentVerificationResponse(
            success: true,
            message: 'Transaction verification completed with updates',
            data: PaymentVerificationData(
              transactionId: testTransactionId,
              paymentId: 'pay_123',
              status: 'succeeded',
              amount: 1500.50,
              currency: 'INR',
              invoiceNumber: testInvoiceNumber,
              customerEmail: '<EMAIL>',
              paymentCompletedTime: '2024-01-15T10:30:00.000Z',
              invoiceStatusUpdated: true, // Updated after force refresh
              invoicePaymentStatus: 'Paid',
              forceRefreshed: true,
            ),
          );

          when(
            () => mockVerifyTransaction(testTransactionId),
          ).thenAnswer((_) async => const Right(initialVerificationResponse));

          when(
            () => mockForceRefreshTransaction(
              testTransactionId,
              updateInvoice: true,
            ),
          ).thenAnswer((_) async => const Right(forceRefreshResponse));

          // Act
          final result = await service.completePaymentFlow(
            transactionId: testTransactionId,
            invoiceNumber: testInvoiceNumber,
          );

          // Assert
          expect(result.isSuccess, true);
          expect(result.wasInvoiceUpdated, true);
          expect(result.wasAutoUpdated, false);
          expect(result.wasForceRefreshed, true);
          expect(result.statusMessage, contains('Invoice status updated'));

          verify(
            () => mockLogger.w(
              'Invoice not auto-updated, attempting force refresh',
            ),
          ).called(1);
          verify(
            () => mockLogger.i(
              'Force refresh successful, invoice updated to: Paid',
            ),
          ).called(1);

          verify(() => mockVerifyTransaction(testTransactionId)).called(1);
          verify(
            () => mockForceRefreshTransaction(
              testTransactionId,
              updateInvoice: true,
            ),
          ).called(1);
        },
      );
    });

    group('Payment Failed Scenarios', () {
      test(
        'should return payment failed result when payment status is not successful',
        () async {
          // Arrange
          const verificationResponse = PaymentVerificationResponse(
            success: true,
            message: 'Transaction verified successfully',
            data: PaymentVerificationData(
              transactionId: testTransactionId,
              paymentId: 'pay_123',
              status: 'failed', // Payment failed
              amount: 1500.50,
              currency: 'INR',
              invoiceNumber: testInvoiceNumber,
              customerEmail: '<EMAIL>',
              invoiceStatusUpdated: false,
              invoicePaymentStatus: 'Overdue',
            ),
          );

          when(
            () => mockVerifyTransaction(testTransactionId),
          ).thenAnswer((_) async => const Right(verificationResponse));

          // Act
          final result = await service.completePaymentFlow(
            transactionId: testTransactionId,
            invoiceNumber: testInvoiceNumber,
          );

          // Assert
          expect(result.isSuccess, false);
          expect(result.isPartialSuccess, false);
          expect(result.error, contains('Payment failed with status: failed'));

          verify(
            () => mockLogger.w('Payment was not successful: failed'),
          ).called(1);

          // Should not attempt force refresh for failed payments
          verifyNever(
            () => mockForceRefreshTransaction(
              any(),
              updateInvoice: any(named: 'updateInvoice'),
            ),
          );
        },
      );
    });

    group('Error Handling', () {
      test(
        'should return failure result when initial verification fails',
        () async {
          // Arrange
          when(
            () => mockVerifyTransaction(testTransactionId),
          ).thenAnswer((_) async => Left(NetworkFailure()));

          // Act
          final result = await service.completePaymentFlow(
            transactionId: testTransactionId,
            invoiceNumber: testInvoiceNumber,
            forceRefreshIfNeeded:
                false, // Disable force refresh to test initial failure
          );

          // Assert
          expect(result.isSuccess, false);
          expect(result.error, contains('Transaction verification failed'));

          verify(
            () => mockLogger.e(
              'Initial transaction verification failed: NetworkFailure',
            ),
          ).called(1);

          // Should not attempt force refresh when disabled
          verifyNever(
            () => mockForceRefreshTransaction(
              any(),
              updateInvoice: any(named: 'updateInvoice'),
            ),
          );
        },
      );

      test(
        'should attempt force refresh when initial verification fails and force refresh is enabled',
        () async {
          // Arrange
          const forceRefreshResponse = PaymentVerificationResponse(
            success: true,
            message: 'Transaction verification completed with updates',
            data: PaymentVerificationData(
              transactionId: testTransactionId,
              status: 'succeeded',
              amount: 1500.50,
              currency: 'INR',
              invoiceNumber: testInvoiceNumber,
              customerEmail: '<EMAIL>',
              invoiceStatusUpdated: true,
              invoicePaymentStatus: 'Paid',
              forceRefreshed: true,
            ),
          );

          when(
            () => mockVerifyTransaction(testTransactionId),
          ).thenAnswer((_) async => Left(NetworkFailure()));

          when(
            () => mockForceRefreshTransaction(
              testTransactionId,
              updateInvoice: true,
            ),
          ).thenAnswer((_) async => Right(forceRefreshResponse));

          // Act
          final result = await service.completePaymentFlow(
            transactionId: testTransactionId,
            invoiceNumber: testInvoiceNumber,
            forceRefreshIfNeeded: true,
          );

          // Assert
          expect(result.isSuccess, true);
          expect(result.wasForceRefreshed, true);

          verify(
            () => mockLogger.i(
              'Attempting force refresh due to verification failure',
            ),
          ).called(1);
          verify(
            () => mockForceRefreshTransaction(
              testTransactionId,
              updateInvoice: true,
            ),
          ).called(1);
        },
      );

      test(
        'should return partial success when payment succeeds but invoice not updated and force refresh disabled',
        () async {
          // Arrange
          const verificationResponse = PaymentVerificationResponse(
            success: true,
            message: 'Transaction verified successfully',
            data: PaymentVerificationData(
              transactionId: testTransactionId,
              status: 'succeeded',
              amount: 1500.50,
              currency: 'INR',
              invoiceNumber: testInvoiceNumber,
              customerEmail: '<EMAIL>',
              invoiceStatusUpdated: false,
              invoicePaymentStatus: 'Overdue',
            ),
          );

          when(
            () => mockVerifyTransaction(testTransactionId),
          ).thenAnswer((_) async => const Right(verificationResponse));

          // Act
          final result = await service.completePaymentFlow(
            transactionId: testTransactionId,
            invoiceNumber: testInvoiceNumber,
            forceRefreshIfNeeded: false,
          );

          // Assert
          expect(result.isSuccess, false);
          expect(result.isPartialSuccess, true);
          expect(
            result.reason,
            contains('Invoice status not automatically updated'),
          );
          expect(
            result.statusMessage,
            contains('invoice status update is pending'),
          );

          verify(
            () => mockLogger.w(
              'Payment successful but invoice not automatically updated',
            ),
          ).called(1);
        },
      );
    });

    group('Retry Delay', () {
      test('should wait specified delay before force refresh', () async {
        // Arrange
        const initialVerificationResponse = PaymentVerificationResponse(
          success: true,
          message: 'Transaction verified successfully',
          data: PaymentVerificationData(
            transactionId: testTransactionId,
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: testInvoiceNumber,
            customerEmail: '<EMAIL>',
            invoiceStatusUpdated: false,
            invoicePaymentStatus: 'Overdue',
          ),
        );

        const forceRefreshResponse = PaymentVerificationResponse(
          success: true,
          message: 'Transaction verification completed with updates',
          data: PaymentVerificationData(
            transactionId: testTransactionId,
            status: 'succeeded',
            amount: 1500.50,
            currency: 'INR',
            invoiceNumber: testInvoiceNumber,
            customerEmail: '<EMAIL>',
            invoiceStatusUpdated: true,
            invoicePaymentStatus: 'Paid',
            forceRefreshed: true,
          ),
        );

        when(
          () => mockVerifyTransaction(testTransactionId),
        ).thenAnswer((_) async => const Right(initialVerificationResponse));

        when(
          () => mockForceRefreshTransaction(
            testTransactionId,
            updateInvoice: true,
          ),
        ).thenAnswer((_) async => const Right(forceRefreshResponse));

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await service.completePaymentFlow(
          transactionId: testTransactionId,
          invoiceNumber: testInvoiceNumber,
          retryDelay: const Duration(milliseconds: 100),
        );
        stopwatch.stop();

        // Assert
        expect(result.isSuccess, true);
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(100));

        verify(
          () => mockLogger.i('Waiting 0 seconds before force refresh'),
        ).called(1);
      });
    });
  });
}
