import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';
import '../../repositories/payment_repository.dart';

/// Use case for validating payment URLs
class ValidatePaymentUrlUseCase implements UseCase<bool, String> {
  final PaymentRepository repository;

  ValidatePaymentUrlUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(String url) async {
    try {
      final isValid = repository.validatePaymentUrl(url);
      return Right(isValid);
    } catch (e) {
      return Left(ValidationFailure('Invalid URL format: ${e.toString()}'));
    }
  }
}

/// Parameters for URL validation
class ValidatePaymentUrlParams {
  final String url;

  ValidatePaymentUrlParams({required this.url});
}
