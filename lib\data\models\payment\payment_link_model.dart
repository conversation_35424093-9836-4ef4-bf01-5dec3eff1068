import '../../../domain/entities/payments/payment_link.dart';

/// Data model for payment link API responses
class PaymentLinkModel {
  final String paymentLinkId;
  final String paymentLinkUrl;
  final double amount;
  final String currency;
  final String description;
  final String customerEmail;
  final String status;
  final int createdTime;
  final int? expiresAt;
  final String transactionId;
  final String? invoiceNumber;
  final String? customerId;
  final String? customerName;
  final String? customerPhone;
  final String? referenceId;
  final bool sendEmail;
  final bool sendSms;
  final bool partialPayments;

  PaymentLinkModel({
    required this.paymentLinkId,
    required this.paymentLinkUrl,
    required this.amount,
    required this.currency,
    required this.description,
    required this.customerEmail,
    required this.status,
    required this.createdTime,
    this.expiresAt,
    required this.transactionId,
    this.invoiceNumber,
    this.customerId,
    this.customerName,
    this.customerPhone,
    this.referenceId,
    this.sendEmail = true,
    this.sendSms = false,
    this.partialPayments = false,
  });

  /// Create model from JSON response
  factory PaymentLinkModel.fromJson(Map<String, dynamic> json) {
    return PaymentLinkModel(
      paymentLinkId: json['payment_link_id'] ?? '',
      paymentLinkUrl: json['payment_link_url'] ?? '',
      amount: _parseDouble(json['amount']),
      currency: json['currency'] ?? 'INR',
      description: json['description'] ?? '',
      customerEmail: json['customer_email'] ?? '',
      status: json['status'] ?? '',
      createdTime: _parseInt(json['created_time']),
      expiresAt:
          json['expires_at'] != null ? _parseInt(json['expires_at']) : null,
      transactionId: json['transaction_id'] ?? '',
      invoiceNumber: json['invoice_number'],
      customerId: json['customer_id'],
      customerName: json['customer_name'],
      customerPhone: json['customer_phone'],
      referenceId: json['reference_id'],
      sendEmail: _parseBool(json['send_email'], defaultValue: true),
      sendSms: _parseBool(json['send_sms'], defaultValue: false),
      partialPayments: _parseBool(
        json['partial_payments'],
        defaultValue: false,
      ),
    );
  }

  /// Safely parse double from dynamic value (handles both String and num)
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// Safely parse int from dynamic value (handles both String and num)
  static int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// Safely parse bool from dynamic value (handles String, bool, and int)
  static bool _parseBool(dynamic value, {required bool defaultValue}) {
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1') return true;
      if (lowerValue == 'false' || lowerValue == '0') return false;
      return defaultValue;
    }
    if (value is int) return value != 0;
    return defaultValue;
  }

  /// Convert to JSON for API requests
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'payment_link_id': paymentLinkId,
      'payment_link_url': paymentLinkUrl,
      'amount': amount,
      'currency': currency,
      'description': description,
      'customer_email': customerEmail,
      'status': status,
      'created_time': createdTime,
      'transaction_id': transactionId,
      'send_email': sendEmail,
      'send_sms': sendSms,
      'partial_payments': partialPayments,
    };

    // Add optional fields if they exist
    if (expiresAt != null) json['expires_at'] = expiresAt;
    if (invoiceNumber != null) json['invoice_number'] = invoiceNumber;
    if (customerId != null) json['customer_id'] = customerId;
    if (customerName != null) json['customer_name'] = customerName;
    if (customerPhone != null) json['customer_phone'] = customerPhone;
    if (referenceId != null) json['reference_id'] = referenceId;

    return json;
  }

  /// Convert to domain entity
  PaymentLink toEntity() {
    return PaymentLink(
      paymentLinkId: paymentLinkId,
      paymentLinkUrl: paymentLinkUrl,
      amount: amount,
      currency: currency,
      description: description,
      customerEmail: customerEmail,
      status: status,
      createdTime: DateTime.fromMillisecondsSinceEpoch(createdTime * 1000),
      expiresAt:
          expiresAt != null
              ? DateTime.fromMillisecondsSinceEpoch(expiresAt! * 1000)
              : null,
      transactionId: transactionId,
      invoiceNumber: invoiceNumber,
      customerId: customerId,
      customerName: customerName,
      customerPhone: customerPhone,
      referenceId: referenceId,
    );
  }
}
