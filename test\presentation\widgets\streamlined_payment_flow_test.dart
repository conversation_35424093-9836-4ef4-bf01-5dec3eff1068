import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:aquapartner/domain/entities/invoices/invoice.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_link.dart';
import 'package:aquapartner/domain/entities/payments/payment_result.dart';
import 'package:aquapartner/presentation/cubit/payment/payment_cubit.dart';
import 'package:aquapartner/core/utils/logger.dart';

@GenerateMocks([PaymentCubit, AppLogger])
import 'streamlined_payment_flow_test.mocks.dart';

/// Test suite for the streamlined payment flow
/// 
/// This test validates that the new direct navigation from invoices list
/// to payment WebView works correctly, bypassing the intermediate screen.
void main() {
  group('Streamlined Payment Flow Tests', () {
    late MockPaymentCubit mockPaymentCubit;
    late MockAppLogger mockLogger;
    
    setUp(() {
      mockPaymentCubit = MockPaymentCubit();
      mockLogger = MockAppLogger();
    });

    test('should create correct payment request for invoice', () {
      // Arrange
      final invoice = Invoice(
        invoiceId: 1,
        invoiceNumber: 'INV-001',
        customerId: 'CUST-123',
        total: 1500.0,
        subTotal: 1200.0,
        invoiceStatus: 'Overdue',
        invoiceDate: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(days: 30)),
        items: [],
      );

      // Act
      final paymentRequest = PaymentRequest(
        amount: invoice.total,
        description: 'Payment for Invoice ${invoice.invoiceNumber}',
        customerEmail: '<EMAIL>',
        currency: 'INR',
        invoiceNumber: invoice.invoiceNumber,
        customerId: invoice.customerId,
        customerName: 'Customer Name',
        metadata: {
          'invoice_id': invoice.invoiceId.toString(),
          'invoice_number': invoice.invoiceNumber,
          'source': 'invoices_page_direct',
          'flow_type': 'streamlined',
        },
      );

      // Assert
      expect(paymentRequest.amount, equals(1500.0));
      expect(paymentRequest.description, equals('Payment for Invoice INV-001'));
      expect(paymentRequest.invoiceNumber, equals('INV-001'));
      expect(paymentRequest.customerId, equals('CUST-123'));
      expect(paymentRequest.metadata?['source'], equals('invoices_page_direct'));
      expect(paymentRequest.metadata?['flow_type'], equals('streamlined'));
    });

    test('should validate payment request correctly', () {
      // Arrange
      final validPaymentRequest = PaymentRequest(
        amount: 1500.0,
        description: 'Payment for Invoice INV-001',
        customerEmail: '<EMAIL>',
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-123',
      );

      final invalidPaymentRequest = PaymentRequest(
        amount: 0.0, // Invalid amount
        description: '',
        customerEmail: 'invalid-email', // Invalid email
        currency: 'INR',
      );

      // Act & Assert
      expect(validPaymentRequest.validate(), isEmpty);
      expect(invalidPaymentRequest.validate(), isNotEmpty);
    });

    test('should create payment result correctly for success', () {
      // Arrange & Act
      final successResult = PaymentResult.success(
        paymentId: 'PAY-123',
        transactionId: 'TXN-456',
        invoiceNumber: 'INV-001',
        amount: 1500.0,
        paymentLinkId: 'PL-789',
      );

      // Assert
      expect(successResult.isSuccess, isTrue);
      expect(successResult.status, equals(PaymentStatus.success));
      expect(successResult.paymentId, equals('PAY-123'));
      expect(successResult.transactionId, equals('TXN-456'));
      expect(successResult.invoiceNumber, equals('INV-001'));
      expect(successResult.amount, equals(1500.0));
    });

    test('should create payment result correctly for failure', () {
      // Arrange & Act
      final failureResult = PaymentResult.failed(
        errorMessage: 'Payment declined',
        invoiceNumber: 'INV-001',
        paymentLinkId: 'PL-789',
      );

      // Assert
      expect(failureResult.isFailed, isTrue);
      expect(failureResult.status, equals(PaymentStatus.failed));
      expect(failureResult.errorMessage, equals('Payment declined'));
      expect(failureResult.invoiceNumber, equals('INV-001'));
    });

    test('should create payment result correctly for cancellation', () {
      // Arrange & Act
      final cancelledResult = PaymentResult.cancelled(
        invoiceNumber: 'INV-001',
        paymentLinkId: 'PL-789',
      );

      // Assert
      expect(cancelledResult.isCancelled, isTrue);
      expect(cancelledResult.status, equals(PaymentStatus.cancelled));
      expect(cancelledResult.invoiceNumber, equals('INV-001'));
    });

    test('should track payment events correctly', () {
      // This test would verify that the correct analytics events are tracked
      // during the streamlined payment flow
      
      // Arrange
      final invoice = Invoice(
        invoiceId: 1,
        invoiceNumber: 'INV-001',
        customerId: 'CUST-123',
        total: 1500.0,
        subTotal: 1200.0,
        invoiceStatus: 'Overdue',
        invoiceDate: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(days: 30)),
        items: [],
      );

      // Act & Assert
      // Verify that the following events would be tracked:
      // 1. payment_initiation_attempt_direct
      // 2. payment_link_created
      // 3. payment_webview_opened
      // 4. payment_completed/failed/cancelled
      
      expect(invoice.invoiceStatus, equals('Overdue'));
      expect(invoice.total, equals(1500.0));
    });
  });

  group('Payment Flow Validation Tests', () {
    test('should only allow payment for overdue invoices', () {
      // Arrange
      final overdueInvoice = Invoice(
        invoiceId: 1,
        invoiceNumber: 'INV-001',
        customerId: 'CUST-123',
        total: 1500.0,
        subTotal: 1200.0,
        invoiceStatus: 'Overdue',
        invoiceDate: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(days: 30)),
        items: [],
      );

      final paidInvoice = Invoice(
        invoiceId: 2,
        invoiceNumber: 'INV-002',
        customerId: 'CUST-123',
        total: 1500.0,
        subTotal: 1200.0,
        invoiceStatus: 'Paid',
        invoiceDate: DateTime.now(),
        dueDate: DateTime.now().add(const Duration(days: 30)),
        items: [],
      );

      // Act & Assert
      expect(overdueInvoice.invoiceStatus, equals('Overdue'));
      expect(paidInvoice.invoiceStatus, equals('Paid'));
      
      // Only overdue invoices should be eligible for payment
      expect(overdueInvoice.invoiceStatus == 'Overdue', isTrue);
      expect(paidInvoice.invoiceStatus == 'Overdue', isFalse);
    });
  });
}
