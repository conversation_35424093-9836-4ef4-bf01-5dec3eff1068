import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dio/dio.dart';

import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/data/models/payment/payment_verification_response_model.dart';

class MockApiClient extends Mock implements ApiClient {}
class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('PaymentRemoteDataSource - Transaction Verification', () {
    late PaymentRemoteDataSourceImpl dataSource;
    late MockApiClient mockApiClient;
    late MockAppLogger mockLogger;

    setUp(() {
      mockApiClient = MockApiClient();
      mockLogger = MockAppLogger();
      dataSource = PaymentRemoteDataSourceImpl(
        apiClient: mockApiClient,
        logger: mockLogger,
      );
    });

    group('verifyTransaction', () {
      const testTransactionId = 'txn_test123';
      
      test('should return PaymentVerificationResponseModel when verification is successful', () async {
        // Arrange
        final responseData = {
          'success': true,
          'message': 'Transaction verified successfully',
          'data': {
            'transaction_id': testTransactionId,
            'payment_id': 'pay_123',
            'status': 'succeeded',
            'amount': 1500.50,
            'currency': 'INR',
            'invoice_number': 'INV-2024-001',
            'customer_email': '<EMAIL>',
            'payment_completed_time': '2024-01-15T10:30:00.000Z',
            'invoice_status_updated': true,
            'invoice_payment_status': 'Paid',
          },
        };

        when(() => mockApiClient.get('/api/verify-transaction/$testTransactionId'))
            .thenAnswer((_) async => Response(
                  data: responseData,
                  statusCode: 200,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act
        final result = await dataSource.verifyTransaction(testTransactionId);

        // Assert
        expect(result.success, true);
        expect(result.data?.transactionId, testTransactionId);
        expect(result.data?.status, 'succeeded');
        expect(result.data?.invoiceStatusUpdated, true);
        expect(result.data?.invoicePaymentStatus, 'Paid');
        
        verify(() => mockLogger.i('Verifying transaction: $testTransactionId')).called(1);
        verify(() => mockLogger.i('Transaction verification successful: succeeded')).called(1);
        verify(() => mockLogger.i('Invoice status was automatically updated to: Paid')).called(1);
      });

      test('should throw ServerException when transaction not found (404)', () async {
        // Arrange
        when(() => mockApiClient.get('/api/verify-transaction/$testTransactionId'))
            .thenAnswer((_) async => Response(
                  statusCode: 404,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act & Assert
        expect(
          () => dataSource.verifyTransaction(testTransactionId),
          throwsA(isA<ServerException>()),
        );
        
        verify(() => mockLogger.e('Transaction not found: $testTransactionId')).called(1);
      });

      test('should throw ServerException when verification fails', () async {
        // Arrange
        final responseData = {
          'success': false,
          'message': 'Transaction verification failed',
        };

        when(() => mockApiClient.get('/api/verify-transaction/$testTransactionId'))
            .thenAnswer((_) async => Response(
                  data: responseData,
                  statusCode: 200,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act & Assert
        expect(
          () => dataSource.verifyTransaction(testTransactionId),
          throwsA(isA<ServerException>()),
        );
        
        verify(() => mockLogger.e('Transaction verification failed: Transaction verification failed')).called(1);
      });

      test('should throw ServerException when API returns error status', () async {
        // Arrange
        when(() => mockApiClient.get('/api/verify-transaction/$testTransactionId'))
            .thenAnswer((_) async => Response(
                  statusCode: 500,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act & Assert
        expect(
          () => dataSource.verifyTransaction(testTransactionId),
          throwsA(isA<ServerException>()),
        );
        
        verify(() => mockLogger.e('Transaction verification failed with status: 500')).called(1);
      });
    });

    group('forceRefreshTransaction', () {
      const testTransactionId = 'txn_test123';
      
      test('should return PaymentVerificationResponseModel when force refresh is successful', () async {
        // Arrange
        final responseData = {
          'success': true,
          'message': 'Transaction verification completed with updates',
          'data': {
            'transaction_id': testTransactionId,
            'payment_id': 'pay_123',
            'status': 'succeeded',
            'amount': 1500.50,
            'currency': 'INR',
            'invoice_number': 'INV-2024-001',
            'customer_email': '<EMAIL>',
            'payment_completed_time': '2024-01-15T10:30:00.000Z',
            'invoice_status_updated': true,
            'invoice_payment_status': 'Paid',
            'force_refreshed': true,
            'invoice_updated': true,
          },
        };

        final expectedRequestData = {
          'forceRefresh': true,
          'updateInvoice': true,
        };

        when(() => mockApiClient.post('/api/verify-transaction/$testTransactionId', data: expectedRequestData))
            .thenAnswer((_) async => Response(
                  data: responseData,
                  statusCode: 200,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act
        final result = await dataSource.forceRefreshTransaction(testTransactionId, updateInvoice: true);

        // Assert
        expect(result.success, true);
        expect(result.data?.transactionId, testTransactionId);
        expect(result.data?.status, 'succeeded');
        expect(result.data?.invoiceStatusUpdated, true);
        expect(result.data?.forceRefreshed, true);
        
        verify(() => mockLogger.i('Force refreshing transaction: $testTransactionId (updateInvoice: true)')).called(1);
        verify(() => mockLogger.i('Transaction force refresh successful: succeeded')).called(1);
        verify(() => mockLogger.i('Invoice status was updated to: Paid')).called(1);
      });

      test('should use correct request data when updateInvoice is false', () async {
        // Arrange
        final responseData = {
          'success': true,
          'message': 'Transaction verification completed',
          'data': {
            'transaction_id': testTransactionId,
            'status': 'succeeded',
            'amount': 1500.50,
            'currency': 'INR',
            'invoice_number': 'INV-2024-001',
            'customer_email': '<EMAIL>',
            'invoice_status_updated': false,
            'force_refreshed': true,
          },
        };

        final expectedRequestData = {
          'forceRefresh': true,
          'updateInvoice': false,
        };

        when(() => mockApiClient.post('/api/verify-transaction/$testTransactionId', data: expectedRequestData))
            .thenAnswer((_) async => Response(
                  data: responseData,
                  statusCode: 200,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act
        final result = await dataSource.forceRefreshTransaction(testTransactionId, updateInvoice: false);

        // Assert
        expect(result.success, true);
        expect(result.data?.invoiceStatusUpdated, false);
        expect(result.data?.forceRefreshed, true);
        
        verify(() => mockApiClient.post('/api/verify-transaction/$testTransactionId', data: expectedRequestData)).called(1);
      });

      test('should throw ServerException when force refresh fails', () async {
        // Arrange
        when(() => mockApiClient.post('/api/verify-transaction/$testTransactionId', data: any(named: 'data')))
            .thenAnswer((_) async => Response(
                  statusCode: 500,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act & Assert
        expect(
          () => dataSource.forceRefreshTransaction(testTransactionId),
          throwsA(isA<ServerException>()),
        );
        
        verify(() => mockLogger.e('Transaction force refresh failed with status: 500')).called(1);
      });
    });
  });
}
