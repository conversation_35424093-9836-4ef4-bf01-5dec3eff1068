import 'dart:convert';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/dues/dues.dart';
import '../../models/dues/dues_model.dart';

abstract class DuesRemoteDataSource {
  Future<DuesSummary> getDues(String customerId);
}

class DuesRemoteDataSourceImpl implements DuesRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  DuesRemoteDataSourceImpl({required this.apiClient, required this.logger});

  @override
  Future<DuesSummary> getDues(String customerId) async {
    logger.i('Fetching sales orders from API for customer: $customerId');
    final response = await apiClient.get(
      '${AppConstants.baseUrl}/api/dues/$customerId',
    );

    if (response.statusCode == 200) {
      logger.i('Successfully fetched sales orders from API');
      final jsonData = json.decode(response.data);
      // Extract the results array which contains aging groups
      final results = jsonData['results'] as List<dynamic>;

      // Create a list to store all aging groups
      final agingGroups = <DuesAgingGroup>[];

      // Calculate total due across all aging groups
      double totalDue = 0;

      // Process each aging group
      for (var agingGroupJson in results) {
        // Extract invoices data
        final invoicesJson = agingGroupJson['data'] as List<dynamic>;
        final invoices = <DuesInvoice>[];

        // Process each invoice in this aging group
        for (var invoiceJson in invoicesJson) {
          // Convert JSON to model and then to entity
          final invoiceModel = DuesInvoiceModel.fromJson(
            invoiceJson,
            agingGroupJson['aging'] ?? '',
            customerId,
          );

          // Add to invoices list
          invoices.add(invoiceModel.toEntity());
        }

        // Create aging group entity
        final agingGroup = DuesAgingGroup(
          invoices: invoices,
          totalPayableAmount:
              (agingGroupJson['totalPayableAmount'] ?? 0).toDouble(),
          aging: agingGroupJson['aging'] ?? '',
          dueDays: agingGroupJson['dueDays'] ?? 0,
        );

        // Add to aging groups list
        agingGroups.add(agingGroup);

        // Add to total due
        totalDue += agingGroup.totalPayableAmount;
      }

      // Create and return the complete dues summary
      return DuesSummary(
        agingGroups: agingGroups,
        customerId: customerId,
        totalDue: totalDue,
      );
    } else {
      logger.e('Error fetching sales orders: ${response.statusCode}');
      throw ServerException();
    }
  }
}
