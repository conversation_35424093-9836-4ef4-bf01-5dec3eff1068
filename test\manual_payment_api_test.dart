import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';

/// Manual test for payment API endpoints
/// This test can be run to verify the payment API is working correctly
/// Run with: flutter test test/manual_payment_api_test.dart
void main() {
  group('Manual Payment API Tests', () {
    late Dio dio;

    setUpAll(() {
      dio = Dio();
      dio.options.baseUrl =
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // Add logging to see requests/responses
      dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          logPrint: (obj) => print(obj),
        ),
      );
    });

    test('API Server Connectivity', () async {
      try {
        final response = await dio.get('/');
        print('✅ API Server is accessible');
        print('   Status Code: ${response.statusCode}');
        expect(response.statusCode, equals(200));
      } catch (e) {
        print('❌ API Server connectivity issue: $e');
        // Don't fail if it's a network issue
        if (e.toString().contains('SocketException')) {
          print(
            '⚠️  Network issue detected, this is expected in some environments',
          );
          return;
        }
        rethrow;
      }
    });

    test('Create Payment Link API', () async {
      final paymentData = {
        'amount': 100.0,
        'currency': 'INR',
        'description': 'Test payment for API validation',
        'customer_email': '<EMAIL>',
        'redirect_url': 'https://example.com/callback',
        'meta_data': [
          {'key': 'source', 'value': 'flutter_app'},
          {
            'key': 'test_id',
            'value': 'manual_test_${DateTime.now().millisecondsSinceEpoch}',
          },
        ],
        'send_email': true,
        'send_sms': false,
        'partial_payments': false,
      };

      try {
        final response = await dio.post(
          '/api/zoho/payments/create-link',
          data: paymentData,
        );

        print('✅ Payment Link API Response:');
        print('   Status Code: ${response.statusCode}');
        print('   Response Data: ${response.data}');

        expect(
          response.statusCode,
          equals(201),
        ); // 201 Created is correct for payment link creation

        if (response.data is Map) {
          final data = response.data as Map<String, dynamic>;
          if (data['success'] == true && data['data'] != null) {
            final paymentData = data['data'] as Map<String, dynamic>;
            print('✅ Payment Link Created Successfully:');
            print('   Payment Link ID: ${paymentData['payment_link_id']}');
            print('   Payment URL: ${paymentData['payment_link_url']}');
            print(
              '   Amount: ${paymentData['amount']} ${paymentData['currency']}',
            );

            expect(paymentData['payment_link_id'], isNotEmpty);
            expect(paymentData['payment_link_url'], isNotEmpty);
            expect(paymentData['amount'], equals(100.0));
          } else {
            print('⚠️  Payment Link Creation Response: ${data['message']}');
          }
        }
      } catch (e) {
        print('❌ Payment Link Creation Failed: $e');
        if (e is DioException) {
          print('   Status Code: ${e.response?.statusCode}');
          print('   Response Data: ${e.response?.data}');
        }
        // Don't fail if it's a network issue
        if (e.toString().contains('SocketException') ||
            e.toString().contains('TimeoutException')) {
          print('⚠️  Network issue detected, skipping test');
          return;
        }
        rethrow;
      }
    }, timeout: const Timeout(Duration(minutes: 2)));

    test('Create Payment Session API', () async {
      final sessionData = {
        'amount': 250.0,
        'currency': 'INR',
        'description': 'Test payment session for API validation',
        'customer_email': '<EMAIL>',
        'redirect_url': 'https://example.com/session-callback',
        'invoiceNo': 'SESSION-INV-${DateTime.now().millisecondsSinceEpoch}',
        'customerId': 'test_customer_session_123',
        'meta_data': [
          {'key': 'source', 'value': 'flutter_app'},
          {'key': 'test_type', 'value': 'session_test'},
        ],
        'send_email': false,
        'send_sms': false,
      };

      try {
        final response = await dio.post(
          '/api/zoho/payments/create-session',
          data: sessionData,
        );

        print('✅ Payment Session API Response:');
        print('   Status Code: ${response.statusCode}');
        print('   Response Data: ${response.data}');

        expect(response.statusCode, equals(200));

        if (response.data is Map) {
          final data = response.data as Map<String, dynamic>;
          if (data['success'] == true && data['data'] != null) {
            final sessionData = data['data'] as Map<String, dynamic>;
            print('✅ Payment Session Created Successfully:');
            print('   Session ID: ${sessionData['payment_link_id']}');
            print('   Session URL: ${sessionData['payment_link_url']}');

            expect(sessionData['payment_link_id'], isNotEmpty);
            expect(sessionData['payment_link_url'], isNotEmpty);
          }
        }
      } catch (e) {
        print('❌ Payment Session Creation Failed: $e');
        if (e is DioException) {
          print('   Status Code: ${e.response?.statusCode}');
          print('   Response Data: ${e.response?.data}');
        }
        if (e.toString().contains('SocketException') ||
            e.toString().contains('TimeoutException')) {
          print('⚠️  Network issue detected, skipping test');
          return;
        }
        rethrow;
      }
    }, timeout: const Timeout(Duration(minutes: 2)));

    test('Create Flutter Payment API', () async {
      final flutterData = {
        'amount': 500.0,
        'currency': 'INR',
        'description': 'Test Flutter payment for API validation',
        'customerEmail': '<EMAIL>',
        'callbackUrl': 'https://example.com/flutter-callback',
        'businessName': 'AquaPartner',
      };

      try {
        final response = await dio.post(
          '/api/flutter/payment/initiate',
          data: flutterData,
        );

        print('✅ Flutter Payment API Response:');
        print('   Status Code: ${response.statusCode}');
        print('   Response Data: ${response.data}');

        expect(response.statusCode, equals(200));

        if (response.data is Map) {
          final data = response.data as Map<String, dynamic>;
          if (data['success'] == true && data['data'] != null) {
            final paymentData = data['data'] as Map<String, dynamic>;
            print('✅ Flutter Payment Created Successfully:');
            print('   Payment ID: ${paymentData['payment_session_id']}');
            print('   WebView URL: ${paymentData['webview_url']}');

            expect(paymentData['payment_session_id'], isNotEmpty);
            expect(paymentData['webview_url'], isNotEmpty);
          }
        }
      } catch (e) {
        print('❌ Flutter Payment Creation Failed: $e');
        if (e is DioException) {
          print('   Status Code: ${e.response?.statusCode}');
          print('   Response Data: ${e.response?.data}');
        }
        if (e.toString().contains('SocketException') ||
            e.toString().contains('TimeoutException')) {
          print('⚠️  Network issue detected, skipping test');
          return;
        }
        rethrow;
      }
    }, timeout: const Timeout(Duration(minutes: 2)));

    test('Payment Status API', () async {
      const testPaymentLinkId = 'test_payment_link_id_123';

      try {
        final response = await dio.get(
          '/api/payment/status/$testPaymentLinkId',
        );

        print('✅ Payment Status API Response:');
        print('   Status Code: ${response.statusCode}');
        print('   Response Data: ${response.data}');

        // For non-existent payment, we expect either 404 or error response
        if (response.statusCode == 200) {
          print('✅ Payment Status Retrieved Successfully');
        }
      } catch (e) {
        print('ℹ️  Payment Status Request Result: $e');
        if (e is DioException) {
          print('   Status Code: ${e.response?.statusCode}');
          print('   Response Data: ${e.response?.data}');
          // This is expected for non-existent payment ID
          expect(e.response?.statusCode, anyOf([404, 400, 500]));
        }
      }
    }, timeout: const Timeout(Duration(minutes: 1)));

    test(
      'Invalid Payment Request Handling',
      () async {
        final invalidData = {
          'amount': -100.0, // Invalid negative amount
          'currency': 'INVALID',
          'description': '', // Empty description
          'customer_email': 'invalid-email', // Invalid email format
          'redirect_url': 'not-a-url',
        };

        try {
          final response = await dio.post(
            '/api/zoho/payments/create-link',
            data: invalidData,
          );

          print('⚠️  Unexpected success for invalid request:');
          print('   Status Code: ${response.statusCode}');
          print('   Response Data: ${response.data}');

          // If we get a response, check if it indicates failure
          if (response.data is Map) {
            final data = response.data as Map<String, dynamic>;
            if (data['success'] == false) {
              print('✅ Invalid Request Properly Rejected');
              expect(data['success'], false);
            }
          }
        } catch (e) {
          print('✅ Invalid Request Handled Correctly: $e');
          if (e is DioException) {
            print('   Status Code: ${e.response?.statusCode}');
            print('   Response Data: ${e.response?.data}');
            // Expect 4xx status code for bad request
            expect(e.response?.statusCode, anyOf([400, 422, 500]));
          }
        }
      },
      timeout: const Timeout(Duration(minutes: 1)),
    );
  });
}
