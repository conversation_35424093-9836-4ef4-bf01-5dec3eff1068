import 'package:get_it/get_it.dart';
import '../core/services/payment_service.dart';
import '../core/services/payment_completion_service.dart';
import '../data/datasources/remote/payment_remote_datasource.dart';
import '../data/repositories/payment_repository_impl.dart';
import '../domain/repositories/payment_repository.dart';
import '../domain/usecases/payment/create_payment_link_usecase.dart';
import '../domain/usecases/payment/parse_payment_result_usecase.dart';
import '../domain/usecases/payment/validate_payment_url_usecase.dart';
import '../domain/usecases/payments/verify_transaction_usecase.dart';
import '../presentation/cubit/payment/payment_cubit.dart';

/// Dependency injection setup for payment-related services
void initPaymentDependencies(GetIt sl) {
  // Data Sources
  sl.registerLazySingleton<PaymentRemoteDataSource>(
    () => PaymentRemoteDataSourceImpl(apiClient: sl(), logger: sl()),
  );

  // Repositories
  sl.registerLazySingleton<PaymentRepository>(
    () => PaymentRepositoryImpl(remoteDataSource: sl(), logger: sl()),
  );

  // Use Cases
  sl.registerLazySingleton(() => CreatePaymentLinkUseCase(sl()));
  sl.registerLazySingleton(() => ValidatePaymentUrlUseCase(sl()));
  sl.registerLazySingleton(() => ParsePaymentResultUseCase(sl()));
  sl.registerLazySingleton(() => VerifyTransaction(sl()));
  sl.registerLazySingleton(() => ForceRefreshTransaction(sl()));

  // Services
  sl.registerLazySingleton<PaymentService>(
    () => PaymentService(
      createPaymentLinkUseCase: sl(),
      validatePaymentUrlUseCase: sl(),
      parsePaymentResultUseCase: sl(),
      logger: sl(),
    ),
  );

  sl.registerLazySingleton<PaymentCompletionService>(
    () => PaymentCompletionService(
      verifyTransaction: sl(),
      forceRefreshTransaction: sl(),
      logger: sl(),
    ),
  );

  // Cubits (Factory registration for multiple instances)
  sl.registerFactory<PaymentCubit>(
    () => PaymentCubit(
      paymentService: sl(),
      logger: sl(),
      analyticsService: sl(),
    ),
  );
}
