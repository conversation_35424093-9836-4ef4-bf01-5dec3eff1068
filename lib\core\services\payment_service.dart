import 'package:dartz/dartz.dart';
import '../error/failures.dart';
import '../utils/logger.dart';
import '../../domain/entities/payments/payment_link.dart';
import '../../domain/entities/payments/payment_request.dart';
import '../../domain/entities/payments/payment_result.dart';
import '../../domain/usecases/payment/create_payment_link_usecase.dart';
import '../../domain/usecases/payment/parse_payment_result_usecase.dart';
import '../../domain/usecases/payment/validate_payment_url_usecase.dart';

/// Service class for payment operations
/// This service acts as a facade for payment-related use cases
class PaymentService {
  final CreatePaymentLinkUseCase createPaymentLinkUseCase;
  final ValidatePaymentUrlUseCase validatePaymentUrlUseCase;
  final ParsePaymentResultUseCase parsePaymentResultUseCase;
  final AppLogger logger;

  PaymentService({
    required this.createPaymentLinkUseCase,
    required this.validatePaymentUrlUseCase,
    required this.parsePaymentResultUseCase,
    required this.logger,
  });

  /// Create a payment link for the given request
  /// Returns [PaymentLink] on success or [Failure] on error
  Future<Either<Failure, PaymentLink>> createPaymentLink(PaymentRequest request) async {
    try {
      logger.i('Creating payment link for amount: ${request.amount}');
      
      // Validate the request first
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        logger.e('Payment request validation failed: ${validationErrors.join(', ')}');
        return Left(ValidationFailure(validationErrors.join(', ')));
      }

      // Create the payment link
      final result = await createPaymentLinkUseCase(request);
      
      result.fold(
        (failure) => logger.e('Failed to create payment link: $failure'),
        (paymentLink) => logger.i('Payment link created successfully: ${paymentLink.paymentLinkId}'),
      );

      return result;
    } catch (e) {
      logger.e('Unexpected error in createPaymentLink: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  /// Validate if a URL is safe for payment processing
  /// Returns true if the URL is valid and safe
  Future<Either<Failure, bool>> validatePaymentUrl(String url) async {
    try {
      logger.i('Validating payment URL: $url');
      
      if (url.trim().isEmpty) {
        logger.e('Payment URL is empty');
        return Left(ValidationFailure('Payment URL cannot be empty'));
      }

      final result = await validatePaymentUrlUseCase(url);
      
      result.fold(
        (failure) => logger.e('Payment URL validation failed: $failure'),
        (isValid) => logger.i('Payment URL validation result: $isValid'),
      );

      return result;
    } catch (e) {
      logger.e('Unexpected error in validatePaymentUrl: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  /// Parse payment result from callback URL
  /// Returns [PaymentResult] with the parsed information
  Future<Either<Failure, PaymentResult>> parsePaymentResult(String callbackUrl) async {
    try {
      logger.i('Parsing payment result from URL: $callbackUrl');
      
      if (callbackUrl.trim().isEmpty) {
        logger.e('Callback URL is empty');
        return Left(ValidationFailure('Callback URL cannot be empty'));
      }

      final result = await parsePaymentResultUseCase(callbackUrl);
      
      result.fold(
        (failure) => logger.e('Failed to parse payment result: $failure'),
        (paymentResult) => logger.i('Payment result parsed: ${paymentResult.status}'),
      );

      return result;
    } catch (e) {
      logger.e('Unexpected error in parsePaymentResult: $e');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  /// Check if a URL indicates payment completion
  /// This is a utility method for quick URL checking
  bool isPaymentCompletionUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.path.contains('/payment/success') || 
             uri.path.contains('/payment/failed');
    } catch (e) {
      logger.e('Error checking payment completion URL: $e');
      return false;
    }
  }

  /// Get user-friendly error message from failure
  String getErrorMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ValidationFailure:
        return (failure as ValidationFailure).message;
      case NetworkFailure:
        return 'Network error. Please check your internet connection and try again.';
      case ServerFailure:
        return 'Server error. Please try again later.';
      case AuthFailure:
        return 'Authentication error. Please login again.';
      case UnexpectedFailure:
        return 'An unexpected error occurred. Please try again.';
      default:
        return 'An error occurred. Please try again.';
    }
  }

  /// Create a payment request with default values
  PaymentRequest createPaymentRequest({
    required double amount,
    required String description,
    required String customerEmail,
    String? invoiceNumber,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? referenceId,
    Map<String, String>? metadata,
    String currency = 'INR',
    bool sendEmail = true,
    bool sendSms = false,
  }) {
    return PaymentRequest(
      amount: amount,
      description: description,
      customerEmail: customerEmail,
      currency: currency,
      invoiceNumber: invoiceNumber,
      customerId: customerId,
      customerName: customerName,
      customerPhone: customerPhone,
      referenceId: referenceId,
      metadata: metadata,
      sendEmail: sendEmail,
      sendSms: sendSms,
    );
  }

  /// Log payment analytics event
  void logPaymentEvent({
    required String eventName,
    Map<String, dynamic>? parameters,
  }) {
    try {
      logger.i('Payment event: $eventName');
      if (parameters != null) {
        logger.i('Payment event parameters: $parameters');
      }
      // Here you could integrate with analytics service
      // analyticsService.logEvent(name: eventName, parameters: parameters);
    } catch (e) {
      logger.e('Error logging payment event: $e');
    }
  }
}
